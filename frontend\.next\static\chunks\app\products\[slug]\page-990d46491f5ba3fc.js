(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[221],{722:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(3464).A.create({baseURL:"",withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json",Accept:"application/json"}});r.interceptors.request.use(e=>{var t;console.log("Making request to: ".concat(null==(t=e.method)?void 0:t.toUpperCase()," ").concat(e.url));let a=localStorage.getItem("token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&419===e.response.status&&console.error("CSRF token mismatch. Refreshing token..."),Promise.reject(e)));let s=r},2699:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(5155),s=a(2115),o=a(5695),i=a(6874),n=a.n(i),c=a(8258);function l(){let e=(0,o.useParams)().slug,[t,a]=(0,s.useState)(null),[i,l]=(0,s.useState)(1),[d,u]=(0,s.useState)(!0),[m,g]=(0,s.useState)(null);return((0,s.useEffect)(()=>{let t=async()=>{try{u(!0);let t=await c.bk.getProductBySlug(e);a(t),g(null)}catch(e){console.error("Error fetching product:",e),g("Failed to load product. Please try again later.")}finally{u(!1)}};e&&t()},[e]),d)?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):m||!t?(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 my-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:m||"Product not found"})})]})}):(0,r.jsx)("div",{className:"bg-white",children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto py-8 px-4 sm:px-6 lg:max-w-7xl lg:px-8",children:(0,r.jsxs)("div",{className:"lg:grid lg:grid-cols-2 lg:gap-x-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsx)("div",{className:"aspect-w-1 aspect-h-1 rounded-lg overflow-hidden",children:t.image?(0,r.jsx)("img",{src:t.image,alt:t.name,className:"w-full h-full object-center object-cover"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gray-100",children:(0,r.jsx)("span",{className:"text-gray-400",children:"No image"})})})}),(0,r.jsxs)("div",{className:"mt-10 lg:mt-0 lg:col-span-1",children:[(0,r.jsx)("h1",{className:"text-3xl font-extrabold tracking-tight text-gray-900",children:t.name}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)("p",{className:"text-3xl text-gray-900",children:["$",t.price.toFixed(2)]})}),t.category&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h3",{className:"text-sm text-gray-600",children:"Category"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(n(),{href:"/categories/".concat(t.category.slug),className:"text-indigo-600 hover:text-indigo-500",children:t.category.name})})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h3",{className:"text-sm text-gray-600",children:"Description"}),(0,r.jsx)("div",{className:"mt-2 prose prose-sm text-gray-500",children:(0,r.jsx)("p",{children:t.description})})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("h3",{className:"text-sm text-gray-600",children:"Availability"}),(0,r.jsx)("div",{className:"ml-2",children:t.quantity>0?(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["In Stock (",t.quantity," available)"]}):(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"Out of Stock"})})]})}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("h3",{className:"text-sm text-gray-600 mr-3",children:"Quantity"}),(0,r.jsx)("input",{type:"number",min:"1",max:t.quantity,value:i,onChange:e=>{let a=parseInt(e.target.value);a>0&&t&&a<=t.quantity&&l(a)},disabled:t.quantity<=0,className:"shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-20 sm:text-sm border-gray-300 rounded-md"})]})}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)("button",{onClick:()=>{t&&(c.mx.addToCart(t,i),window.dispatchEvent(new Event("storage")),alert("Product added to cart!"))},disabled:t.quantity<=0,className:"w-full inline-flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white ".concat(t.quantity>0?"bg-indigo-600 hover:bg-indigo-700":"bg-gray-400 cursor-not-allowed"),children:"Add to Cart"})})]})]})})})}},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},5769:(e,t,a)=>{Promise.resolve().then(a.bind(a,2699))},8258:(e,t,a)=>{"use strict";a.d(t,{uR:()=>o,mx:()=>u,M$:()=>n,QE:()=>c,bk:()=>i,d$:()=>m});var r=a(722);let s={login:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/login",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Login error details:",e),e}},register:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/register",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Registration error details:",e),e}},logout:async()=>{try{await r.A.get("/sanctum/csrf-cookie"),await r.A.post("/api/logout"),localStorage.removeItem("token"),localStorage.removeItem("user")}catch(e){console.error("Logout error:",e),localStorage.removeItem("token"),localStorage.removeItem("user")}},getCurrentUser:()=>{let e=localStorage.getItem("user");return e?JSON.parse(e):null},isAuthenticated:()=>!!localStorage.getItem("token"),isEmailVerified:()=>{let e=localStorage.getItem("user");return!!e&&!!JSON.parse(e).email_verified_at},resendVerificationEmail:async()=>{try{if(!s.isAuthenticated())throw Error("You must be logged in to resend verification email");console.log("Attempting to resend verification email...");let e=await r.A.get("/sanctum/csrf-cookie");console.log("CSRF cookie response:",e.status),console.log("Sending verification email request...");let t=await r.A.post("/api/email/verification-notification");return console.log("Verification email response:",t.status,t.data),t.data}catch(e){throw console.error("Resend verification email error:",e),e}},refreshUserData:async()=>{try{if(!s.isAuthenticated())return null;let e=await r.A.get("/api/user");if(console.log("Refresh user data response:",e.data),e.data&&e.data.success&&e.data.data&&e.data.data.user){let t=e.data.data.user;return localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),t}return null}catch(e){return console.error("Refresh user data error:",e),null}}},o=s,i={fetchProducts:async()=>{try{return(await r.A.get("/products")).data}catch(e){throw e}},getFeaturedProducts:async()=>{try{return(await r.A.get("/products?featured=true")).data}catch(e){throw e}},getProductBySlug:async e=>{try{let t=(await r.A.get("/products")).data.data.find(t=>t.slug===e);if(!t)throw Error("Product not found");return t}catch(e){throw e}},getAllProducts:async()=>{try{return(await r.A.get("/products")).data.data}catch(e){throw e}}},n={getAllCategories:async()=>{try{return(await r.A.get("/categories")).data.data}catch(e){throw e}},getCategoryBySlug:async e=>{try{let t=(await r.A.get("/categories")).data.data.find(t=>t.slug===e);if(!t)throw Error("Category not found");return t}catch(e){throw e}}},c={getAllOrders:async()=>{try{return(await r.A.get("/orders")).data.data}catch(e){throw e}},getOrderById:async e=>{try{return(await r.A.get("/orders/".concat(e))).data.data}catch(e){throw e}},createOrder:async e=>{try{return(await r.A.post("/orders",e)).data.data}catch(e){throw e}},updateOrder:async(e,t)=>{try{return(await r.A.put("/orders/".concat(e),{notes:t})).data.data}catch(e){throw e}},cancelOrder:async e=>{try{return(await r.A.post("/orders/".concat(e,"/cancel"))).data.data}catch(e){throw e}}},l="ecommerce_cart",d={getCart:()=>{let e=localStorage.getItem(l);return e?JSON.parse(e):{items:[],total:0}},saveCart:e=>{localStorage.setItem(l,JSON.stringify(e))},addToCart:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=d.getCart(),r=a.items.findIndex(t=>t.product_id===e.id);return -1!==r?a.items[r].quantity+=t:a.items.push({product_id:e.id,quantity:t,product:e}),a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(a),a},updateQuantity:(e,t)=>{let a=d.getCart(),r=a.items.findIndex(t=>t.product_id===e);return -1!==r&&(t<=0?a.items.splice(r,1):a.items[r].quantity=t,a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(a)),a},removeFromCart:e=>{let t=d.getCart(),a=t.items.findIndex(t=>t.product_id===e);return -1!==a&&(t.items.splice(a,1),t.total=t.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(t)),t},clearCart:()=>{let e={items:[],total:0};return d.saveCart(e),e},getItemCount:()=>d.getCart().items.reduce((e,t)=>e+t.quantity,0)},u=d,m={createPaymentIntent:async e=>{try{return(await r.A.post("/payment/create-intent",e)).data}catch(e){throw e}},processPayment:async(e,t)=>{try{return(await r.A.post("/payment/process",{payment_method_id:e,items:t})).data}catch(e){throw e}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,441,684,358],()=>t(5769)),_N_E=e.O()}]);