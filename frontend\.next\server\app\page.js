(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8327:(e,t,r)=>{"use strict";var a,n,o=Object.create,i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,d=e=>{throw TypeError(e)},h=(e,t,r,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of l(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(a=s(t,n))||a.enumerable});return e},f=(e,t,r)=>(r=null!=e?o(u(e)):{},h(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)),m=(e,t,r)=>t.has(e)||d("Cannot "+r),p=(e,t,r)=>(m(e,t,"read from private field"),r?r.call(e):t.get(e)),y=(e,t,r)=>t.has(e)?d("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),v={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(v,{HydratedRouter:()=>t5,RouterProvider:()=>t1}),e.exports=h(i({},"__esModule",{value:!0}),v);var g=f(r(43210)),w=f(r(51215)),b="popstate";function E(e,t){if(!1===e||null==e)throw Error(t)}function x(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw Error(t)}catch(e){}}}function R(e,t){return{usr:e.state,key:e.key,idx:t}}function S(e,t,r=null,a){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?P(t):t,state:r,key:t&&t.key||a||Math.random().toString(36).substring(2,10)}}function C({pathname:e="/",search:t="",hash:r=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),r&&"#"!==r&&(e+="#"===r.charAt(0)?r:"#"+r),e}function P(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substring(a),e=e.substring(0,a)),e&&(t.pathname=e)}return t}var _=class{constructor(e){if(y(this,a,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(p(this,a).has(e))return p(this,a).get(e);if(void 0!==e.defaultValue)return e.defaultValue;throw Error("No value found for context")}set(e,t){p(this,a).set(e,t)}};a=new WeakMap;var L=new Set(["lazy","caseSensitive","path","id","index","children"]),k=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function j(e,t,r=[],a={}){return e.map((e,n)=>{let o=[...r,String(n)],i="string"==typeof e.id?e.id:o.join("-");if(E(!0!==e.index||!e.children,"Cannot specify children on an index route"),E(!a[i],`Found a route id collision on id "${i}".  Route id's must be globally unique within Data Router usages`),!0===e.index){let r={...e,...t(e),id:i};return a[i]=r,r}{let r={...e,...t(e),id:i,children:void 0};return a[i]=r,e.children&&(r.children=j(e.children,t,o,a)),r}})}function T(e,t,r="/"){return N(e,t,r,!1)}function N(e,t,r,a){let n=A(("string"==typeof t?P(t):t).pathname||"/",r);if(null==n)return null;let o=function e(t,r=[],a=[],n=""){let o=(t,o,i)=>{var s,l;let u,c,d={relativePath:void 0===i?t.path||"":i,caseSensitive:!0===t.caseSensitive,childrenIndex:o,route:t};d.relativePath.startsWith("/")&&(E(d.relativePath.startsWith(n),`Absolute route path "${d.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),d.relativePath=d.relativePath.slice(n.length));let h=H([n,d.relativePath]),f=a.concat(d);t.children&&t.children.length>0&&(E(!0!==t.index,`Index routes must not have child routes. Please remove all child routes from route path "${h}".`),e(t.children,r,f,h)),(null!=t.path||t.index)&&r.push({path:h,score:(s=h,l=t.index,c=(u=s.split("/")).length,u.some(O)&&(c+=-2),l&&(c+=2),u.filter(e=>!O(e)).reduce((e,t)=>e+(M.test(t)?3:""===t?1:10),c)),routesMeta:f})};return t.forEach((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let r of function e(t){let r=t.split("/");if(0===r.length)return[];let[a,...n]=r,o=a.endsWith("?"),i=a.replace(/\?$/,"");if(0===n.length)return o?[i,""]:[i];let s=e(n.join("/")),l=[];return l.push(...s.map(e=>""===e?i:[i,e].join("/"))),o&&l.push(...s),l.map(e=>t.startsWith("/")&&""===e?"/":e)}(e.path))o(e,t,r);else o(e,t)}),r}(e);o.sort((e,t)=>{var r,a;return e.score!==t.score?t.score-e.score:(r=e.routesMeta.map(e=>e.childrenIndex),a=t.routesMeta.map(e=>e.childrenIndex),r.length===a.length&&r.slice(0,-1).every((e,t)=>e===a[t])?r[r.length-1]-a[a.length-1]:0)});let i=null;for(let e=0;null==i&&e<o.length;++e){let t=function(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return x(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}(n);i=function(e,t,r=!1){let{routesMeta:a}=e,n={},o="/",i=[];for(let e=0;e<a.length;++e){let s=a[e],l=e===a.length-1,u="/"===o?t:t.slice(o.length)||"/",c=$({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},u),d=s.route;if(!c&&l&&r&&!a[a.length-1].route.index&&(c=$({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},u)),!c)return null;Object.assign(n,c.params),i.push({params:n,pathname:H([o,c.pathname]),pathnameBase:U(H([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=H([o,c.pathnameBase]))}return i}(o[e],t,a)}return i}var M=/^:[\w-]+$/,O=e=>"*"===e;function $(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=D(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let o=n[0],i=o.replace(/(.)\/+$/,"$1"),s=n.slice(1);return{params:a.reduce((e,{paramName:t,isOptional:r},a)=>{if("*"===t){let e=s[a]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}let n=s[a];return r&&!n?e[t]=void 0:e[t]=(n||"").replace(/%2F/g,"/"),e},{}),pathname:o,pathnameBase:i,pattern:e}}function D(e,t=!1,r=!0){x("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,r)=>(a.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":""!==e&&"/"!==e&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),a]}function A(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&"/"!==a?null:e.slice(r)||"/"}function F(e,t,r,a){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(a)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function I(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}var H=e=>e.join("/").replace(/\/\/+/g,"/"),U=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),z=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",B=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"",Y=class{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}},q=(e,t=302)=>{let r=t;"number"==typeof r?r={status:r}:void 0===r.status&&(r.status=302);let a=new Headers(r.headers);return a.set("Location",e),new Response(null,{...r,headers:a})},W=class{constructor(e,t,r,a=!1){this.status=e,this.statusText=t||"",this.internal=a,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function J(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}var V=["POST","PUT","PATCH","DELETE"],X=new Set(V),G=new Set(["GET",...V]),K=new Set([301,302,303,307,308]),Z=new Set([307,308]),Q={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ee={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},et={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},er=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ea=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),en="remix-router-transitions",eo=Symbol("ResetLoaderData");function ei(e,t,r,a,n,o){let i,s,l;if(n){for(let e of(i=[],t))if(i.push(e),e.route.id===n){s=e;break}}else i=t,s=t[t.length-1];let u=function(e,t,r,a=!1){let n,o;"string"==typeof e?n=P(e):(E(!(n={...e}).pathname||!n.pathname.includes("?"),F("?","pathname","search",n)),E(!n.pathname||!n.pathname.includes("#"),F("#","pathname","hash",n)),E(!n.search||!n.search.includes("#"),F("#","search","hash",n)));let i=""===e||""===n.pathname,s=i?"/":n.pathname;if(null==s)o=r;else{let e=t.length-1;if(!a&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;n.pathname=t.join("/")}o=e>=0?t[e]:"/"}let l=function(e,t="/"){var r;let a,{pathname:n,search:o="",hash:i=""}="string"==typeof e?P(e):e;return{pathname:n?n.startsWith("/")?n:(r=n,a=t.replace(/\/+$/,"").split("/"),r.split("/").forEach(e=>{".."===e?a.length>1&&a.pop():"."!==e&&a.push(e)}),a.length>1?a.join("/"):"/"):t,search:z(o),hash:B(i)}}(n,o),u=s&&"/"!==s&&s.endsWith("/"),c=(i||"."===s)&&r.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}(a||".",(l=I(i)).map((e,t)=>t===l.length-1?e.pathname:e.pathnameBase),A(e.pathname,r)||e.pathname,"path"===o);if(null==a&&(u.search=e.search,u.hash=e.hash),(null==a||""===a||"."===a)&&s){let e=ez(u.search);if(s.route.index&&!e)u.search=u.search?u.search.replace(/^\?/,"?index&"):"?index";else if(!s.route.index&&e){let e=new URLSearchParams(u.search),t=e.getAll("index");e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let r=e.toString();u.search=r?`?${r}`:""}}return"/"!==r&&(u.pathname="/"===u.pathname?r:H([r,u.pathname])),C(u)}function es(e,t,r){var a;let n,o;if(!r||!(null!=r&&("formData"in r&&null!=r.formData||"body"in r&&void 0!==r.body)))return{path:t};if(r.formMethod&&(a=r.formMethod,!G.has(a.toUpperCase())))return{path:t,error:eO(405,{method:r.formMethod})};let i=()=>({path:t,error:eO(400,{type:"invalid-body"})}),s=(r.formMethod||"get").toUpperCase(),l=eD(t);if(void 0!==r.body){if("text/plain"===r.formEncType){if(!eU(s))return i();let e="string"==typeof r.body?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((e,[t,r])=>`${e}${t}=${r}
`,""):String(r.body);return{path:t,submission:{formMethod:s,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:e}}}else if("application/json"===r.formEncType){if(!eU(s))return i();try{let e="string"==typeof r.body?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:s,formAction:l,formEncType:r.formEncType,formData:void 0,json:e,text:void 0}}}catch(e){return i()}}}if(E("function"==typeof FormData,"FormData is not available in this environment"),r.formData)n=e_(r.formData),o=r.formData;else if(r.body instanceof FormData)n=e_(r.body),o=r.body;else if(r.body instanceof URLSearchParams)o=eL(n=r.body);else if(null==r.body)n=new URLSearchParams,o=new FormData;else try{n=new URLSearchParams(r.body),o=eL(n)}catch(e){return i()}let u={formMethod:s,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:o,json:void 0,text:void 0};if(eU(u.formMethod))return{path:t,submission:u};let c=P(t);return e&&c.search&&ez(c.search)&&n.append("index",""),c.search=`?${n}`,{path:C(c),submission:u}}function el(e,t,r,a,n,o,i,s,l,u,c,d,h,f,m,p,y,v,g){let w,b=g?eA(g[1])?g[1].error:g[1].data:void 0,E=n.createURL(o.location),x=n.createURL(l);if(c&&o.errors){let e=Object.keys(o.errors)[0];w=i.findIndex(t=>t.route.id===e)}else if(g&&eA(g[1])){let e=g[0];w=i.findIndex(t=>t.route.id===e)-1}let R=g?g[1].statusCode:void 0,S=R&&R>=400,C={currentUrl:E,currentParams:o.matches[0]?.params||{},nextUrl:x,nextParams:i[0].params,...s,actionResult:b,actionStatus:R},P=i.map((n,i)=>{var s,l,h,f,m;let p,y,v,{route:g}=n,b=null;if(null!=w&&i>w?b=!1:g.lazy?b=!0:null==g.loader?b=!1:c?b=eu(g,o.loaderData,o.errors):(s=o.loaderData,l=o.matches[i],h=n,p=!l||h.route.id!==l.route.id,y=!s.hasOwnProperty(h.route.id),(p||y)&&(b=!0)),null!==b)return eb(r,a,e,n,u,t,b);let R=!S&&(d||E.pathname+E.search===x.pathname+x.search||E.search!==x.search||(f=o.matches[i],m=n,v=f.route.path,f.pathname!==m.pathname||null!=v&&v.endsWith("*")&&f.params["*"]!==m.params["*"])),P={...C,defaultShouldRevalidate:R},_=ec(n,P);return eb(r,a,e,n,u,t,_,P)}),_=[];return m.forEach((e,s)=>{if(c||!i.some(t=>t.route.id===e.routeId)||f.has(s))return;let l=T(y,e.path,v);if(!l)return void _.push({key:s,routeId:e.routeId,path:e.path,matches:null,match:null,request:null,controller:null});if(p.has(s))return;let m=o.fetchers.get(s),g=eB(l,e.path),w=new AbortController,b=eP(n,e.path,w.signal),E=null;if(h.has(s))h.delete(s),E=eE(r,a,b,l,g,u,t);else if(m&&"idle"!==m.state&&void 0===m.data)d&&(E=eE(r,a,b,l,g,u,t));else{let e={...C,defaultShouldRevalidate:!S&&d};ec(g,e)&&(E=eE(r,a,b,l,g,u,t,e))}E&&_.push({key:s,routeId:e.routeId,path:e.path,matches:E,match:g,request:b,controller:w})}),{dsMatches:P,revalidatingFetchers:_}}function eu(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let a=null!=t&&void 0!==t[e.id],n=null!=r&&void 0!==r[e.id];return(!!a||!n)&&("function"==typeof e.loader&&!0===e.loader.hydrate||!a&&!n)}function ec(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if("boolean"==typeof r)return r}return t.defaultShouldRevalidate}function ed(e,t,r,a,n){let o;if(e){let t=a[e];E(t,`No route found to patch children into: routeId = ${e}`),t.children||(t.children=[]),o=t.children}else o=r;let i=j(t.filter(e=>!o.some(t=>(function e(t,r){return"id"in t&&"id"in r&&t.id===r.id||t.index===r.index&&t.path===r.path&&t.caseSensitive===r.caseSensitive&&((!t.children||0===t.children.length)&&(!r.children||0===r.children.length)||t.children.every((t,a)=>r.children?.some(r=>e(t,r))))})(e,t))),n,[e||"_","patch",String(o?.length||"0")],a);o.push(...i)}var eh=new WeakMap,ef=({key:e,route:t,manifest:r,mapRouteProperties:a})=>{let n=r[t.id];if(E(n,"No route found in manifest"),!n.lazy||"object"!=typeof n.lazy)return;let o=n.lazy[e];if(!o)return;let i=eh.get(n);i||(i={},eh.set(n,i));let s=i[e];if(s)return s;let l=(async()=>{let t=L.has(e),r=void 0!==n[e]&&"hasErrorBoundary"!==e;if(t)x(!t,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),i[e]=Promise.resolve();else if(r)x(!1,`Route "${n.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let t=await o();null!=t&&(Object.assign(n,{[e]:t}),Object.assign(n,a(n)))}"object"==typeof n.lazy&&(n.lazy[e]=void 0,Object.values(n.lazy).every(e=>void 0===e)&&(n.lazy=void 0))})();return i[e]=l,l},em=new WeakMap;async function ep(e){let t=e.matches.filter(e=>e.shouldLoad),r={};return(await Promise.all(t.map(e=>e.resolve()))).forEach((e,a)=>{r[t[a].route.id]=e}),r}async function ey(e){return e.matches.some(e=>e.route.unstable_middleware)?ev(e,!1,()=>ep(e),(e,t)=>({[t]:{type:"error",result:e}})):ep(e)}async function ev(e,t,r,a){let{matches:n,request:o,params:i,context:s}=e,l={handlerResult:void 0};try{let e=n.flatMap(e=>e.route.unstable_middleware?e.route.unstable_middleware.map(t=>[e.route.id,t]):[]),a=await eg({request:o,params:i,context:s},e,t,l,r);return t?a:l.handlerResult}catch(r){if(!l.middlewareError)throw r;let e=await a(l.middlewareError.error,l.middlewareError.routeId);if(t||!l.handlerResult)return e;return Object.assign(l.handlerResult,e)}}async function eg(e,t,r,a,n,o=0){let i,{request:s}=e;if(s.signal.aborted){if(s.signal.reason)throw s.signal.reason;throw Error(`Request aborted without an \`AbortSignal.reason\`: ${s.method} ${s.url}`)}let l=t[o];if(!l)return a.handlerResult=await n(),a.handlerResult;let[u,c]=l,d=!1,h=async()=>{if(d)throw Error("You may only call `next()` once per middleware");d=!0;let s=await eg(e,t,r,a,n,o+1);if(r)return i=s};try{let t=await c({request:e.request,params:e.params,context:e.context},h);if(!d)return h();if(void 0===t)return i;return t}catch(e){throw a.middlewareError?a.middlewareError.error!==e&&(a.middlewareError={routeId:u,error:e}):a.middlewareError={routeId:u,error:e},e}}function ew(e,t,r,a,n){let o=ef({key:"unstable_middleware",route:a.route,manifest:t,mapRouteProperties:e}),i=function(e,t,r,a,n){let o,i=r[e.id];if(E(i,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if("function"==typeof e.lazy){let t=em.get(i);if(t)return{lazyRoutePromise:t,lazyHandlerPromise:t};let r=(async()=>{E("function"==typeof e.lazy,"No lazy route function found");let t=await e.lazy(),r={};for(let e in t){let a=t[e];if(void 0===a)continue;let n=k.has(e),o=void 0!==i[e]&&"hasErrorBoundary"!==e;n?x(!n,"Route property "+e+" is not a supported property to be returned from a lazy route function. This property will be ignored."):o?x(!o,`Route "${i.id}" has a static property "${e}" defined but its lazy function is also returning a value for this property. The lazy route property "${e}" will be ignored.`):r[e]=a}Object.assign(i,r),Object.assign(i,{...a(i),lazy:void 0})})();return em.set(i,r),r.catch(()=>{}),{lazyRoutePromise:r,lazyHandlerPromise:r}}let s=Object.keys(e.lazy),l=[];for(let i of s){if(n&&n.includes(i))continue;let s=ef({key:i,route:e,manifest:r,mapRouteProperties:a});s&&(l.push(s),i===t&&(o=s))}let u=l.length>0?Promise.all(l).then(()=>{}):void 0;return u?.catch(()=>{}),o?.catch(()=>{}),{lazyRoutePromise:u,lazyHandlerPromise:o}}(a.route,eU(r.method)?"action":"loader",t,e,n);return{middleware:o,route:i.lazyRoutePromise,handler:i.lazyHandlerPromise}}function eb(e,t,r,a,n,o,i,s=null){let l=!1,u=ew(e,t,r,a,n);return{...a,_lazyPromises:u,shouldLoad:i,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler:e=>(l=!0,s)?"boolean"==typeof e?ec(a,{...s,defaultShouldRevalidate:e}):ec(a,s):i,resolve:e=>l||i||e&&"GET"===r.method&&(a.route.lazy||a.route.loader)?eR({request:r,match:a,lazyHandlerPromise:u?.handler,lazyRoutePromise:u?.route,handlerOverride:e,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}function eE(e,t,r,a,n,o,i,s=null){return a.map(a=>a.route.id!==n.route.id?{...a,shouldLoad:!1,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler:()=>!1,_lazyPromises:ew(e,t,r,a,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:eb(e,t,r,a,o,i,!0,s))}async function ex(e,t,r,a,n,o){r.some(e=>e._lazyPromises?.middleware)&&await Promise.all(r.map(e=>e._lazyPromises?.middleware));let i={request:t,params:r[0].params,context:n,matches:r},s=o?()=>{throw Error("You cannot call `unstable_runClientMiddleware()` from a static handler `dataStrategy`. Middleware is run outside of `dataStrategy` during SSR in order to bubble up the Response.  You can enable middleware via the `respond` API in `query`/`queryRoute`")}:e=>ev(i,!1,()=>e({...i,fetcherKey:a,unstable_runClientMiddleware:()=>{throw Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(e,t)=>({[t]:{type:"error",result:e}})),l=await e({...i,fetcherKey:a,unstable_runClientMiddleware:s});try{await Promise.all(r.flatMap(e=>[e._lazyPromises?.handler,e._lazyPromises?.route]))}catch(e){}return l}async function eR({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:a,handlerOverride:n,scopedContext:o}){let i,s,l=eU(e.method),u=l?"action":"loader",c=r=>{let a,i=new Promise((e,t)=>a=t);s=()=>a(),e.signal.addEventListener("abort",s);let l=a=>"function"!=typeof r?Promise.reject(Error(`You cannot call the handler for a route which defines a boolean "${u}" [routeId: ${t.route.id}]`)):r({request:e,params:t.params,context:o},...void 0!==a?[a]:[]);return Promise.race([(async()=>{try{let e=await (n?n(e=>l(e)):l());return{type:"data",result:e}}catch(e){return{type:"error",result:e}}})(),i])};try{let n=l?t.route.action:t.route.loader;if(r||a)if(n){let e,[t]=await Promise.all([c(n).catch(t=>{e=t}),r,a]);if(void 0!==e)throw e;i=t}else{await r;let n=l?t.route.action:t.route.loader;if(n)[i]=await Promise.all([c(n),a]);else{if("action"!==u)return{type:"data",result:void 0};let r=new URL(e.url),a=r.pathname+r.search;throw eO(405,{method:e.method,pathname:a,routeId:t.route.id})}}else if(n)i=await c(n);else{let t=new URL(e.url),r=t.pathname+t.search;throw eO(404,{pathname:r})}}catch(e){return{type:"error",result:e}}finally{s&&e.signal.removeEventListener("abort",s)}return i}async function eS(e){let{result:t,type:r}=e;if(eH(t)){let e;try{let r=t.headers.get("Content-Type");e=r&&/\bapplication\/json\b/.test(r)?null==t.body?null:await t.json():await t.text()}catch(e){return{type:"error",error:e}}return"error"===r?{type:"error",error:new W(t.status,t.statusText,e),statusCode:t.status,headers:t.headers}:{type:"data",data:e,statusCode:t.status,headers:t.headers}}if("error"===r)return eI(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new W(t.init?.status||500,void 0,t.data),statusCode:J(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:J(t)?t.status:void 0};return eI(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function eC(e,t,r){if(er.test(e)){let a=new URL(e.startsWith("//")?t.protocol+e:e),n=null!=A(a.pathname,r);if(a.origin===t.origin&&n)return a.pathname+a.search+a.hash}return e}function eP(e,t,r,a){let n=e.createURL(eD(t)).toString(),o={signal:r};if(a&&eU(a.formMethod)){let{formMethod:e,formEncType:t}=a;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(a.json)):"text/plain"===t?o.body=a.text:"application/x-www-form-urlencoded"===t&&a.formData?o.body=e_(a.formData):o.body=a.formData}return new Request(n,o)}function e_(e){let t=new URLSearchParams;for(let[r,a]of e.entries())t.append(r,"string"==typeof a?a:a.name);return t}function eL(e){let t=new FormData;for(let[r,a]of e.entries())t.append(r,a);return t}function ek(e,t,r,a,n,o){let{loaderData:i,errors:s}=function(e,t,r,a=!1,n=!1){let o,i={},s=null,l=!1,u={},c=r&&eA(r[1])?r[1].error:void 0;return e.forEach(r=>{if(!(r.route.id in t))return;let d=r.route.id,h=t[d];if(E(!eF(h),"Cannot handle redirect results in processLoaderData"),eA(h)){let t=h.error;if(void 0!==c&&(t=c,c=void 0),s=s||{},n)s[d]=t;else{let r=eN(e,d);null==s[r.route.id]&&(s[r.route.id]=t)}a||(i[d]=eo),l||(l=!0,o=J(h.error)?h.error.status:500),h.headers&&(u[d]=h.headers)}else i[d]=h.data,h.statusCode&&200!==h.statusCode&&!l&&(o=h.statusCode),h.headers&&(u[d]=h.headers)}),void 0!==c&&r&&(s={[r[0]]:c},i[r[0]]=void 0),{loaderData:i,errors:s,statusCode:o||200,loaderHeaders:u}}(t,r,a);return n.filter(e=>!e.matches||e.matches.some(e=>e.shouldLoad)).forEach(t=>{let{key:r,match:a,controller:n}=t,i=o[r];if(E(i,"Did not find corresponding fetcher result"),!n||!n.signal.aborted)if(eA(i)){let t=eN(e.matches,a?.route.id);s&&s[t.route.id]||(s={...s,[t.route.id]:i.error}),e.fetchers.delete(r)}else if(eF(i))E(!1,"Unhandled fetcher revalidation redirect");else{let t=eJ(i.data);e.fetchers.set(r,t)}}),{loaderData:i,errors:s}}function ej(e,t,r,a){let n=Object.entries(t).filter(([,e])=>e!==eo).reduce((e,[t,r])=>(e[t]=r,e),{});for(let o of r){let r=o.route.id;if(!t.hasOwnProperty(r)&&e.hasOwnProperty(r)&&o.route.loader&&(n[r]=e[r]),a&&a.hasOwnProperty(r))break}return n}function eT(e){return e?eA(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function eN(e,t){return(t?e.slice(0,e.findIndex(e=>e.route.id===t)+1):[...e]).reverse().find(e=>!0===e.route.hasErrorBoundary)||e[0]}function eM(e){let t=1===e.length?e[0]:e.find(e=>e.index||!e.path||"/"===e.path)||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function eO(e,{pathname:t,routeId:r,method:a,type:n,message:o}={}){let i="Unknown Server Error",s="Unknown @remix-run/router error";return 400===e?(i="Bad Request",a&&t&&r?s=`You made a ${a} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:"invalid-body"===n&&(s="Unable to encode submission body")):403===e?(i="Forbidden",s=`Route "${r}" does not match URL "${t}"`):404===e?(i="Not Found",s=`No route matches URL "${t}"`):405===e&&(i="Method Not Allowed",a&&t&&r?s=`You made a ${a.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:a&&(s=`Invalid request method "${a.toUpperCase()}"`)),new W(e||500,i,Error(s),!0)}function e$(e){let t=Object.entries(e);for(let e=t.length-1;e>=0;e--){let[r,a]=t[e];if(eF(a))return{key:r,result:a}}}function eD(e){return C({..."string"==typeof e?P(e):e,hash:""})}function eA(e){return"error"===e.type}function eF(e){return"redirect"===(e&&e.type)}function eI(e){return"object"==typeof e&&null!=e&&"type"in e&&"data"in e&&"init"in e&&"DataWithResponseInit"===e.type}function eH(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"object"==typeof e.headers&&void 0!==e.body}function eU(e){return X.has(e.toUpperCase())}function ez(e){return new URLSearchParams(e).getAll("index").some(e=>""===e)}function eB(e,t){let r="string"==typeof t?P(t).search:t.search;if(e[e.length-1].route.index&&ez(r||""))return e[e.length-1];let a=I(e);return a[a.length-1]}function eY(e){let{formMethod:t,formAction:r,formEncType:a,text:n,formData:o,json:i}=e;if(t&&r&&a){if(null!=n)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:void 0,text:n};else if(null!=o)return{formMethod:t,formAction:r,formEncType:a,formData:o,json:void 0,text:void 0};else if(void 0!==i)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:i,text:void 0}}}function eq(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function eW(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function eJ(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}var eV=f(r(43210)),eX=f(r(43210)),eG=eX.createContext(null);eG.displayName="DataRouter";var eK=eX.createContext(null);eK.displayName="DataRouterState";var eZ=eX.createContext({isTransitioning:!1});eZ.displayName="ViewTransition";var eQ=eX.createContext(new Map);eQ.displayName="Fetchers",eX.createContext(null).displayName="Await";var e0=eX.createContext(null);e0.displayName="Navigation";var e1=eX.createContext(null);e1.displayName="Location";var e2=eX.createContext({outlet:null,matches:[],isDataRoute:!1});e2.displayName="Route";var e4=eX.createContext(null);e4.displayName="RouteError";var e3=f(r(43210));function e5(){return null!=e3.useContext(e1)}e3.createContext(null);var e9=e3.createElement(function(){let e=te(),t=J(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",n={padding:"2px 4px",backgroundColor:a},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=e3.createElement(e3.Fragment,null,e3.createElement("p",null,"\uD83D\uDCBF Hey developer \uD83D\uDC4B"),e3.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",e3.createElement("code",{style:n},"ErrorBoundary")," or"," ",e3.createElement("code",{style:n},"errorElement")," prop on your route.")),e3.createElement(e3.Fragment,null,e3.createElement("h2",null,"Unexpected Application Error!"),e3.createElement("h3",{style:{fontStyle:"italic"}},t),r?e3.createElement("pre",{style:{padding:"0.5rem",backgroundColor:a}},r):null,o)},null),e8=class extends e3.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?e3.createElement(e2.Provider,{value:this.props.routeContext},e3.createElement(e4.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function e7({routeContext:e,match:t,children:r}){let a=e3.useContext(eG);return a&&a.static&&a.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=t.route.id),e3.createElement(e2.Provider,{value:e},r)}function e6(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function te(){var e,t,r;let a,n,o,i,s=e3.useContext(e4),l=(e="useRouteError",E(a=e3.useContext(eK),e6(e)),a),u=(i=(r=t="useRouteError",E(n=e3.useContext(e2),e6(r)),o=n).matches[o.matches.length-1],E(i.route.id,`${t} can only be used on routes that contain a unique "id"`),i.route.id);return void 0!==s?s:l.errors?.[u]}var tt={};function tr(e,t,r){t||tt[e]||(tt[e]=!0,x(!1,r))}var ta={};function tn(e,t){e||ta[t]||(ta[t]=!0,console.warn(t))}function to(e){let t={hasErrorBoundary:e.hasErrorBoundary||null!=e.ErrorBoundary||null!=e.errorElement};return e.Component&&(e.element&&x(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:eV.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&x(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:eV.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&x(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:eV.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var ti=["HydrateFallback","hydrateFallbackElement"],ts=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}})}};function tl({router:e,flushSync:t}){let[r,a]=eV.useState(e.state),[n,o]=eV.useState(),[i,s]=eV.useState({isTransitioning:!1}),[l,u]=eV.useState(),[c,d]=eV.useState(),[h,f]=eV.useState(),m=eV.useRef(new Map),p=eV.useCallback((r,{deletedFetchers:n,flushSync:i,viewTransitionOpts:h})=>{r.fetchers.forEach((e,t)=>{void 0!==e.data&&m.current.set(t,e.data)}),n.forEach(e=>m.current.delete(e)),tn(!1===i||null!=t,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let p=null!=e.window&&null!=e.window.document&&"function"==typeof e.window.document.startViewTransition;if(tn(null==h||p,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!h||!p)return void(t&&i?t(()=>a(r)):eV.startTransition(()=>a(r)));if(t&&i){t(()=>{c&&(l&&l.resolve(),c.skipTransition()),s({isTransitioning:!0,flushSync:!0,currentLocation:h.currentLocation,nextLocation:h.nextLocation})});let n=e.window.document.startViewTransition(()=>{t(()=>a(r))});n.finished.finally(()=>{t(()=>{u(void 0),d(void 0),o(void 0),s({isTransitioning:!1})})}),t(()=>d(n));return}c?(l&&l.resolve(),c.skipTransition(),f({state:r,currentLocation:h.currentLocation,nextLocation:h.nextLocation})):(o(r),s({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}))},[e.window,t,c,l]);eV.useLayoutEffect(()=>e.subscribe(p),[e,p]),eV.useEffect(()=>{i.isTransitioning&&!i.flushSync&&u(new ts)},[i]),eV.useEffect(()=>{if(l&&n&&e.window){let t=l.promise,r=e.window.document.startViewTransition(async()=>{eV.startTransition(()=>a(n)),await t});r.finished.finally(()=>{u(void 0),d(void 0),o(void 0),s({isTransitioning:!1})}),d(r)}},[n,l,e.window]),eV.useEffect(()=>{l&&n&&r.location.key===n.location.key&&l.resolve()},[l,c,r.location,n]),eV.useEffect(()=>{!i.isTransitioning&&h&&(o(h.state),s({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}),f(void 0))},[i.isTransitioning,h]);let y=eV.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:t=>e.navigate(t),push:(t,r,a)=>e.navigate(t,{state:r,preventScrollReset:a?.preventScrollReset}),replace:(t,r,a)=>e.navigate(t,{replace:!0,state:r,preventScrollReset:a?.preventScrollReset})}),[e]),v=e.basename||"/",g=eV.useMemo(()=>({router:e,navigator:y,static:!1,basename:v}),[e,y,v]);return eV.createElement(eV.Fragment,null,eV.createElement(eG.Provider,{value:g},eV.createElement(eK.Provider,{value:r},eV.createElement(eQ.Provider,{value:m.current},eV.createElement(eZ.Provider,{value:i},eV.createElement(tc,{basename:v,location:r.location,navigationType:r.historyAction,navigator:y},eV.createElement(tu,{routes:e.routes,future:e.future,state:r})))))),null)}var tu=eV.memo(function({routes:e,future:t,state:r}){return function(e,t,r,a){let n;E(e5(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o,static:i}=e3.useContext(e0),{matches:s}=e3.useContext(e2),l=s[s.length-1],u=l?l.params:{},c=l?l.pathname:"/",d=l?l.pathnameBase:"/",h=l&&l.route;{let e=h&&h.path||"";tr(c,!h||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${c}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let f=(E(e5(),"useLocation() may be used only in the context of a <Router> component."),e3.useContext(e1).location);if(t){let e="string"==typeof t?P(t):t;E("/"===d||e.pathname?.startsWith(d),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${d}" but pathname "${e.pathname}" was given in the \`location\` prop.`),n=e}else n=f;let m=n.pathname||"/",p=m;if("/"!==d){let e=d.replace(/^\//,"").split("/");p="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=!i&&r&&r.matches&&r.matches.length>0?r.matches:T(e,{pathname:p});x(h||null!=y,`No routes matched location "${n.pathname}${n.search}${n.hash}" `),x(null==y||void 0!==y[y.length-1].route.element||void 0!==y[y.length-1].route.Component||void 0!==y[y.length-1].route.lazy,`Matched leaf route at location "${n.pathname}${n.search}${n.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let v=function(e,t=[],r=null,a=null){if(null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(0!==t.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let n=e,o=r?.errors;if(null!=o){let e=n.findIndex(e=>e.route.id&&o?.[e.route.id]!==void 0);E(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),n=n.slice(0,Math.min(n.length,e+1))}let i=!1,s=-1;if(r)for(let e=0;e<n.length;e++){let t=n[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(s=e),t.route.id){let{loaderData:e,errors:a}=r,o=t.route.loader&&!e.hasOwnProperty(t.route.id)&&(!a||void 0===a[t.route.id]);if(t.route.lazy||o){i=!0,n=s>=0?n.slice(0,s+1):[n[0]];break}}}return n.reduceRight((e,a,l)=>{let u,c=!1,d=null,h=null;r&&(u=o&&a.route.id?o[a.route.id]:void 0,d=a.route.errorElement||e9,i&&(s<0&&0===l?(tr("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),c=!0,h=null):s===l&&(c=!0,h=a.route.hydrateFallbackElement||null)));let f=t.concat(n.slice(0,l+1)),m=()=>{let t;return t=u?d:c?h:a.route.Component?e3.createElement(a.route.Component,null):a.route.element?a.route.element:e,e3.createElement(e7,{match:a,routeContext:{outlet:e,matches:f,isDataRoute:null!=r},children:t})};return r&&(a.route.ErrorBoundary||a.route.errorElement||0===l)?e3.createElement(e8,{location:r.location,revalidation:r.revalidation,component:d,error:u,children:m(),routeContext:{outlet:null,matches:f,isDataRoute:!0}}):m()},null)}(y&&y.map(e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:H([d,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:H([d,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),s,r,a);return t&&v?e3.createElement(e1.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...n},navigationType:"POP"}},v):v}(e,void 0,r,t)});function tc({basename:e="/",children:t=null,location:r,navigationType:a="POP",navigator:n,static:o=!1}){E(!e5(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let i=e.replace(/^\/*/,"/"),s=eV.useMemo(()=>({basename:i,navigator:n,static:o,future:{}}),[i,n,o]);"string"==typeof r&&(r=P(r));let{pathname:l="/",search:u="",hash:c="",state:d=null,key:h="default"}=r,f=eV.useMemo(()=>{let e=A(l,i);return null==e?null:{location:{pathname:e,search:u,hash:c,state:d,key:h},navigationType:a}},[i,l,u,c,d,h,a]);return(x(null!=f,`<Router basename="${i}"> is not able to match the URL "${l}${u}${c}" because it does not start with the basename, so the <Router> won't render anything.`),null==f)?null:eV.createElement(e0.Provider,{value:s},eV.createElement(e1.Provider,{children:t,value:f}))}var td=f(r(43210));function th(e,t){if(!1===e||null==e)throw Error(t)}async function tf(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(t){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(t),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function tm(e){return e.css?e.css.map(e=>({rel:"stylesheet",href:e})):[]}async function tp(e){if(!e.css)return;let t=tm(e);await Promise.all(t.map(tv))}async function ty(e,t){if(!e.css&&!t.links||!function(){if(void 0!==n)return n;let e=document.createElement("link");return n=e.relList.supports("preload"),e=null,n}())return;let r=[];if(e.css&&r.push(...tm(e)),t.links&&r.push(...t.links()),0===r.length)return;let a=[];for(let e of r){var o;(null==(o=e)||"string"!=typeof o.page)&&"stylesheet"===e.rel&&a.push({...e,rel:"preload",as:"style"})}await Promise.all(a.map(tv))}async function tv(e){return new Promise(t=>{if(e.media&&!window.matchMedia(e.media).matches||document.querySelector(`link[rel="stylesheet"][href="${e.href}"]`))return t();let r=document.createElement("link");function a(){document.head.contains(r)&&document.head.removeChild(r)}Object.assign(r,e),r.onload=()=>{a(),t()},r.onerror=()=>{a(),t()},document.head.appendChild(r)})}f(r(43210));var tg=r(14791);async function tw(e){let t={signal:e.signal};if("GET"!==e.method){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}var tb=Symbol("SingleFetchRedirect"),tE=new Set([100,101,204,205]);async function tx(e,t,r){var a;let n,o=e.matches.find(e=>e.unstable_shouldCallHandler());th(o,"No action match found");let i=await o.resolve(async a=>await a(async()=>{let{data:a,status:i}=await t(e,r,[o.route.id]);return n=i,tL(a,o.route.id)}));return eH(i.result)||J(i.result)?{[o.route.id]:i}:{[o.route.id]:{type:i.type,result:new Y(i.result,"number"==typeof(a=n)?{status:a}:a)}}}async function tR(e,t,r,a){let n=e.matches.filter(e=>e.unstable_shouldCallHandler()),o={};return await Promise.all(n.map(n=>n.resolve(async i=>{try{let{hasClientLoader:s}=t(n),l=n.route.id,u=s?await i(async()=>{let{data:t}=await r(e,a,[l]);return tL(t,l)}):await i();o[n.route.id]={type:"data",result:u}}catch(e){o[n.route.id]={type:"error",result:e}}}))),o}async function tS(e,t,r,a,n,o){let i=new Set,s=!1,l=e.matches.map(()=>tk()),u=tk(),c={},d=Promise.all(e.matches.map(async(t,n)=>t.resolve(async d=>{l[n].resolve();let h=t.route.id,{hasLoader:f,hasClientLoader:m,hasShouldRevalidate:p}=r(t),y=!t.unstable_shouldRevalidateArgs||null==t.unstable_shouldRevalidateArgs.actionStatus||t.unstable_shouldRevalidateArgs.actionStatus<400;if(!t.unstable_shouldCallHandler(y)){s||(s=null!=t.unstable_shouldRevalidateArgs&&f&&!0===p);return}if(m){f&&(s=!0);try{let t=await d(async()=>{let{data:t}=await a(e,o,[h]);return tL(t,h)});c[h]={type:"data",result:t}}catch(e){c[h]={type:"error",result:e}}return}f&&i.add(h);try{let e=await d(async()=>{let e=await u.promise;return tL(e,h)});c[h]={type:"data",result:e}}catch(e){c[h]={type:"error",result:e}}})));if(await Promise.all(l.map(e=>e.promise)),t.state.initialized&&0!==i.size||window.__reactRouterHdrActive){let t=n&&s&&i.size>0?[...i.keys()]:void 0;try{let r=await a(e,o,t);u.resolve(r.data)}catch(e){u.reject(e)}}else u.resolve({routes:{}});return await d,c}async function tC(e,t,r){let a=e.matches.find(e=>e.unstable_shouldCallHandler());th(a,"No fetcher match found");let n=a.route.id,o=await a.resolve(async a=>a(async()=>{let{data:a}=await t(e,r,[n]);return tL(a,n)}));return{[a.route.id]:o}}async function tP(e,t,r){var a;let n,{request:o}=e,i=(a=o.url,"/"===(n="string"==typeof a?new URL(a,"undefined"==typeof window?"server://singlefetch/":window.location.origin):a).pathname?n.pathname="_root.data":t&&"/"===A(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n);"GET"===o.method&&(i=function(e){let t=e.searchParams.getAll("index");e.searchParams.delete("index");let r=[];for(let e of t)e&&r.push(e);for(let t of r)e.searchParams.append("index",t);return e}(i),r&&i.searchParams.set("_routes",r.join(",")));let s=await fetch(i,await tw(o));if(404===s.status&&!s.headers.has("X-Remix-Response"))throw new W(404,"Not Found",!0);if(204===s.status&&s.headers.has("X-Remix-Redirect"))return{status:202,data:{redirect:{redirect:s.headers.get("X-Remix-Redirect"),status:Number(s.headers.get("X-Remix-Status")||"302"),revalidate:"true"===s.headers.get("X-Remix-Revalidate"),reload:"true"===s.headers.get("X-Remix-Reload-Document"),replace:"true"===s.headers.get("X-Remix-Replace")}}};if(tE.has(s.status)){let e={};return r&&"GET"!==o.method&&(e[r[0]]={data:void 0}),{status:s.status,data:{routes:e}}}th(s.body,"No response body to decode");try{let e,t=await t_(s.body,window);if("GET"===o.method){let r=t.value;e=tb in r?{redirect:r[tb]}:{routes:r}}else{let a=t.value,n=r?.[0];th(n,"No routeId found for single fetch call decoding"),e="redirect"in a?{redirect:a}:{routes:{[n]:a}}}return{status:s.status,data:e}}catch(e){throw Error("Unable to decode turbo-stream response")}}function t_(e,t){return(0,tg.decode)(e,{plugins:[(e,...r)=>{if("SanitizedError"===e){let[e,a,n]=r,o=Error;e&&e in t&&"function"==typeof t[e]&&(o=t[e]);let i=new o(a);return i.stack=n,{value:i}}if("ErrorResponse"===e){let[e,t,a]=r;return{value:new W(t,a,e)}}return"SingleFetchRedirect"===e?{value:{[tb]:r[0]}}:"SingleFetchClassInstance"===e?{value:r[0]}:"SingleFetchFallback"===e?{value:void 0}:void 0}]})}function tL(e,t){if("redirect"in e){let{redirect:t,revalidate:r,reload:a,replace:n,status:o}=e.redirect;throw q(t,{status:o,headers:{...r?{"X-Remix-Revalidate":"yes"}:null,...a?{"X-Remix-Reload-Document":"yes"}:null,...n?{"X-Remix-Replace":"yes"}:null}})}let r=e.routes[t];if("error"in r)throw r.error;if("data"in r)return r.data;throw Error(`No response found for routeId "${t}"`)}function tk(){let e,t,r=new Promise((a,n)=>{e=async e=>{a(e);try{await r}catch(e){}},t=async e=>{n(e);try{await r}catch(e){}}});return{promise:r,resolve:e,reject:t}}var tj=f(r(43210)),tT=f(r(43210)),tN=f(r(43210)),tM=class extends tN.Component{constructor(e){super(e),this.state={error:e.error||null,location:e.location}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location?{error:e.error||null,location:e.location}:{error:e.error||t.error,location:t.location}}render(){return this.state.error?tN.createElement(tO,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}};function tO({error:e,isOutsideRemixApp:t}){let r;console.error(e);let a=tN.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "\u{1F4BF} Hey developer \u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information."
        );
      `}});return J(e)?tN.createElement(t$,{title:"Unhandled Thrown Response!"},tN.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),a):(r=e instanceof Error?e:Error(null==e?"Unknown Error":"object"==typeof e&&"toString"in e?e.toString():JSON.stringify(e)),tN.createElement(t$,{title:"Application Error!",isOutsideRemixApp:t},tN.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),tN.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},r.stack),a))}function t$({title:e,renderScripts:t,isOutsideRemixApp:r,children:a}){let{routeModules:n}=tZ();return n.root?.Layout&&!r?a:tN.createElement("html",{lang:"en"},tN.createElement("head",null,tN.createElement("meta",{charSet:"utf-8"}),tN.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),tN.createElement("title",null,e)),tN.createElement("body",null,tN.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},a,t?tN.createElement(t0,null):null)))}var tD=f(r(43210));function tA(){return tD.createElement(t$,{title:"Loading...",renderScripts:!0},tD.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "\u{1F4BF} Hey developer \u{1F44B}. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://remix.run/route/hydrate-fallback " +
                "for more information."
              );
            `}}))}function tF(e){let t={};return Object.values(e).forEach(e=>{if(e){let r=e.parentId||"";t[r]||(t[r]=[]),t[r].push(e)}}),t}function tI(e,t,r,a,n,o){return tz(t,r,a,n,o,"",tF(t),e)}function tH(e,t){if("loader"===e&&!t.hasLoader||"action"===e&&!t.hasAction){let r=`You are trying to call ${"action"===e?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;throw console.error(r),new W(400,"Bad Request",Error(r),!0)}}function tU(e,t){let r="clientAction"===e?"a":"an",a=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(a),new W(405,"Method Not Allowed",Error(a),!0)}function tz(e,t,r,a,n,o="",i=tF(e),s){return(i[o]||[]).map(o=>{let l=t[o.id];function u(e){return th("function"==typeof e,"No single fetch function available for route handler"),e()}function c(e){return o.hasLoader?u(e):Promise.resolve(null)}function d(e){if(!o.hasAction)throw tU("action",o.id);return u(e)}function h(e){e.clientActionModule&&import(e.clientActionModule),e.clientLoaderModule&&import(e.clientLoaderModule)}async function f(e){let r=t[o.id],a=r?ty(o,r):Promise.resolve();try{return e()}finally{await a}}let m={id:o.id,index:o.index,path:o.path};if(l){let e,t,i;Object.assign(m,{...m,...(e=tq(l),t=l.HydrateFallback&&(!n||"root"===o.id)?l.HydrateFallback:"root"===o.id?tA:void 0,i=l.ErrorBoundary?l.ErrorBoundary:"root"===o.id?()=>tT.createElement(tO,{error:te()}):void 0,"root"===o.id&&l.Layout?{...e?{element:tT.createElement(l.Layout,null,tT.createElement(e,null))}:{Component:e},...i?{errorElement:tT.createElement(l.Layout,null,tT.createElement(i,null))}:{ErrorBoundary:i},...t?{hydrateFallbackElement:tT.createElement(l.Layout,null,tT.createElement(t,null))}:{HydrateFallback:t}}:{Component:e,ErrorBoundary:i,HydrateFallback:t}),unstable_middleware:l.unstable_clientMiddleware,handle:l.handle,shouldRevalidate:tB(m.path,l,o,a,s)});let u=r&&r.loaderData&&o.id in r.loaderData,h=u?r?.loaderData?.[o.id]:void 0,p=r&&r.errors&&o.id in r.errors,y=p?r?.errors?.[o.id]:void 0,v=null==s&&(l.clientLoader?.hydrate===!0||!o.hasLoader);m.loader=async({request:e,params:t,context:r},a)=>{try{return await f(async()=>(th(l,"No `routeModule` available for critical-route loader"),l.clientLoader)?l.clientLoader({request:e,params:t,context:r,async serverLoader(){if(tH("loader",o),v){if(u)return h;if(p)throw y}return c(a)}}):c(a))}finally{v=!1}},m.loader.hydrate=tW(o.id,l.clientLoader,o.hasLoader,n),m.action=({request:e,params:t,context:r},a)=>f(async()=>{if(th(l,"No `routeModule` available for critical-route action"),!l.clientAction){if(n)throw tU("clientAction",o.id);return d(a)}return l.clientAction({request:e,params:t,context:r,serverAction:async()=>(tH("action",o),d(a))})})}else{let e;async function p(){return e||(e=(async()=>{(o.clientLoaderModule||o.clientActionModule)&&await new Promise(e=>setTimeout(e,0));let e=tY(o,t);return h(o),await e})()),await e}o.hasClientLoader||(m.loader=(e,t)=>f(()=>c(t))),o.hasClientAction||(m.action=(e,t)=>f(()=>{if(n)throw tU("clientAction",o.id);return d(t)})),m.lazy={loader:o.hasClientLoader?async()=>{let{clientLoader:e}=o.clientLoaderModule?await import(o.clientLoaderModule):await p();return th(e,"No `clientLoader` export found"),(t,r)=>e({...t,serverLoader:async()=>(tH("loader",o),c(r))})}:void 0,action:o.hasClientAction?async()=>{let e=o.clientActionModule?import(o.clientActionModule):p();h(o);let{clientAction:t}=await e;return th(t,"No `clientAction` export found"),(e,r)=>t({...e,serverAction:async()=>(tH("action",o),d(r))})}:void 0,unstable_middleware:o.hasClientMiddleware?async()=>{let{unstable_clientMiddleware:e}=o.clientMiddlewareModule?await import(o.clientMiddlewareModule):await p();return th(e,"No `unstable_clientMiddleware` export found"),e}:void 0,shouldRevalidate:async()=>{let e=await p();return tB(m.path,e,o,a,s)},handle:async()=>(await p()).handle,Component:async()=>(await p()).Component,ErrorBoundary:o.hasErrorBoundary?async()=>(await p()).ErrorBoundary:void 0}}let y=tz(e,t,r,a,n,o.id,i,s);return y.length>0&&(m.children=y),m})}function tB(e,t,r,a,n){if(n){var o,i,s;let e;return o=r.id,i=t.shouldRevalidate,s=n,e=!1,t=>e?i?i(t):t.defaultShouldRevalidate:(e=!0,s.has(o))}if(!a&&r.hasLoader&&!r.hasClientLoader){let r=e?D(e)[1].map(e=>e.paramName):[],a=e=>r.some(t=>e.currentParams[t]!==e.nextParams[t]);if(!t.shouldRevalidate)return e=>a(e);{let e=t.shouldRevalidate;return t=>e({...t,defaultShouldRevalidate:a(t)})}}if(a&&t.shouldRevalidate){let e=t.shouldRevalidate;return t=>e({...t,defaultShouldRevalidate:!0})}return t.shouldRevalidate}async function tY(e,t){let r=tf(e,t),a=tp(e),n=await r;return await Promise.all([a,ty(e,n)]),{Component:tq(n),ErrorBoundary:n.ErrorBoundary,unstable_clientMiddleware:n.unstable_clientMiddleware,clientAction:n.clientAction,clientLoader:n.clientLoader,handle:n.handle,links:n.links,meta:n.meta,shouldRevalidate:n.shouldRevalidate}}function tq(e){if(null!=e.default&&("object"!=typeof e.default||0!==Object.keys(e.default).length))return e.default}function tW(e,t,r,a){return a&&"root"!==e||null!=t&&(!0===t.hydrate||!0!==r)}var tJ=new Set,tV=new Set,tX="react-router-manifest-version";async function tG(e,t,r,a,n,o,i,s,l){let u,c=new URL(`${null!=i?i:"/"}/__manifest`.replace(/\/+/g,"/"),window.location.origin);if(e.sort().forEach(e=>c.searchParams.append("p",e)),c.searchParams.set("version",r.version),c.toString().length>7680)return void tJ.clear();try{let e=await fetch(c,{signal:l});if(e.ok){if(204===e.status&&e.headers.has("X-Remix-Reload-Document")){if(!t)return void console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");if(sessionStorage.getItem(tX)===r.version)return void console.error("Unable to discover routes due to manifest version mismatch.");throw sessionStorage.setItem(tX,r.version),window.location.href=t,Error("Detected manifest version mismatch, reloading...")}else if(e.status>=400)throw Error(await e.text())}else throw Error(`${e.status} ${e.statusText}`);sessionStorage.removeItem(tX),u=await e.json()}catch(e){if(l?.aborted)return;throw e}let d=new Set(Object.keys(r.routes)),h=Object.values(u).reduce((e,t)=>(t&&!d.has(t.id)&&(e[t.id]=t),e),{});Object.assign(r.routes,h),e.forEach(e=>(function(e,t){if(t.size>=1e3){let e=t.values().next().value;t.delete(e)}t.add(e)})(e,tV));let f=new Set;Object.values(h).forEach(e=>{!e||e.parentId&&h[e.parentId]||f.add(e.parentId)}),f.forEach(e=>s(e||null,tz(h,a,null,n,o,e)))}var tK=td.createContext(void 0);function tZ(){let e=td.useContext(tK);return th(e,"You must render this element inside a <HydratedRouter> element"),e}tK.displayName="FrameworkContext";var tQ=!1;function t0(e){var t,r;let a,n,{manifest:o,serverHandoffString:i,isSpaMode:s,ssr:l,renderMeta:u}=tZ(),{router:c,static:d,staticContext:h}=(th(a=td.useContext(eG),"You must render this element inside a <DataRouterContext.Provider> element"),a),{matches:f}=(th(n=td.useContext(eK),"You must render this element inside a <DataRouterStateContext.Provider> element"),n),m=!0===l;u&&(u.didRenderScripts=!0);let p=(t=f,r=0,s&&!tQ?[t[0]]:t);td.useEffect(()=>{tQ=!0},[]);let y=td.useMemo(()=>{let t=h?`window.__reactRouterContext = ${i};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",r=d?`${o.hmr?.runtime?`import ${JSON.stringify(o.hmr.runtime)};`:""}${!m?`import ${JSON.stringify(o.url)}`:""};
${p.map((e,t)=>{let r=`route${t}`,a=o.routes[e.route.id];th(a,`Route ${e.route.id} not found in manifest`);let{clientActionModule:n,clientLoaderModule:i,clientMiddlewareModule:s,hydrateFallbackModule:l,module:u}=a,c=[...n?[{module:n,varName:`${r}_clientAction`}]:[],...i?[{module:i,varName:`${r}_clientLoader`}]:[],...s?[{module:s,varName:`${r}_clientMiddleware`}]:[],...l?[{module:l,varName:`${r}_HydrateFallback`}]:[],{module:u,varName:`${r}_main`}];return 1===c.length?`import * as ${r} from ${JSON.stringify(u)};`:[c.map(e=>`import * as ${e.varName} from "${e.module}";`).join("\n"),`const ${r} = {${c.map(e=>`...${e.varName}`).join(",")}};`].join("\n")}).join("\n")}
  ${m?`window.__reactRouterManifest = ${JSON.stringify(function({sri:e,...t},r){let a=new Set(r.state.matches.map(e=>e.route.id)),n=r.state.location.pathname.split("/").filter(Boolean),o=["/"];for(n.pop();n.length>0;)o.push(`/${n.join("/")}`),n.pop();o.forEach(e=>{let t=T(r.routes,e,r.basename);t&&t.forEach(e=>a.add(e.route.id))});let i=[...a].reduce((e,r)=>Object.assign(e,{[r]:t.routes[r]}),{});return{...t,routes:i,sri:!!e||void 0}}(o,c),null,2)};`:""}
  window.__reactRouterRouteModules = {${p.map((e,t)=>`${JSON.stringify(e.route.id)}:route${t}`).join(",")}};

import(${JSON.stringify(o.entry.module)});`:" ";return td.createElement(td.Fragment,null,td.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:t},type:void 0}),td.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:r},type:"module",async:!0}))},[]),v=tQ?[]:[...new Set(o.entry.imports.concat(function(e,t,{includeHydrateFallback:r}={}){return[...new Set(e.map(e=>{let a=t.routes[e.route.id];if(!a)return[];let n=[a.module];return a.clientActionModule&&(n=n.concat(a.clientActionModule)),a.clientLoaderModule&&(n=n.concat(a.clientLoaderModule)),r&&a.hydrateFallbackModule&&(n=n.concat(a.hydrateFallbackModule)),a.imports&&(n=n.concat(a.imports)),n}).flat(1))]}(p,o,{includeHydrateFallback:!0})))],g="object"==typeof o.sri?o.sri:{};return tQ?null:td.createElement(td.Fragment,null,"object"==typeof o.sri?td.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:g})}}):null,m?null:td.createElement("link",{rel:"modulepreload",href:o.url,crossOrigin:e.crossOrigin,integrity:g[o.url],suppressHydrationWarning:!0}),td.createElement("link",{rel:"modulepreload",href:o.entry.module,crossOrigin:e.crossOrigin,integrity:g[o.entry.module],suppressHydrationWarning:!0}),v.map(t=>td.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:g[t],suppressHydrationWarning:!0})),y)}function t1(e){return g.createElement(tl,{flushSync:w.flushSync,...e})}var t2=f(r(43210)),t4=null,t3=null;function t5(e){var t,r,a,n,o;t3||(t3=function({unstable_getContext:e}){var t,r,a,n,o,i,s,l,u;let c,d;if(!t4&&window.__reactRouterContext&&window.__reactRouterManifest&&window.__reactRouterRouteModules){if(!0===window.__reactRouterManifest.sri){let e=document.querySelector("script[rr-importmap]");if(e?.textContent)try{window.__reactRouterManifest.sri=JSON.parse(e.textContent).integrity}catch(e){console.error("Failed to parse import map",e)}}t4={context:window.__reactRouterContext,manifest:window.__reactRouterManifest,routeModules:window.__reactRouterRouteModules,stateDecodingPromise:void 0,router:void 0,routerInitialized:!1}}if(!t4)throw Error("You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`");let h=t4;if(!t4.stateDecodingPromise){let e=t4.context.stream;E(e,"No stream found for single fetch decoding"),t4.context.stream=void 0,t4.stateDecodingPromise=t_(e,window).then(e=>{t4.context.state=e.value,h.stateDecodingPromise.value=!0}).catch(e=>{h.stateDecodingPromise.error=e})}if(t4.stateDecodingPromise.error)throw t4.stateDecodingPromise.error;if(!t4.stateDecodingPromise.value)throw t4.stateDecodingPromise;let f=tz(t4.manifest.routes,t4.routeModules,t4.context.state,t4.context.ssr,t4.context.isSpaMode);if(t4.context.isSpaMode){let{loaderData:e}=t4.context.state;t4.manifest.routes.root?.hasLoader&&e&&"root"in e&&(c={loaderData:{root:e.root}})}else(c=function(e,t,r,a,n,o){let i={...e,loaderData:{...e.loaderData}},s=T(t,a,n);if(s)for(let e of s){let t=e.route.id,a=r(t);tW(t,a.clientLoader,a.hasLoader,o)&&(a.hasHydrateFallback||!a.hasLoader)?delete i.loaderData[t]:a.hasLoader||(i.loaderData[t]=null)}return i}(t4.context.state,f,e=>({clientLoader:t4.routeModules[e]?.clientLoader,hasLoader:t4.manifest.routes[e]?.hasLoader===!0,hasHydrateFallback:t4.routeModules[e]?.HydrateFallback!=null}),window.location,window.__reactRouterContext?.basename,t4.context.isSpaMode))&&c.errors&&(c.errors=function(e){if(!e)return null;let t=Object.entries(e),r={};for(let[e,a]of t)if(a&&"RouteErrorResponse"===a.__type)r[e]=new W(a.status,a.statusText,a.data,!0===a.internal);else if(a&&"Error"===a.__type){if(a.__subType){let t=window[a.__subType];if("function"==typeof t)try{let n=new t(a.message);n.stack=a.stack,r[e]=n}catch(e){}}if(null==r[e]){let t=Error(a.message);t.stack=a.stack,r[e]=t}}else r[e]=a;return r}(c.errors));let m=function(e){let t,r,a,n,o,i=e.window?e.window:"undefined"!=typeof window?window:void 0,s=void 0!==i&&void 0!==i.document&&void 0!==i.document.createElement;E(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let l=e.hydrationRouteProperties||[],u=e.mapRouteProperties||ea,c={},d=j(e.routes,u,void 0,c),h=e.basename||"/",f=e.dataStrategy||ey,m={unstable_middleware:!1,...e.future},p=null,y=new Set,v=null,g=null,w=null,b=null!=e.hydrationData,R=T(d,e.history.location,h),C=!1,P=null;if(null==R&&!e.patchRoutesOnNavigation){let t=eO(404,{pathname:e.history.location.pathname}),{matches:r,route:a}=eM(d);R=r,P={[a.id]:t}}if(R&&!e.hydrationData&&e9(R,d,e.history.location.pathname).active&&(R=null),R)if(R.some(e=>e.route.lazy))r=!1;else if(R.some(e=>e.route.loader)){let t=e.hydrationData?e.hydrationData.loaderData:null,a=e.hydrationData?e.hydrationData.errors:null;if(a){let e=R.findIndex(e=>void 0!==a[e.route.id]);r=R.slice(0,e+1).every(e=>!eu(e.route,t,a))}else r=R.every(e=>!eu(e.route,t,a))}else r=!0;else{r=!1,R=[];let t=e9(null,d,e.history.location.pathname);t.active&&t.matches&&(C=!0,R=t.matches)}let L={historyAction:e.history.action,location:e.history.location,matches:R,initialized:r,navigation:Q,restoreScrollPosition:null==e.hydrationData&&null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||P,fetchers:new Map,blockers:new Map},k="POP",M=!1,O=!1,$=new Map,D=null,F=!1,I=!1,H=new Set,U=new Map,z=0,B=-1,Y=new Map,q=new Set,W=new Map,V=new Map,X=new Set,G=new Map,eo=null;function ec(e,t={}){L={...L,...e};let r=[],a=[];L.fetchers.forEach((e,t)=>{"idle"===e.state&&(X.has(t)?r.push(t):a.push(t))}),X.forEach(e=>{L.fetchers.has(e)||U.has(e)||r.push(e)}),[...y].forEach(e=>e(L,{deletedFetchers:r,viewTransitionOpts:t.viewTransitionOpts,flushSync:!0===t.flushSync})),r.forEach(e=>eX(e)),a.forEach(e=>L.fetchers.delete(e))}function eh(r,a,{flushSync:n}={}){let o,i,s=null!=L.actionData&&null!=L.navigation.formMethod&&eU(L.navigation.formMethod)&&"loading"===L.navigation.state&&r.state?._isRedirect!==!0;o=a.actionData?Object.keys(a.actionData).length>0?a.actionData:null:s?L.actionData:null;let l=a.loaderData?ej(L.loaderData,a.loaderData,a.matches||[],a.errors):L.loaderData,u=L.blockers;u.size>0&&(u=new Map(u)).forEach((e,t)=>u.set(t,et));let c=!0===M||null!=L.navigation.formMethod&&eU(L.navigation.formMethod)&&r.state?._isRedirect!==!0;if(t&&(d=t,t=void 0),F||"POP"===k||("PUSH"===k?e.history.push(r,r.state):"REPLACE"===k&&e.history.replace(r,r.state)),"POP"===k){let e=$.get(L.location.pathname);e&&e.has(r.pathname)?i={currentLocation:L.location,nextLocation:r}:$.has(r.pathname)&&(i={currentLocation:r,nextLocation:L.location})}else if(O){let e=$.get(L.location.pathname);e?e.add(r.pathname):(e=new Set([r.pathname]),$.set(L.location.pathname,e)),i={currentLocation:L.location,nextLocation:r}}ec({...a,actionData:o,loaderData:l,historyAction:k,location:r,initialized:!0,navigation:Q,revalidation:"idle",restoreScrollPosition:e5(r,a.matches||L.matches),preventScrollReset:c,blockers:u},{viewTransitionOpts:i,flushSync:!0===n}),k="POP",M=!1,O=!1,F=!1,I=!1,eo?.resolve(),eo=null}async function ef(t,r){if("number"==typeof t)return void e.history.go(t);let{path:a,submission:n,error:o}=es(!1,ei(L.location,L.matches,h,t,r?.fromRouteId,r?.relative),r),i=L.location,s=S(L.location,a,r&&r.state);s={...s,...e.history.encodeLocation(s)};let l=r&&null!=r.replace?r.replace:void 0,u="PUSH";!0===l?u="REPLACE":!1===l||null!=n&&eU(n.formMethod)&&n.formAction===L.location.pathname+L.location.search&&(u="REPLACE");let c=r&&"preventScrollReset"in r?!0===r.preventScrollReset:void 0,d=!0===(r&&r.flushSync),f=e2({currentLocation:i,nextLocation:s,historyAction:u});if(f)return void e1(f,{state:"blocked",location:s,proceed(){e1(f,{state:"proceeding",proceed:void 0,reset:void 0,location:s}),ef(t,r)},reset(){let e=new Map(L.blockers);e.set(f,et),ec({blockers:e})}});await em(u,s,{submission:n,pendingError:o,preventScrollReset:c,replace:r&&r.replace,enableViewTransition:r&&r.viewTransition,flushSync:d})}async function em(r,a,o){var i,s,l,u;let c;n&&n.abort(),n=null,k=r,F=!0===(o&&o.startUninterruptedRevalidation),i=L.location,s=L.matches,v&&w&&(v[e3(i,s)]=w()),M=!0===(o&&o.preventScrollReset),O=!0===(o&&o.enableViewTransition);let f=t||d,m=o&&o.overrideNavigation,p=o?.initialHydration&&L.matches&&L.matches.length>0&&!C?L.matches:T(f,a,h),y=!0===(o&&o.flushSync);if(p&&L.initialized&&!I&&(l=L.location,u=a,l.pathname===u.pathname&&l.search===u.search&&(""===l.hash?""!==u.hash:l.hash===u.hash||""!==u.hash||!1))&&!(o&&o.submission&&eU(o.submission.formMethod)))return void eh(a,{matches:p},{flushSync:y});let g=e9(p,f,a.pathname);if(g.active&&g.matches&&(p=g.matches),!p){let{error:e,notFoundMatches:t,route:r}=e4(a.pathname);eh(a,{matches:t,loaderData:{},errors:{[r.id]:e}},{flushSync:y});return}n=new AbortController;let b=eP(e.history,a,n.signal,o&&o.submission),E=new _(e.unstable_getContext?await e.unstable_getContext():void 0);if(o&&o.pendingError)c=[eN(p).route.id,{type:"error",error:o.pendingError}];else if(o&&o.submission&&eU(o.submission.formMethod)){let t=await ep(b,a,o.submission,p,E,g.active,o&&!0===o.initialHydration,{replace:o.replace,flushSync:y});if(t.shortCircuited)return;if(t.pendingActionResult){let[e,r]=t.pendingActionResult;if(eA(r)&&J(r.error)&&404===r.error.status){n=null,eh(a,{matches:t.matches,loaderData:{},errors:{[e]:r.error}});return}}p=t.matches||p,c=t.pendingActionResult,m=eq(a,o.submission),y=!1,g.active=!1,b=eP(e.history,b.url,b.signal)}let{shortCircuited:x,matches:R,loaderData:S,errors:P}=await ev(b,a,p,E,g.active,m,o&&o.submission,o&&o.fetcherSubmission,o&&o.replace,o&&!0===o.initialHydration,y,c);x||(n=null,eh(a,{matches:R||p,...eT(c),loaderData:S,errors:P}))}async function ep(e,t,r,a,n,o,i,s={}){var d;let f;if(eD(),ec({navigation:{state:"submitting",location:t,formMethod:(d=r).formMethod,formAction:d.formAction,formEncType:d.formEncType,formData:d.formData,json:d.json,text:d.text}},{flushSync:!0===s.flushSync}),o){let r=await e8(a,t.pathname,e.signal);if("aborted"===r.type)return{shortCircuited:!0};if("error"===r.type){let e=eN(r.partialMatches).route.id;return{matches:r.partialMatches,pendingActionResult:[e,{type:"error",error:r.error}]}}if(r.matches)a=r.matches;else{let{notFoundMatches:e,error:r,route:a}=e4(t.pathname);return{matches:e,pendingActionResult:[a.id,{type:"error",error:r}]}}}let m=eB(a,t);if(m.route.action||m.route.lazy){let t=eE(u,c,e,a,m,i?[]:l,n),r=await e_(e,t,n,null);if(!(f=r[m.route.id])){for(let e of a)if(r[e.route.id]){f=r[e.route.id];break}}if(e.signal.aborted)return{shortCircuited:!0}}else f={type:"error",error:eO(405,{method:e.method,pathname:t.pathname,routeId:m.route.id})};if(eF(f)){let t;return t=s&&null!=s.replace?s.replace:eC(f.response.headers.get("Location"),new URL(e.url),h)===L.location.pathname+L.location.search,await eR(e,f,!0,{submission:r,replace:t}),{shortCircuited:!0}}if(eA(f)){let e=eN(a,m.route.id);return!0!==(s&&s.replace)&&(k="PUSH"),{matches:a,pendingActionResult:[e.route.id,f]}}return{matches:a,pendingActionResult:[m.route.id,f]}}async function ev(r,a,o,i,s,f,m,p,y,v,g,w){let b=f||eq(a,m),E=m||p||eY(b),x=!F&&!v;if(s){if(x){let e=eg(w);ec({navigation:b,...void 0!==e?{actionData:e}:{}},{flushSync:g})}let e=await e8(o,a.pathname,r.signal);if("aborted"===e.type)return{shortCircuited:!0};if("error"===e.type){let t=eN(e.partialMatches).route.id;return{matches:e.partialMatches,loaderData:{},errors:{[t]:e.error}}}if(e.matches)o=e.matches;else{let{error:e,notFoundMatches:t,route:r}=e4(a.pathname);return{matches:t,loaderData:{},errors:{[r.id]:e}}}}let R=t||d,{dsMatches:S,revalidatingFetchers:C}=el(r,i,u,c,e.history,L,o,E,a,v?[]:l,!0===v,I,H,X,W,q,R,h,w);if(B=++z,!e.dataStrategy&&!S.some(e=>e.shouldLoad)&&0===C.length){let e=eZ();return eh(a,{matches:o,loaderData:{},errors:w&&eA(w[1])?{[w[0]]:w[1].error}:null,...eT(w),...e?{fetchers:new Map(L.fetchers)}:{}},{flushSync:g}),{shortCircuited:!0}}if(x){let e={};if(!s){e.navigation=b;let t=eg(w);void 0!==t&&(e.actionData=t)}C.length>0&&(C.forEach(e=>{let t=L.fetchers.get(e.key),r=eW(void 0,t?t.data:void 0);L.fetchers.set(e.key,r)}),e.fetchers=new Map(L.fetchers)),ec(e,{flushSync:g})}C.forEach(e=>{eG(e.key),e.controller&&U.set(e.key,e.controller)});let P=()=>C.forEach(e=>eG(e.key));n&&n.signal.addEventListener("abort",P);let{loaderResults:_,fetcherResults:k}=await eL(S,C,r,i);if(r.signal.aborted)return{shortCircuited:!0};n&&n.signal.removeEventListener("abort",P),C.forEach(e=>U.delete(e.key));let j=e$(_);if(j)return await eR(r,j.result,!0,{replace:y}),{shortCircuited:!0};if(j=e$(k))return q.add(j.key),await eR(r,j.result,!0,{replace:y}),{shortCircuited:!0};let{loaderData:T,errors:N}=ek(L,o,_,w,C,k);v&&L.errors&&(N={...L.errors,...N});let M=eZ(),O=eQ(B);return{matches:o,loaderData:T,errors:N,...M||O||C.length>0?{fetchers:new Map(L.fetchers)}:{}}}function eg(e){if(e&&!eA(e[1]))return{[e[0]]:e[1].data};if(L.actionData)if(0===Object.keys(L.actionData).length)return null;else return L.actionData}async function ew(r,a,o,i,s,f,m,p,y,v){var g,w;function b(e){if(!e.route.action&&!e.route.lazy){let e=eO(405,{method:v.formMethod,pathname:o,routeId:a});return ez(r,a,e,{flushSync:p}),!0}return!1}if(eD(),W.delete(r),!m&&b(i))return;let x=L.fetchers.get(r);eI(r,(g=v,w=x,{state:"submitting",formMethod:g.formMethod,formAction:g.formAction,formEncType:g.formEncType,formData:g.formData,json:g.json,text:g.text,data:w?w.data:void 0}),{flushSync:p});let R=new AbortController,S=eP(e.history,o,R.signal,v);if(m){let e=await e8(s,o,S.signal,r);if("aborted"===e.type)return;if("error"===e.type)return void ez(r,a,e.error,{flushSync:p});if(!e.matches)return void ez(r,a,eO(404,{pathname:o}),{flushSync:p});if(b(i=eB(s=e.matches,o)))return}U.set(r,R);let C=z,P=eE(u,c,S,s,i,l,f),_=(await e_(S,P,f,r))[i.route.id];if(S.signal.aborted){U.get(r)===R&&U.delete(r);return}if(X.has(r)){if(eF(_)||eA(_))return void eI(r,eJ(void 0))}else{if(eF(_))return(U.delete(r),B>C)?void eI(r,eJ(void 0)):(q.add(r),eI(r,eW(v)),eR(S,_,!1,{fetcherSubmission:v,preventScrollReset:y}));if(eA(_))return void ez(r,a,_.error)}let j=L.navigation.location||L.location,N=eP(e.history,j,R.signal),M=t||d,O="idle"!==L.navigation.state?T(M,L.navigation.location,h):L.matches;E(O,"Didn't find any matches after fetcher action");let $=++z;Y.set(r,$);let D=eW(v,_.data);L.fetchers.set(r,D);let{dsMatches:A,revalidatingFetchers:F}=el(N,f,u,c,e.history,L,O,v,j,l,!1,I,H,X,W,q,M,h,[i.route.id,_]);F.filter(e=>e.key!==r).forEach(e=>{let t=e.key,r=L.fetchers.get(t),a=eW(void 0,r?r.data:void 0);L.fetchers.set(t,a),eG(t),e.controller&&U.set(t,e.controller)}),ec({fetchers:new Map(L.fetchers)});let J=()=>F.forEach(e=>eG(e.key));R.signal.addEventListener("abort",J);let{loaderResults:V,fetcherResults:G}=await eL(A,F,N,f);if(R.signal.aborted)return;R.signal.removeEventListener("abort",J),Y.delete(r),U.delete(r),F.forEach(e=>U.delete(e.key));let K=e$(V);if(K)return eR(N,K.result,!1,{preventScrollReset:y});if(K=e$(G))return q.add(K.key),eR(N,K.result,!1,{preventScrollReset:y});let{loaderData:Z,errors:Q}=ek(L,O,V,void 0,F,G);if(L.fetchers.has(r)){let e=eJ(_.data);L.fetchers.set(r,e)}eQ($),"loading"===L.navigation.state&&$>B?(E(k,"Expected pending action"),n&&n.abort(),eh(L.navigation.location,{matches:O,loaderData:Z,errors:Q,fetchers:new Map(L.fetchers)})):(ec({errors:Q,loaderData:ej(L.loaderData,Z,O,Q),fetchers:new Map(L.fetchers)}),I=!1)}async function eb(t,r,a,n,o,i,s,d,h,f){let m=L.fetchers.get(t);eI(t,eW(f,m?m.data:void 0),{flushSync:d});let p=new AbortController,y=eP(e.history,a,p.signal);if(s){let e=await e8(o,a,y.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void ez(t,r,e.error,{flushSync:d});if(!e.matches)return void ez(t,r,eO(404,{pathname:a}),{flushSync:d});n=eB(o=e.matches,a)}U.set(t,p);let v=z,g=eE(u,c,y,o,n,l,i),w=(await e_(y,g,i,t))[n.route.id];if(U.get(t)===p&&U.delete(t),!y.signal.aborted){if(X.has(t))return void eI(t,eJ(void 0));if(eF(w))if(B>v)return void eI(t,eJ(void 0));else{q.add(t),await eR(y,w,!1,{preventScrollReset:h});return}if(eA(w))return void ez(t,r,w.error);eI(t,eJ(w.data))}}async function eR(t,r,a,{submission:o,fetcherSubmission:l,preventScrollReset:u,replace:c}={}){r.response.headers.has("X-Remix-Revalidate")&&(I=!0);let d=r.response.headers.get("Location");E(d,"Expected a Location header on the redirect Response"),d=eC(d,new URL(t.url),h);let f=S(L.location,d,{_isRedirect:!0});if(s){let t=!1;if(r.response.headers.has("X-Remix-Reload-Document"))t=!0;else if(er.test(d)){let r=e.history.createURL(d);t=r.origin!==i.location.origin||null==A(r.pathname,h)}if(t)return void(c?i.location.replace(d):i.location.assign(d))}n=null;let m=!0===c||r.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:p,formAction:y,formEncType:v}=L.navigation;!o&&!l&&p&&y&&v&&(o=eY(L.navigation));let g=o||l;if(Z.has(r.response.status)&&g&&eU(g.formMethod))await em(m,f,{submission:{...g,formAction:d},preventScrollReset:u||M,enableViewTransition:a?O:void 0});else{let e=eq(f,o);await em(m,f,{overrideNavigation:e,fetcherSubmission:l,preventScrollReset:u||M,enableViewTransition:a?O:void 0})}}async function e_(e,t,r,a){let n,o={};try{n=await ex(f,e,t,a,r,!1)}catch(e){return t.filter(e=>e.shouldLoad).forEach(t=>{o[t.route.id]={type:"error",error:e}}),o}for(let[r,a]of Object.entries(n)){var i;if(eH((i=a).result)&&K.has(i.result.status)){let n=a.result;o[r]={type:"redirect",response:function(e,t,r,a,n){let o=e.headers.get("Location");if(E(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!er.test(o)){let i=a.slice(0,a.findIndex(e=>e.route.id===r)+1);o=ei(new URL(t.url),i,n,o),e.headers.set("Location",o)}return e}(n,e,r,t,h)}}else o[r]=await eS(a)}return o}async function eL(e,t,r,a){let n=e_(r,e,a,null),o=Promise.all(t.map(async e=>{if(!e.matches||!e.match||!e.request||!e.controller)return Promise.resolve({[e.key]:{type:"error",error:eO(404,{pathname:e.path})}});{let t=(await e_(e.request,e.matches,a,e.key))[e.match.route.id];return{[e.key]:t}}}));return{loaderResults:await n,fetcherResults:(await o).reduce((e,t)=>Object.assign(e,t),{})}}function eD(){I=!0,W.forEach((e,t)=>{U.has(t)&&H.add(t),eG(t)})}function eI(e,t,r={}){L.fetchers.set(e,t),ec({fetchers:new Map(L.fetchers)},{flushSync:!0===(r&&r.flushSync)})}function ez(e,t,r,a={}){let n=eN(L.matches,t);eX(e),ec({errors:{[n.route.id]:r},fetchers:new Map(L.fetchers)},{flushSync:!0===(a&&a.flushSync)})}function eV(e){return V.set(e,(V.get(e)||0)+1),X.has(e)&&X.delete(e),L.fetchers.get(e)||ee}function eX(e){let t=L.fetchers.get(e);U.has(e)&&!(t&&"loading"===t.state&&Y.has(e))&&eG(e),W.delete(e),Y.delete(e),q.delete(e),X.delete(e),H.delete(e),L.fetchers.delete(e)}function eG(e){let t=U.get(e);t&&(t.abort(),U.delete(e))}function eK(e){for(let t of e){let e=eJ(eV(t).data);L.fetchers.set(t,e)}}function eZ(){let e=[],t=!1;for(let r of q){let a=L.fetchers.get(r);E(a,`Expected fetcher: ${r}`),"loading"===a.state&&(q.delete(r),e.push(r),t=!0)}return eK(e),t}function eQ(e){let t=[];for(let[r,a]of Y)if(a<e){let e=L.fetchers.get(r);E(e,`Expected fetcher: ${r}`),"loading"===e.state&&(eG(r),Y.delete(r),t.push(r))}return eK(t),t.length>0}function e0(e){L.blockers.delete(e),G.delete(e)}function e1(e,t){let r=L.blockers.get(e)||et;E("unblocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"proceeding"===t.state||"blocked"===r.state&&"unblocked"===t.state||"proceeding"===r.state&&"unblocked"===t.state,`Invalid blocker state transition: ${r.state} -> ${t.state}`);let a=new Map(L.blockers);a.set(e,t),ec({blockers:a})}function e2({currentLocation:e,nextLocation:t,historyAction:r}){if(0===G.size)return;G.size>1&&x(!1,"A router only supports one blocker at a time");let a=Array.from(G.entries()),[n,o]=a[a.length-1],i=L.blockers.get(n);if((!i||"proceeding"!==i.state)&&o({currentLocation:e,nextLocation:t,historyAction:r}))return n}function e4(e){let r=eO(404,{pathname:e}),{matches:a,route:n}=eM(t||d);return{notFoundMatches:a,route:n,error:r}}function e3(e,t){return g&&g(e,t.map(e=>(function(e,t){let{route:r,pathname:a,params:n}=e;return{id:r.id,pathname:a,params:n,data:t[r.id],handle:r.handle}})(e,L.loaderData)))||e.key}function e5(e,t){if(v){let r=v[e3(e,t)];if("number"==typeof r)return r}return null}function e9(t,r,a){if(e.patchRoutesOnNavigation){if(!t)return{active:!0,matches:N(r,a,h,!0)||[]};else if(Object.keys(t[0].params).length>0)return{active:!0,matches:N(r,a,h,!0)}}return{active:!1,matches:null}}async function e8(r,a,n,o){if(!e.patchRoutesOnNavigation)return{type:"success",matches:r};let i=r;for(;;){let r=null==t,s=t||d,l=c;try{await e.patchRoutesOnNavigation({signal:n,path:a,matches:i,fetcherKey:o,patch:(e,t)=>{n.aborted||ed(e,t,s,l,u)}})}catch(e){return{type:"error",error:e,partialMatches:i}}finally{r&&!n.aborted&&(d=[...d])}if(n.aborted)return{type:"aborted"};let f=T(s,a,h);if(f)return{type:"success",matches:f};let m=N(s,a,h,!0);if(!m||i.length===m.length&&i.every((e,t)=>e.route.id===m[t].route.id))return{type:"success",matches:null};i=m}}return a={get basename(){return h},get future(){return m},get state(){return L},get routes(){return d},get window(){return i},initialize:function(){if(p=e.history.listen(({action:t,location:r,delta:a})=>{if(o){o(),o=void 0;return}x(0===G.size||null!=a,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let n=e2({currentLocation:L.location,nextLocation:r,historyAction:t});if(n&&null!=a){let t=new Promise(e=>{o=e});e.history.go(-1*a),e1(n,{state:"blocked",location:r,proceed(){e1(n,{state:"proceeding",proceed:void 0,reset:void 0,location:r}),t.then(()=>e.history.go(a))},reset(){let e=new Map(L.blockers);e.set(n,et),ec({blockers:e})}});return}return em(t,r)}),s){var t=i,r=$;try{let e=t.sessionStorage.getItem(en);if(e){let t=JSON.parse(e);for(let[e,a]of Object.entries(t||{}))a&&Array.isArray(a)&&r.set(e,new Set(a||[]))}}catch(e){}let e=()=>(function(e,t){if(t.size>0){let r={};for(let[e,a]of t)r[e]=[...a];try{e.sessionStorage.setItem(en,JSON.stringify(r))}catch(e){x(!1,`Failed to save applied view transitions in sessionStorage (${e}).`)}}})(i,$);i.addEventListener("pagehide",e),D=()=>i.removeEventListener("pagehide",e)}return L.initialized||em("POP",L.location,{initialHydration:!0}),a},subscribe:function(e){return y.add(e),()=>y.delete(e)},enableScrollRestoration:function(e,t,r){if(v=e,w=t,g=r||null,!b&&L.navigation===Q){b=!0;let e=e5(L.location,L.matches);null!=e&&ec({restoreScrollPosition:e})}return()=>{v=null,w=null,g=null}},navigate:ef,fetch:async function r(r,a,n,o){eG(r);let i=!0===(o&&o.flushSync),s=t||d,l=ei(L.location,L.matches,h,n,a,o?.relative),u=T(s,l,h),c=e9(u,s,l);if(c.active&&c.matches&&(u=c.matches),!u)return void ez(r,a,eO(404,{pathname:l}),{flushSync:i});let{path:f,submission:m,error:p}=es(!0,l,o);if(p)return void ez(r,a,p,{flushSync:i});let y=eB(u,f),v=new _(e.unstable_getContext?await e.unstable_getContext():void 0),g=!0===(o&&o.preventScrollReset);if(m&&eU(m.formMethod))return void await ew(r,a,f,y,u,v,c.active,i,g,m);W.set(r,{routeId:a,path:f}),await eb(r,a,f,y,u,v,c.active,i,g,m)},revalidate:function(){let e,t,r;eo||(eo={promise:r=new Promise((a,n)=>{e=async e=>{a(e);try{await r}catch(e){}},t=async e=>{n(e);try{await r}catch(e){}}}),resolve:e,reject:t}),eD(),ec({revalidation:"loading"});let a=eo.promise;return"submitting"===L.navigation.state||("idle"===L.navigation.state?em(L.historyAction,L.location,{startUninterruptedRevalidation:!0}):em(k||L.historyAction,L.navigation.location,{overrideNavigation:L.navigation,enableViewTransition:!0===O})),a},createHref:t=>e.history.createHref(t),encodeLocation:t=>e.history.encodeLocation(t),getFetcher:eV,deleteFetcher:function(e){let t=(V.get(e)||0)-1;t<=0?(V.delete(e),X.add(e)):V.set(e,t),ec({fetchers:new Map(L.fetchers)})},dispose:function(){p&&p(),D&&D(),y.clear(),n&&n.abort(),L.fetchers.forEach((e,t)=>eX(t)),L.blockers.forEach((e,t)=>e0(t))},getBlocker:function(e,t){let r=L.blockers.get(e)||et;return G.get(e)!==t&&G.set(e,t),r},deleteBlocker:e0,patchRoutes:function(e,r){let a=null==t;ed(e,r,t||d,c,u),a&&(d=[...d],ec({}))},_internalFetchControllers:U,_internalSetRoutes:function(e){t=j(e,u,void 0,c={})}}}({routes:f,history:function(e={}){return function(e,t,r,a={}){let{window:n=document.defaultView,v5Compat:o=!1}=a,i=n.history,s="POP",l=null,u=c();function c(){return(i.state||{idx:null}).idx}function d(){s="POP";let e=c(),t=null==e?null:e-u;u=e,l&&l({action:s,location:f.location,delta:t})}function h(e){let t="null"!==n.location.origin?n.location.origin:n.location.href,r="string"==typeof e?e:C(e);return r=r.replace(/ $/,"%20"),E(t,`No window.location.(origin|href) available to create URL for href: ${r}`),new URL(r,t)}null==u&&(u=0,i.replaceState({...i.state,idx:u},""));let f={get action(){return s},get location(){return e(n,i)},listen(e){if(l)throw Error("A history only accepts one active listener");return n.addEventListener(b,d),l=e,()=>{n.removeEventListener(b,d),l=null}},createHref:e=>t(n,e),createURL:h,encodeLocation(e){let t=h(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s="PUSH";let r=S(f.location,e,t);let a=R(r,u=c()+1),d=f.createHref(r);try{i.pushState(a,"",d)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;n.location.assign(d)}o&&l&&l({action:s,location:f.location,delta:1})},replace:function(e,t){s="REPLACE";let a=S(f.location,e,t);r&&r(a,e);let n=R(a,u=c()),d=f.createHref(a);i.replaceState(n,"",d),o&&l&&l({action:s,location:f.location,delta:0})},go:e=>i.go(e)};return f}(function(e,t){let{pathname:r,search:a,hash:n}=e.location;return S("",{pathname:r,search:a,hash:n},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:C(t)},null,e)}(),basename:t4.context.basename,unstable_getContext:e,hydrationData:c,hydrationRouteProperties:ti,mapRouteProperties:to,future:{unstable_middleware:t4.context.future.unstable_middleware},dataStrategy:(t=()=>m,r=t4.manifest,a=t4.routeModules,n=t4.context.ssr,o=t,i=e=>{let t=r.routes[e.route.id];th(t,"Route not found in manifest");let n=a[e.route.id];return{hasLoader:t.hasLoader,hasClientLoader:t.hasClientLoader,hasShouldRevalidate:!!n?.shouldRevalidate}},s=tP,l=n,u=t4.context.basename,d=async e=>{let{request:t,matches:r,fetcherKey:a}=e,n=o();if("GET"!==t.method)return tx(e,s,u);let c=r.some(e=>{let{hasLoader:t,hasClientLoader:r}=i(e);return e.unstable_shouldCallHandler()&&t&&!r});return l||c?a?tC(e,s,u):tS(e,n,i,s,l,u):tR(e,i,s,u)},async e=>e.unstable_runClientMiddleware(d)),patchRoutesOnNavigation:function(e,t,r,a,n){if(!0===r)return async({path:o,patch:i,signal:s,fetcherKey:l})=>{tV.has(o)||await tG([o],l?window.location.href:o,e,t,r,a,n,i,s)}}(t4.manifest,t4.routeModules,t4.context.ssr,t4.context.isSpaMode,t4.context.basename)});return t4.router=m,m.state.initialized&&(t4.routerInitialized=!0,m.initialize()),m.createRoutesForHMR=tI,window.__reactRouterDataRouter=m,m}({unstable_getContext:e.unstable_getContext}));let[i,s]=t2.useState(void 0),[l,u]=t2.useState(t3.state.location);return t2.useLayoutEffect(()=>{t4&&t4.router&&!t4.routerInitialized&&(t4.routerInitialized=!0,t4.router.initialize())},[]),t2.useLayoutEffect(()=>{if(t4&&t4.router)return t4.router.subscribe(e=>{e.location!==l&&u(e.location)})},[l]),E(t4,"ssrInfo unavailable for HydratedRouter"),t=t3,r=t4.manifest,a=t4.routeModules,n=t4.context.ssr,o=t4.context.isSpaMode,tj.useEffect(()=>{var e,i;let s;if(!0!==n||navigator.connection?.saveData===!0)return;function l(e){let t="FORM"===e.tagName?e.getAttribute("action"):e.getAttribute("href");if(!t)return;let r="A"===e.tagName?e.pathname:new URL(t,window.location.origin).pathname;tV.has(r)||tJ.add(r)}async function u(){document.querySelectorAll("a[data-discover], form[data-discover]").forEach(l);let e=Array.from(tJ.keys()).filter(e=>!tV.has(e)||(tJ.delete(e),!1));if(0!==e.length)try{await tG(e,null,r,a,n,o,t.basename,t.patchRoutes)}catch(e){console.error("Failed to fetch manifest patches",e)}}let c=(e=u,i=100,(...t)=>{window.clearTimeout(s),s=window.setTimeout(()=>e(...t),100)});u();let d=new MutationObserver(()=>c());return d.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>d.disconnect()},[n,o,r,a,t]),t2.createElement(t2.Fragment,null,t2.createElement(tK.Provider,{value:{manifest:t4.manifest,routeModules:t4.routeModules,future:t4.context.future,criticalCss:i,ssr:t4.context.ssr,isSpaMode:t4.context.isSpaMode}},t2.createElement(tM,{location:l},t2.createElement(t1,{router:t3}))),t2.createElement(t2.Fragment,null))}},10333:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unflatten=void 0;let a=r(73143),n="undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:void 0;function o(e){let t,{hydrated:r,values:o,deferred:i,plugins:s}=this,l=[[e,e=>{t=e}]],u=[];for(;l.length>0;){let[e,t]=l.pop();switch(e){case a.UNDEFINED:t(void 0);continue;case a.NULL:t(null);continue;case a.NAN:t(NaN);continue;case a.POSITIVE_INFINITY:t(1/0);continue;case a.NEGATIVE_INFINITY:t(-1/0);continue;case a.NEGATIVE_ZERO:t(-0);continue}if(r[e]){t(r[e]);continue}let c=o[e];if(!c||"object"!=typeof c){r[e]=c,t(c);continue}if(Array.isArray(c))if("string"==typeof c[0]){let[o,d,h]=c;switch(o){case a.TYPE_DATE:t(r[e]=new Date(d));continue;case a.TYPE_URL:t(r[e]=new URL(d));continue;case a.TYPE_BIGINT:t(r[e]=BigInt(d));continue;case a.TYPE_REGEXP:t(r[e]=new RegExp(d,h));continue;case a.TYPE_SYMBOL:t(r[e]=Symbol.for(d));continue;case a.TYPE_SET:let f=new Set;r[e]=f;for(let e=1;e<c.length;e++)l.push([c[e],e=>{f.add(e)}]);t(f);continue;case a.TYPE_MAP:let m=new Map;r[e]=m;for(let e=1;e<c.length;e+=2){let t=[];l.push([c[e+1],e=>{t[1]=e}]),l.push([c[e],e=>{t[0]=e}]),u.push(()=>{m.set(t[0],t[1])})}t(m);continue;case a.TYPE_NULL_OBJECT:let p=Object.create(null);for(let t of(r[e]=p,Object.keys(d).reverse())){let e=[];l.push([d[t],t=>{e[1]=t}]),l.push([Number(t.slice(1)),t=>{e[0]=t}]),u.push(()=>{p[e[0]]=e[1]})}t(p);continue;case a.TYPE_PROMISE:if(r[d])t(r[e]=r[d]);else{let n=new a.Deferred;i[d]=n,t(r[e]=n.promise)}continue;case a.TYPE_ERROR:let[,y,v]=c,g=v&&n&&n[v]?new n[v](y):Error(y);r[e]=g,t(g);continue;case a.TYPE_PREVIOUS_RESOLVED:t(r[e]=r[d]);continue;default:if(Array.isArray(s)){let a=[],n=c.slice(1);for(let e=0;e<n.length;e++){let t=n[e];l.push([t,t=>{a[e]=t}])}u.push(()=>{for(let n of s){let o=n(c[0],...a);if(o)return void t(r[e]=o.value)}throw SyntaxError()});continue}throw SyntaxError()}}else{let n=[];r[e]=n;for(let e=0;e<c.length;e++){let t=c[e];t!==a.HOLE&&l.push([t,t=>{n[e]=t}])}t(n);continue}{let a={};for(let t of(r[e]=a,Object.keys(c).reverse())){let e=[];l.push([c[t],t=>{e[1]=t}]),l.push([Number(t.slice(1)),t=>{e[0]=t}]),u.push(()=>{a[e[0]]=e[1]})}t(a);continue}}for(;u.length>0;)u.pop()();return t}t.unflatten=function(e){let{hydrated:t,values:r}=this;if("number"==typeof e)return o.call(this,e);if(!Array.isArray(e)||!e.length)throw SyntaxError();let a=r.length;for(let t of e)r.push(t);return t.length=r.length,o.call(this,a)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14791:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.encode=t.decode=void 0;let a=r(17744),n=r(10333),o=r(73143);async function i(e){let t,r=await e.read();if(!r.value)throw SyntaxError();try{t=JSON.parse(r.value)}catch(e){throw SyntaxError()}return{done:r.done,value:n.unflatten.call(this,t)}}async function s(e){let t=await e.read();for(;!t.done;){if(!t.value)continue;let r=t.value;switch(r[0]){case o.TYPE_PROMISE:{let e,t=r.indexOf(":"),a=Number(r.slice(1,t)),o=this.deferred[a];if(!o)throw Error(`Deferred ID ${a} not found in stream`);let i=r.slice(t+1);try{e=JSON.parse(i)}catch(e){throw SyntaxError()}let s=n.unflatten.call(this,e);o.resolve(s);break}case o.TYPE_ERROR:{let e,t=r.indexOf(":"),a=Number(r.slice(1,t)),o=this.deferred[a];if(!o)throw Error(`Deferred ID ${a} not found in stream`);let i=r.slice(t+1);try{e=JSON.parse(i)}catch(e){throw SyntaxError()}let s=n.unflatten.call(this,e);o.reject(s);break}default:throw SyntaxError()}t=await e.read()}}t.decode=async function(e,t){let{plugins:r}=t??{},a=new o.Deferred,n=e.pipeThrough((0,o.createLineSplittingTransform)()).getReader(),l={values:[],hydrated:[],deferred:{},plugins:r},u=await i.call(l,n),c=a.promise;return u.done?a.resolve():c=s.call(l,n).then(a.resolve).catch(e=>{for(let t of Object.values(l.deferred))t.reject(e);a.reject(e)}),{done:c.then(()=>n.closed),value:u.value}},t.encode=function(e,t){let{plugins:r,postPlugins:n,signal:i}=t??{},s={deferred:{},index:0,indices:new Map,stringified:[],plugins:r,postPlugins:n,signal:i},l=new TextEncoder,u=0;return new ReadableStream({async start(t){let r=a.flatten.call(s,e);if(Array.isArray(r))throw Error("This should never happen");r<0?t.enqueue(l.encode(`${r}
`)):(t.enqueue(l.encode(`[${s.stringified.join(",")}]
`)),u=s.stringified.length-1);let n=new WeakSet;for(;Object.keys(s.deferred).length>0;){for(let[e,r]of Object.entries(s.deferred))n.has(r)||n.add(s.deferred[Number(e)]=(function(e,t){if(!t)return e;if(t.aborted)return Promise.reject(t.reason||Error("Signal was aborted."));let r=new Promise((r,a)=>{t.addEventListener("abort",e=>{a(t.reason||Error("Signal was aborted."))}),e.then(r).catch(a)});return r.catch(()=>{}),Promise.race([r,e])})(r,s.signal).then(r=>{let n=a.flatten.call(s,r);if(Array.isArray(n))t.enqueue(l.encode(`${o.TYPE_PROMISE}${e}:[["${o.TYPE_PREVIOUS_RESOLVED}",${n[0]}]]
`)),s.index++,u++;else if(n<0)t.enqueue(l.encode(`${o.TYPE_PROMISE}${e}:${n}
`));else{let r=s.stringified.slice(u+1).join(",");t.enqueue(l.encode(`${o.TYPE_PROMISE}${e}:[${r}]
`)),u=s.stringified.length-1}},r=>{r&&"object"==typeof r&&r instanceof Error||(r=Error("An unknown error occurred"));let n=a.flatten.call(s,r);if(Array.isArray(n))t.enqueue(l.encode(`${o.TYPE_ERROR}${e}:[["${o.TYPE_PREVIOUS_RESOLVED}",${n[0]}]]
`)),s.index++,u++;else if(n<0)t.enqueue(l.encode(`${o.TYPE_ERROR}${e}:${n}
`));else{let r=s.stringified.slice(u+1).join(",");t.enqueue(l.encode(`${o.TYPE_ERROR}${e}:[${r}]
`)),u=s.stringified.length-1}}).finally(()=>{delete s.deferred[Number(e)]}));await Promise.race(Object.values(s.deferred))}await Promise.all(Object.values(s.deferred)),t.close()}})}},17744:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.flatten=void 0;let a=r(73143);function n(e){let{indices:t}=this,r=t.get(e);if(r)return[r];if(void 0===e)return a.UNDEFINED;if(null===e)return a.NULL;if(Number.isNaN(e))return a.NAN;if(e===Number.POSITIVE_INFINITY)return a.POSITIVE_INFINITY;if(e===Number.NEGATIVE_INFINITY)return a.NEGATIVE_INFINITY;if(0===e&&1/e<0)return a.NEGATIVE_ZERO;let n=this.index++;return t.set(e,n),o.call(this,e,n),n}function o(e,t){let{deferred:r,plugins:o,postPlugins:s}=this,l=this.stringified,u=[[e,t]];for(;u.length>0;){let[e,t]=u.pop(),c=e=>Object.keys(e).map(t=>`"_${n.call(this,t)}":${n.call(this,e[t])}`).join(","),d=null;switch(typeof e){case"boolean":case"number":case"string":l[t]=JSON.stringify(e);break;case"bigint":l[t]=`["${a.TYPE_BIGINT}","${e}"]`;break;case"symbol":{let r=Symbol.keyFor(e);r?l[t]=`["${a.TYPE_SYMBOL}",${JSON.stringify(r)}]`:d=Error("Cannot encode symbol unless created with Symbol.for()");break}case"object":{if(!e){l[t]=`${a.NULL}`;break}let s=Array.isArray(e),u=!1;if(!s&&o)for(let r of o){let a=r(e);if(Array.isArray(a)){u=!0;let[e,...r]=a;l[t]=`[${JSON.stringify(e)}`,r.length>0&&(l[t]+=`,${r.map(e=>n.call(this,e)).join(",")}`),l[t]+="]";break}}if(!u){let o=s?"[":"{";if(s){for(let t=0;t<e.length;t++)o+=(t?",":"")+(t in e?n.call(this,e[t]):a.HOLE);l[t]=`${o}]`}else e instanceof Date?l[t]=`["${a.TYPE_DATE}",${e.getTime()}]`:e instanceof URL?l[t]=`["${a.TYPE_URL}",${JSON.stringify(e.href)}]`:e instanceof RegExp?l[t]=`["${a.TYPE_REGEXP}",${JSON.stringify(e.source)},${JSON.stringify(e.flags)}]`:e instanceof Set?e.size>0?l[t]=`["${a.TYPE_SET}",${[...e].map(e=>n.call(this,e)).join(",")}]`:l[t]=`["${a.TYPE_SET}"]`:e instanceof Map?e.size>0?l[t]=`["${a.TYPE_MAP}",${[...e].flatMap(([e,t])=>[n.call(this,e),n.call(this,t)]).join(",")}]`:l[t]=`["${a.TYPE_MAP}"]`:e instanceof Promise?(l[t]=`["${a.TYPE_PROMISE}",${t}]`,r[t]=e):e instanceof Error?(l[t]=`["${a.TYPE_ERROR}",${JSON.stringify(e.message)}`,"Error"!==e.name&&(l[t]+=`,${JSON.stringify(e.name)}`),l[t]+="]"):null===Object.getPrototypeOf(e)?l[t]=`["${a.TYPE_NULL_OBJECT}",{${c(e)}}]`:function(e){let t=Object.getPrototypeOf(e);return t===Object.prototype||null===t||Object.getOwnPropertyNames(t).sort().join("\0")===i}(e)?l[t]=`{${c(e)}}`:d=Error("Cannot encode object with prototype")}break}default:{let r=Array.isArray(e),a=!1;if(!r&&o)for(let r of o){let o=r(e);if(Array.isArray(o)){a=!0;let[e,...r]=o;l[t]=`[${JSON.stringify(e)}`,r.length>0&&(l[t]+=`,${r.map(e=>n.call(this,e)).join(",")}`),l[t]+="]";break}}a||(d=Error("Cannot encode function or unexpected type"))}}if(d){let r=!1;if(s)for(let a of s){let o=a(e);if(Array.isArray(o)){r=!0;let[e,...a]=o;l[t]=`[${JSON.stringify(e)}`,a.length>0&&(l[t]+=`,${a.map(e=>n.call(this,e)).join(",")}`),l[t]+="]";break}}if(!r)throw d}}}t.flatten=n;let i=Object.getOwnPropertyNames(Object.prototype).sort().join("\0")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28356:e=>{"use strict";var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function a(e,a){var n,o,i,s,l=e.split(";").filter(r),u=(n=l.shift(),o="",i="",(s=n.split("=")).length>1?(o=s.shift(),i=s.join("=")):i=n,{name:o,value:i}),c=u.name,d=u.value;a=a?Object.assign({},t,a):t;try{d=a.decodeValues?decodeURIComponent(d):d}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+d+"'. Set options.decodeValues to false to disable this feature.",e)}var h={name:c,value:d};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),a=t.join("=");"expires"===r?h.expires=new Date(a):"max-age"===r?h.maxAge=parseInt(a,10):"secure"===r?h.secure=!0:"httponly"===r?h.httpOnly=!0:"samesite"===r?h.sameSite=a:"partitioned"===r?h.partitioned=!0:h[r]=a}),h}function n(e,n){if(n=n?Object.assign({},t,n):t,!e)if(!n.map)return[];else return{};if(e.headers)if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var o=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];o||!e.headers.cookie||n.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=o}return(Array.isArray(e)||(e=[e]),n.map)?e.filter(r).reduce(function(e,t){var r=a(t,n);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return a(e,n)})}e.exports=n,e.exports.parse=n,e.exports.parseString=a,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,a,n,o,i=[],s=0;function l(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;l();)if(","===(r=e.charAt(s))){for(a=s,s+=1,l(),n=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=n,i.push(e.substring(t,a)),t=s):s=a+1}else s+=1;(!o||s>=e.length)&&i.push(e.substring(t,e.length))}return i}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29438:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\page-components\\\\RegisterPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\RegisterPage.tsx","default")},29594:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(60687),n=r(43210),o=r(85814),i=r.n(o),s=r(35421),l=r(53199);let u=()=>{let[e,t]=(0,n.useState)([]),[r,o]=(0,n.useState)([]),[u,c]=(0,n.useState)(!0),[d,h]=(0,n.useState)(null);return((0,n.useEffect)(()=>{(async()=>{try{c(!0);let[e,r]=await Promise.all([s.bk.getFeaturedProducts(),s.M$.getAllCategories()]);t(e.data),o(r.data),h(null)}catch(e){console.error("Error fetching data:",e),h("Failed to load data. Please try again later.")}finally{c(!1)}})()},[]),u)?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):d?(0,a.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 my-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:d})})]})}):(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"bg-indigo-700 rounded-lg shadow-xl overflow-hidden",children:(0,a.jsx)("div",{className:"px-4 py-12 sm:px-6 lg:px-8 lg:py-16",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-extrabold text-white sm:text-4xl",children:(0,a.jsx)("span",{className:"block",children:"Welcome to EcommerceApp"})}),(0,a.jsx)("p",{className:"mt-4 text-lg leading-6 text-indigo-100",children:"Discover amazing products at great prices. Shop now and enjoy fast delivery!"}),(0,a.jsx)("div",{className:"mt-8 flex justify-center",children:(0,a.jsx)("div",{className:"inline-flex rounded-md shadow",children:(0,a.jsx)(i(),{href:"/products",className:"inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50",children:"Browse Products"})})})]})})}),(0,a.jsxs)("div",{className:"mt-12",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Featured Products"}),(0,a.jsx)(i(),{href:"/products",className:"text-indigo-600 hover:text-indigo-500",children:"View all"})]}),e.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4",children:e.map(e=>(0,a.jsx)(l.AA,{product:e},e.id))}):(0,a.jsx)("p",{className:"text-gray-500",children:"No featured products available."})]}),(0,a.jsxs)("div",{className:"mt-12",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Shop by Category"}),(0,a.jsx)(i(),{href:"/categories",className:"text-indigo-600 hover:text-indigo-500",children:"View all"})]}),r.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3",children:r.slice(0,3).map(e=>(0,a.jsx)(l.rm,{category:e},e.id))}):(0,a.jsx)("p",{className:"text-gray-500",children:"No categories available."})]})]})}},33057:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parse=function(e,t){let r=new s,a=e.length;if(a<2)return r;let n=t?.decode||c,o=0;do{let t=e.indexOf("=",o);if(-1===t)break;let i=e.indexOf(";",o),s=-1===i?a:i;if(t>s){o=e.lastIndexOf(";",t-1)+1;continue}let c=l(e,o,t),d=u(e,t,c),h=e.slice(c,d);if(void 0===r[h]){let a=l(e,t+1,s),o=u(e,s,a),i=n(e.slice(a,o));r[h]=i}o=s+1}while(o<a);return r},t.serialize=function(e,t,s){let l=s?.encode||encodeURIComponent;if(!r.test(e))throw TypeError(`argument name is invalid: ${e}`);let u=l(t);if(!a.test(u))throw TypeError(`argument val is invalid: ${t}`);let c=e+"="+u;if(!s)return c;if(void 0!==s.maxAge){if(!Number.isInteger(s.maxAge))throw TypeError(`option maxAge is invalid: ${s.maxAge}`);c+="; Max-Age="+s.maxAge}if(s.domain){if(!n.test(s.domain))throw TypeError(`option domain is invalid: ${s.domain}`);c+="; Domain="+s.domain}if(s.path){if(!o.test(s.path))throw TypeError(`option path is invalid: ${s.path}`);c+="; Path="+s.path}if(s.expires){var d;if(d=s.expires,"[object Date]"!==i.call(d)||!Number.isFinite(s.expires.valueOf()))throw TypeError(`option expires is invalid: ${s.expires}`);c+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(c+="; HttpOnly"),s.secure&&(c+="; Secure"),s.partitioned&&(c+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${s.priority}`)}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${s.sameSite}`)}return c};let r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,a=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,s=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function u(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},33649:(e,t,r)=>{Promise.resolve().then(r.bind(r,29594)),Promise.resolve().then(r.bind(r,48410)),Promise.resolve().then(r.bind(r,72852))},33873:e=>{"use strict";e.exports=require("path")},39709:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(37413),n=r(92560);function o(){return(0,a.jsx)(n.default,{})}r(52020),r(29438)},43377:(e,t,r)=>{Promise.resolve().then(r.bind(r,92560)),Promise.resolve().then(r.bind(r,52020)),Promise.resolve().then(r.bind(r,29438))},48410:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var a=r(60687),n=r(43210),o=r(82989),i=r(35421);let s=()=>{let[e,t]=(0,n.useState)(""),[r,s]=(0,n.useState)(""),[l,u]=(0,n.useState)(null),[c,d]=(0,n.useState)(!1),h=(0,o.useNavigate)(),f=async t=>{t.preventDefault();try{d(!0),u(null),await i.uR.login({email:e,password:r}),h("/")}catch(e){console.error("Login error:",e),u(e.response?.data?.message||"Invalid email or password")}finally{d(!1)}};return(0,a.jsxs)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,a.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,a.jsx)(o.Link,{to:"/register",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"create a new account"})]})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[l&&(0,a.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:l})})]})}),(0,a.jsxs)("form",{className:"space-y-6",onSubmit:f,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>s(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:c,className:`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${c?"opacity-70 cursor-not-allowed":""}`,children:c?"Signing in...":"Sign in"})})]})]})})]})}},52020:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\page-components\\\\LoginPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\LoginPage.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72124:(e,t,r)=>{"use strict";var a,n,o=Object.create,i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,d=e=>{throw TypeError(e)},h=(e,t,r,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of l(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(a=s(t,n))||a.enumerable});return e},f=(e,t,r)=>(r=null!=e?o(u(e)):{},h(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)),m=(e,t,r)=>t.has(e)||d("Cannot "+r),p=(e,t,r)=>(m(e,t,"read from private field"),r?r.call(e):t.get(e)),y=(e,t,r)=>t.has(e)?d("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),v={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(v,{Await:()=>rs,BrowserRouter:()=>aL,Form:()=>aO,HashRouter:()=>ak,IDLE_BLOCKER:()=>em,IDLE_FETCHER:()=>ef,IDLE_NAVIGATION:()=>eh,Link:()=>aN,Links:()=>ag,MemoryRouter:()=>rt,Meta:()=>aE,NavLink:()=>aM,Navigate:()=>rr,NavigationType:()=>g,Outlet:()=>ra,PrefetchPageLinks:()=>aw,Route:()=>rn,Router:()=>ro,RouterProvider:()=>t6,Routes:()=>ri,Scripts:()=>aR,ScrollRestoration:()=>a$,ServerRouter:()=>na,StaticRouter:()=>a1,StaticRouterProvider:()=>a2,UNSAFE_DataRouterContext:()=>td,UNSAFE_DataRouterStateContext:()=>th,UNSAFE_ErrorResponseImpl:()=>eo,UNSAFE_FetchersContext:()=>tm,UNSAFE_FrameworkContext:()=>am,UNSAFE_LocationContext:()=>tv,UNSAFE_NavigationContext:()=>ty,UNSAFE_RemixErrorBoundary:()=>r1,UNSAFE_RouteContext:()=>tg,UNSAFE_ServerMode:()=>nv,UNSAFE_SingleFetchRedirectSymbol:()=>rI,UNSAFE_ViewTransitionContext:()=>tf,UNSAFE_createBrowserHistory:()=>E,UNSAFE_createClientRoutes:()=>at,UNSAFE_createClientRoutesWithHMRRevalidationOptOut:()=>r7,UNSAFE_createRouter:()=>ew,UNSAFE_decodeViaTurboStream:()=>rX,UNSAFE_deserializeErrors:()=>n3,UNSAFE_getHydrationData:()=>n5,UNSAFE_getPatchRoutesOnNavigationFunction:()=>al,UNSAFE_getTurboStreamSingleFetchDataStrategy:()=>rz,UNSAFE_hydrationRouteProperties:()=>t9,UNSAFE_invariant:()=>R,UNSAFE_mapRouteProperties:()=>t5,UNSAFE_shouldHydrateRouteLoader:()=>ao,UNSAFE_useFogOFWarDiscovery:()=>au,UNSAFE_useScrollRestoration:()=>aG,createBrowserRouter:()=>aC,createCookie:()=>nd,createCookieSessionStorage:()=>n1,createHashRouter:()=>aP,createMemoryRouter:()=>t8,createMemorySessionStorage:()=>n2,createPath:()=>_,createRequestHandler:()=>nY,createRoutesFromChildren:()=>rc,createRoutesFromElements:()=>rd,createRoutesStub:()=>no,createSearchParams:()=>rv,createSession:()=>nK,createSessionStorage:()=>nQ,createStaticHandler:()=>a5,createStaticRouter:()=>a9,data:()=>et,generatePath:()=>H,href:()=>n4,isCookie:()=>nh,isRouteErrorResponse:()=>ei,isSession:()=>nZ,matchPath:()=>U,matchRoutes:()=>$,parsePath:()=>L,redirect:()=>er,redirectDocument:()=>ea,renderMatches:()=>rh,replace:()=>en,resolvePath:()=>q,unstable_HistoryRouter:()=>aj,unstable_RouterContextProvider:()=>T,unstable_createContext:()=>j,unstable_setDevServerHooks:()=>nP,unstable_usePrompt:()=>aZ,useActionData:()=>tX,useAsyncError:()=>tZ,useAsyncValue:()=>tK,useBeforeUnload:()=>aK,useBlocker:()=>t0,useFetcher:()=>aq,useFetchers:()=>aW,useFormAction:()=>aY,useHref:()=>tE,useInRouterContext:()=>tx,useLinkClickHandler:()=>aI,useLoaderData:()=>tJ,useLocation:()=>tR,useMatch:()=>tC,useMatches:()=>tW,useNavigate:()=>tL,useNavigation:()=>tY,useNavigationType:()=>tS,useOutlet:()=>tT,useOutletContext:()=>tj,useParams:()=>tN,useResolvedPath:()=>tM,useRevalidator:()=>tq,useRouteError:()=>tG,useRouteLoaderData:()=>tV,useRoutes:()=>tO,useSearchParams:()=>aH,useSubmit:()=>aB,useViewTransitionState:()=>aQ}),e.exports=h(i({},"__esModule",{value:!0}),v);var g=(e=>(e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE",e))(g||{}),w="popstate";function b(e={}){let t,{initialEntries:r=["/"],initialIndex:a,v5Compat:n=!1}=e;t=r.map((e,t)=>u(e,"string"==typeof e?null:e.state,0===t?"default":void 0));let o=l(null==a?t.length-1:a),i="POP",s=null;function l(e){return Math.min(Math.max(e,0),t.length-1)}function u(e,r=null,a){let n=P(t?t[o].pathname:"/",e,r,a);return S("/"===n.pathname.charAt(0),`relative pathnames are not supported in memory history: ${JSON.stringify(e)}`),n}function c(e){return"string"==typeof e?e:_(e)}return{get index(){return o},get action(){return i},get location(){return t[o]},createHref:c,createURL:e=>new URL(c(e),"http://localhost"),encodeLocation(e){let t="string"==typeof e?L(e):e;return{pathname:t.pathname||"",search:t.search||"",hash:t.hash||""}},push(e,r){i="PUSH";let a=u(e,r);o+=1,t.splice(o,t.length,a),n&&s&&s({action:i,location:a,delta:1})},replace(e,r){i="REPLACE";let a=u(e,r);t[o]=a,n&&s&&s({action:i,location:a,delta:0})},go(e){i="POP";let r=l(o+e),a=t[r];o=r,s&&s({action:i,location:a,delta:e})},listen:e=>(s=e,()=>{s=null})}}function E(e={}){return k(function(e,t){let{pathname:r,search:a,hash:n}=e.location;return P("",{pathname:r,search:a,hash:n},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:_(t)},null,e)}function x(e={}){return k(function(e,t){let{pathname:r="/",search:a="",hash:n=""}=L(e.location.hash.substring(1));return r.startsWith("/")||r.startsWith(".")||(r="/"+r),P("",{pathname:r,search:a,hash:n},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){let r=e.document.querySelector("base"),a="";if(r&&r.getAttribute("href")){let t=e.location.href,r=t.indexOf("#");a=-1===r?t:t.slice(0,r)}return a+"#"+("string"==typeof t?t:_(t))},function(e,t){S("/"===e.pathname.charAt(0),`relative pathnames are not supported in hash history.push(${JSON.stringify(t)})`)},e)}function R(e,t){if(!1===e||null==e)throw Error(t)}function S(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw Error(t)}catch(e){}}}function C(e,t){return{usr:e.state,key:e.key,idx:t}}function P(e,t,r=null,a){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?L(t):t,state:r,key:t&&t.key||a||Math.random().toString(36).substring(2,10)}}function _({pathname:e="/",search:t="",hash:r=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),r&&"#"!==r&&(e+="#"===r.charAt(0)?r:"#"+r),e}function L(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substring(a),e=e.substring(0,a)),e&&(t.pathname=e)}return t}function k(e,t,r,a={}){let{window:n=document.defaultView,v5Compat:o=!1}=a,i=n.history,s="POP",l=null,u=c();function c(){return(i.state||{idx:null}).idx}function d(){s="POP";let e=c(),t=null==e?null:e-u;u=e,l&&l({action:s,location:f.location,delta:t})}function h(e){let t="null"!==n.location.origin?n.location.origin:n.location.href,r="string"==typeof e?e:_(e);return r=r.replace(/ $/,"%20"),R(t,`No window.location.(origin|href) available to create URL for href: ${r}`),new URL(r,t)}null==u&&(u=0,i.replaceState({...i.state,idx:u},""));let f={get action(){return s},get location(){return e(n,i)},listen(e){if(l)throw Error("A history only accepts one active listener");return n.addEventListener(w,d),l=e,()=>{n.removeEventListener(w,d),l=null}},createHref:e=>t(n,e),createURL:h,encodeLocation(e){let t=h(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s="PUSH";let a=P(f.location,e,t);r&&r(a,e);let d=C(a,u=c()+1),h=f.createHref(a);try{i.pushState(d,"",h)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;n.location.assign(h)}o&&l&&l({action:s,location:f.location,delta:1})},replace:function(e,t){s="REPLACE";let a=P(f.location,e,t);r&&r(a,e);let n=C(a,u=c()),d=f.createHref(a);i.replaceState(n,"",d),o&&l&&l({action:s,location:f.location,delta:0})},go:e=>i.go(e)};return f}function j(e){return{defaultValue:e}}var T=class{constructor(e){if(y(this,a,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(p(this,a).has(e))return p(this,a).get(e);if(void 0!==e.defaultValue)return e.defaultValue;throw Error("No value found for context")}set(e,t){p(this,a).set(e,t)}};a=new WeakMap;var N=new Set(["lazy","caseSensitive","path","id","index","children"]),M=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function O(e,t,r=[],a={}){return e.map((e,n)=>{let o=[...r,String(n)],i="string"==typeof e.id?e.id:o.join("-");if(R(!0!==e.index||!e.children,"Cannot specify children on an index route"),R(!a[i],`Found a route id collision on id "${i}".  Route id's must be globally unique within Data Router usages`),!0===e.index){let r={...e,...t(e),id:i};return a[i]=r,r}{let r={...e,...t(e),id:i,children:void 0};return a[i]=r,e.children&&(r.children=O(e.children,t,o,a)),r}})}function $(e,t,r="/"){return D(e,t,r,!1)}function D(e,t,r,a){let n=Y(("string"==typeof t?L(t):t).pathname||"/",r);if(null==n)return null;let o=function e(t,r=[],a=[],n=""){let o=(t,o,i)=>{var s,l;let u,c,d={relativePath:void 0===i?t.path||"":i,caseSensitive:!0===t.caseSensitive,childrenIndex:o,route:t};d.relativePath.startsWith("/")&&(R(d.relativePath.startsWith(n),`Absolute route path "${d.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),d.relativePath=d.relativePath.slice(n.length));let h=G([n,d.relativePath]),f=a.concat(d);t.children&&t.children.length>0&&(R(!0!==t.index,`Index routes must not have child routes. Please remove all child routes from route path "${h}".`),e(t.children,r,f,h)),(null!=t.path||t.index)&&r.push({path:h,score:(s=h,l=t.index,c=(u=s.split("/")).length,u.some(I)&&(c+=-2),l&&(c+=2),u.filter(e=>!I(e)).reduce((e,t)=>e+(F.test(t)?3:""===t?1:10),c)),routesMeta:f})};return t.forEach((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let r of function e(t){let r=t.split("/");if(0===r.length)return[];let[a,...n]=r,o=a.endsWith("?"),i=a.replace(/\?$/,"");if(0===n.length)return o?[i,""]:[i];let s=e(n.join("/")),l=[];return l.push(...s.map(e=>""===e?i:[i,e].join("/"))),o&&l.push(...s),l.map(e=>t.startsWith("/")&&""===e?"/":e)}(e.path))o(e,t,r);else o(e,t)}),r}(e);o.sort((e,t)=>{var r,a;return e.score!==t.score?t.score-e.score:(r=e.routesMeta.map(e=>e.childrenIndex),a=t.routesMeta.map(e=>e.childrenIndex),r.length===a.length&&r.slice(0,-1).every((e,t)=>e===a[t])?r[r.length-1]-a[a.length-1]:0)});let i=null;for(let e=0;null==i&&e<o.length;++e){let t=B(n);i=function(e,t,r=!1){let{routesMeta:a}=e,n={},o="/",i=[];for(let e=0;e<a.length;++e){let s=a[e],l=e===a.length-1,u="/"===o?t:t.slice(o.length)||"/",c=U({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},u),d=s.route;if(!c&&l&&r&&!a[a.length-1].route.index&&(c=U({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},u)),!c)return null;Object.assign(n,c.params),i.push({params:n,pathname:G([o,c.pathname]),pathnameBase:K(G([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=G([o,c.pathnameBase]))}return i}(o[e],t,a)}return i}function A(e,t){let{route:r,pathname:a,params:n}=e;return{id:r.id,pathname:a,params:n,data:t[r.id],handle:r.handle}}var F=/^:[\w-]+$/,I=e=>"*"===e;function H(e,t={}){let r=e;r.endsWith("*")&&"*"!==r&&!r.endsWith("/*")&&(S(!1,`Route path "${r}" will be treated as if it were "${r.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${r.replace(/\*$/,"/*")}".`),r=r.replace(/\*$/,"/*"));let a=r.startsWith("/")?"/":"",n=e=>null==e?"":"string"==typeof e?e:String(e);return a+r.split(/\/+/).map((e,r,a)=>{if(r===a.length-1&&"*"===e)return n(t["*"]);let o=e.match(/^:([\w-]+)(\??)$/);if(o){let[,e,r]=o,a=t[e];return R("?"===r||null!=a,`Missing ":${e}" param`),n(a)}return e.replace(/\?$/g,"")}).filter(e=>!!e).join("/")}function U(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=z(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let o=n[0],i=o.replace(/(.)\/+$/,"$1"),s=n.slice(1);return{params:a.reduce((e,{paramName:t,isOptional:r},a)=>{if("*"===t){let e=s[a]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}let n=s[a];return r&&!n?e[t]=void 0:e[t]=(n||"").replace(/%2F/g,"/"),e},{}),pathname:o,pathnameBase:i,pattern:e}}function z(e,t=!1,r=!0){S("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,r)=>(a.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":""!==e&&"/"!==e&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),a]}function B(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return S(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function Y(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&"/"!==a?null:e.slice(r)||"/"}function q(e,t="/"){var r;let a,{pathname:n,search:o="",hash:i=""}="string"==typeof e?L(e):e;return{pathname:n?n.startsWith("/")?n:(r=n,a=t.replace(/\/+$/,"").split("/"),r.split("/").forEach(e=>{".."===e?a.length>1&&a.pop():"."!==e&&a.push(e)}),a.length>1?a.join("/"):"/"):t,search:Z(o),hash:Q(i)}}function W(e,t,r,a){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(a)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function J(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function V(e){let t=J(e);return t.map((e,r)=>r===t.length-1?e.pathname:e.pathnameBase)}function X(e,t,r,a=!1){let n,o;"string"==typeof e?n=L(e):(R(!(n={...e}).pathname||!n.pathname.includes("?"),W("?","pathname","search",n)),R(!n.pathname||!n.pathname.includes("#"),W("#","pathname","hash",n)),R(!n.search||!n.search.includes("#"),W("#","search","hash",n)));let i=""===e||""===n.pathname,s=i?"/":n.pathname;if(null==s)o=r;else{let e=t.length-1;if(!a&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;n.pathname=t.join("/")}o=e>=0?t[e]:"/"}let l=q(n,o),u=s&&"/"!==s&&s.endsWith("/"),c=(i||"."===s)&&r.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}var G=e=>e.join("/").replace(/\/\/+/g,"/"),K=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Z=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Q=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"",ee=class{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}};function et(e,t){return new ee(e,"number"==typeof t?{status:t}:t)}var er=(e,t=302)=>{let r=t;"number"==typeof r?r={status:r}:void 0===r.status&&(r.status=302);let a=new Headers(r.headers);return a.set("Location",e),new Response(null,{...r,headers:a})},ea=(e,t)=>{let r=er(e,t);return r.headers.set("X-Remix-Reload-Document","true"),r},en=(e,t)=>{let r=er(e,t);return r.headers.set("X-Remix-Replace","true"),r},eo=class{constructor(e,t,r,a=!1){this.status=e,this.statusText=t||"",this.internal=a,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function ei(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}var es=["POST","PUT","PATCH","DELETE"],el=new Set(es),eu=new Set(["GET",...es]),ec=new Set([301,302,303,307,308]),ed=new Set([307,308]),eh={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ef={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},em={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},ep=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ey=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),ev="remix-router-transitions",eg=Symbol("ResetLoaderData");function ew(e){let t,r,a,n,o,i=e.window?e.window:"undefined"!=typeof window?window:void 0,s=void 0!==i&&void 0!==i.document&&void 0!==i.document.createElement;R(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let l=e.hydrationRouteProperties||[],u=e.mapRouteProperties||ey,c={},d=O(e.routes,u,void 0,c),h=e.basename||"/",f=e.dataStrategy||e$,m={unstable_middleware:!1,...e.future},p=null,y=new Set,v=null,g=null,w=null,b=null!=e.hydrationData,E=$(d,e.history.location,h),x=!1,C=null;if(null==E&&!e.patchRoutesOnNavigation){let t=e1(404,{pathname:e.history.location.pathname}),{matches:r,route:a}=e0(d);E=r,C={[a.id]:t}}if(E&&!e.hydrationData&&eI(E,d,e.history.location.pathname).active&&(E=null),E)if(E.some(e=>e.route.lazy))r=!1;else if(E.some(e=>e.route.loader)){let t=e.hydrationData?e.hydrationData.loaderData:null,a=e.hydrationData?e.hydrationData.errors:null;if(a){let e=E.findIndex(e=>void 0!==a[e.route.id]);r=E.slice(0,e+1).every(e=>!eP(e.route,t,a))}else r=E.every(e=>!eP(e.route,t,a))}else r=!0;else{r=!1,E=[];let t=eI(null,d,e.history.location.pathname);t.active&&t.matches&&(x=!0,E=t.matches)}let _={historyAction:e.history.action,location:e.history.location,matches:E,initialized:r,navigation:eh,restoreScrollPosition:null==e.hydrationData&&null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||C,fetchers:new Map,blockers:new Map},L="POP",k=!1,j=!1,N=new Map,M=null,F=!1,I=!1,H=new Set,U=new Map,z=0,B=-1,q=new Map,W=new Set,J=new Map,V=new Map,X=new Set,G=new Map,K=null;function Z(e,t={}){_={..._,...e};let r=[],a=[];_.fetchers.forEach((e,t)=>{"idle"===e.state&&(X.has(t)?r.push(t):a.push(t))}),X.forEach(e=>{_.fetchers.has(e)||U.has(e)||r.push(e)}),[...y].forEach(e=>e(_,{deletedFetchers:r,viewTransitionOpts:t.viewTransitionOpts,flushSync:!0===t.flushSync})),r.forEach(e=>ex(e)),a.forEach(e=>_.fetchers.delete(e))}function Q(r,a,{flushSync:n}={}){let o,i,s=null!=_.actionData&&null!=_.navigation.formMethod&&tr(_.navigation.formMethod)&&"loading"===_.navigation.state&&r.state?._isRedirect!==!0;o=a.actionData?Object.keys(a.actionData).length>0?a.actionData:null:s?_.actionData:null;let l=a.loaderData?eK(_.loaderData,a.loaderData,a.matches||[],a.errors):_.loaderData,u=_.blockers;u.size>0&&(u=new Map(u)).forEach((e,t)=>u.set(t,em));let c=!0===k||null!=_.navigation.formMethod&&tr(_.navigation.formMethod)&&r.state?._isRedirect!==!0;if(t&&(d=t,t=void 0),F||"POP"===L||("PUSH"===L?e.history.push(r,r.state):"REPLACE"===L&&e.history.replace(r,r.state)),"POP"===L){let e=N.get(_.location.pathname);e&&e.has(r.pathname)?i={currentLocation:_.location,nextLocation:r}:N.has(r.pathname)&&(i={currentLocation:r,nextLocation:_.location})}else if(j){let e=N.get(_.location.pathname);e?e.add(r.pathname):(e=new Set([r.pathname]),N.set(_.location.pathname,e)),i={currentLocation:_.location,nextLocation:r}}Z({...a,actionData:o,loaderData:l,historyAction:L,location:r,initialized:!0,navigation:eh,revalidation:"idle",restoreScrollPosition:eF(r,a.matches||_.matches),preventScrollReset:c,blockers:u},{viewTransitionOpts:i,flushSync:!0===n}),L="POP",k=!1,j=!1,F=!1,I=!1,K?.resolve(),K=null}async function ee(t,r){if("number"==typeof t)return void e.history.go(t);let{path:a,submission:n,error:o}=eS(!1,eR(_.location,_.matches,h,t,r?.fromRouteId,r?.relative),r),i=_.location,s=P(_.location,a,r&&r.state);s={...s,...e.history.encodeLocation(s)};let l=r&&null!=r.replace?r.replace:void 0,u="PUSH";!0===l?u="REPLACE":!1===l||null!=n&&tr(n.formMethod)&&n.formAction===_.location.pathname+_.location.search&&(u="REPLACE");let c=r&&"preventScrollReset"in r?!0===r.preventScrollReset:void 0,d=!0===(r&&r.flushSync),f=eO({currentLocation:i,nextLocation:s,historyAction:u});if(f)return void eM(f,{state:"blocked",location:s,proceed(){eM(f,{state:"proceeding",proceed:void 0,reset:void 0,location:s}),ee(t,r)},reset(){let e=new Map(_.blockers);e.set(f,em),Z({blockers:e})}});await et(u,s,{submission:n,pendingError:o,preventScrollReset:c,replace:r&&r.replace,enableViewTransition:r&&r.viewTransition,flushSync:d})}async function et(r,a,o){var i,s,l,u;let c;n&&n.abort(),n=null,L=r,F=!0===(o&&o.startUninterruptedRevalidation),i=_.location,s=_.matches,v&&w&&(v[eA(i,s)]=w()),k=!0===(o&&o.preventScrollReset),j=!0===(o&&o.enableViewTransition);let f=t||d,m=o&&o.overrideNavigation,p=o?.initialHydration&&_.matches&&_.matches.length>0&&!x?_.matches:$(f,a,h),y=!0===(o&&o.flushSync);if(p&&_.initialized&&!I&&(l=_.location,u=a,l.pathname===u.pathname&&l.search===u.search&&(""===l.hash?""!==u.hash:l.hash===u.hash||""!==u.hash||!1))&&!(o&&o.submission&&tr(o.submission.formMethod)))return void Q(a,{matches:p},{flushSync:y});let g=eI(p,f,a.pathname);if(g.active&&g.matches&&(p=g.matches),!p){let{error:e,notFoundMatches:t,route:r}=eD(a.pathname);Q(a,{matches:t,loaderData:{},errors:{[r.id]:e}},{flushSync:y});return}n=new AbortController;let b=eW(e.history,a,n.signal,o&&o.submission),E=new T(e.unstable_getContext?await e.unstable_getContext():void 0);if(o&&o.pendingError)c=[eQ(p).route.id,{type:"error",error:o.pendingError}];else if(o&&o.submission&&tr(o.submission.formMethod)){let t=await er(b,a,o.submission,p,E,g.active,o&&!0===o.initialHydration,{replace:o.replace,flushSync:y});if(t.shortCircuited)return;if(t.pendingActionResult){let[e,r]=t.pendingActionResult;if(e5(r)&&ei(r.error)&&404===r.error.status){n=null,Q(a,{matches:t.matches,loaderData:{},errors:{[e]:r.error}});return}}p=t.matches||p,c=t.pendingActionResult,m=ti(a,o.submission),y=!1,g.active=!1,b=eW(e.history,b.url,b.signal)}let{shortCircuited:R,matches:S,loaderData:C,errors:P}=await ea(b,a,p,E,g.active,m,o&&o.submission,o&&o.fetcherSubmission,o&&o.replace,o&&!0===o.initialHydration,y,c);R||(n=null,Q(a,{matches:S||p,...eZ(c),loaderData:C,errors:P}))}async function er(e,t,r,a,n,o,i,s={}){var d;let f;if(eg(),Z({navigation:{state:"submitting",location:t,formMethod:(d=r).formMethod,formAction:d.formAction,formEncType:d.formEncType,formData:d.formData,json:d.json,text:d.text}},{flushSync:!0===s.flushSync}),o){let r=await ez(a,t.pathname,e.signal);if("aborted"===r.type)return{shortCircuited:!0};if("error"===r.type){let e=eQ(r.partialMatches).route.id;return{matches:r.partialMatches,pendingActionResult:[e,{type:"error",error:r.error}]}}if(r.matches)a=r.matches;else{let{notFoundMatches:e,error:r,route:a}=eD(t.pathname);return{matches:e,pendingActionResult:[a.id,{type:"error",error:r}]}}}let m=tn(a,t);if(m.route.action||m.route.lazy){let t=eH(u,c,e,a,m,i?[]:l,n),r=await eu(e,t,n,null);if(!(f=r[m.route.id])){for(let e of a)if(r[e.route.id]){f=r[e.route.id];break}}if(e.signal.aborted)return{shortCircuited:!0}}else f={type:"error",error:e1(405,{method:e.method,pathname:t.pathname,routeId:m.route.id})};if(e9(f)){let t;return t=s&&null!=s.replace?s.replace:eq(f.response.headers.get("Location"),new URL(e.url),h)===_.location.pathname+_.location.search,await el(e,f,!0,{submission:r,replace:t}),{shortCircuited:!0}}if(e5(f)){let e=eQ(a,m.route.id);return!0!==(s&&s.replace)&&(L="PUSH"),{matches:a,pendingActionResult:[e.route.id,f]}}return{matches:a,pendingActionResult:[m.route.id,f]}}async function ea(r,a,o,i,s,f,m,p,y,v,g,w){let b=f||ti(a,m),E=m||p||to(b),x=!F&&!v;if(s){if(x){let e=en(w);Z({navigation:b,...void 0!==e?{actionData:e}:{}},{flushSync:g})}let e=await ez(o,a.pathname,r.signal);if("aborted"===e.type)return{shortCircuited:!0};if("error"===e.type){let t=eQ(e.partialMatches).route.id;return{matches:e.partialMatches,loaderData:{},errors:{[t]:e.error}}}if(e.matches)o=e.matches;else{let{error:e,notFoundMatches:t,route:r}=eD(a.pathname);return{matches:t,loaderData:{},errors:{[r.id]:e}}}}let R=t||d,{dsMatches:S,revalidatingFetchers:C}=eC(r,i,u,c,e.history,_,o,E,a,v?[]:l,!0===v,I,H,X,J,W,R,h,w);if(B=++z,!e.dataStrategy&&!S.some(e=>e.shouldLoad)&&0===C.length){let e=ej();return Q(a,{matches:o,loaderData:{},errors:w&&e5(w[1])?{[w[0]]:w[1].error}:null,...eZ(w),...e?{fetchers:new Map(_.fetchers)}:{}},{flushSync:g}),{shortCircuited:!0}}if(x){let e={};if(!s){e.navigation=b;let t=en(w);void 0!==t&&(e.actionData=t)}C.length>0&&(C.forEach(e=>{let t=_.fetchers.get(e.key),r=ts(void 0,t?t.data:void 0);_.fetchers.set(e.key,r)}),e.fetchers=new Map(_.fetchers)),Z(e,{flushSync:g})}C.forEach(e=>{e_(e.key),e.controller&&U.set(e.key,e.controller)});let P=()=>C.forEach(e=>e_(e.key));n&&n.signal.addEventListener("abort",P);let{loaderResults:L,fetcherResults:k}=await ec(S,C,r,i);if(r.signal.aborted)return{shortCircuited:!0};n&&n.signal.removeEventListener("abort",P),C.forEach(e=>U.delete(e.key));let j=e2(L);if(j)return await el(r,j.result,!0,{replace:y}),{shortCircuited:!0};if(j=e2(k))return W.add(j.key),await el(r,j.result,!0,{replace:y}),{shortCircuited:!0};let{loaderData:T,errors:N}=eG(_,o,L,w,C,k);v&&_.errors&&(N={..._.errors,...N});let M=ej(),O=eT(B);return{matches:o,loaderData:T,errors:N,...M||O||C.length>0?{fetchers:new Map(_.fetchers)}:{}}}function en(e){if(e&&!e5(e[1]))return{[e[0]]:e[1].data};if(_.actionData)if(0===Object.keys(_.actionData).length)return null;else return _.actionData}async function eo(r,a,o,i,s,f,m,p,y,v){var g,w;function b(e){if(!e.route.action&&!e.route.lazy){let e=e1(405,{method:v.formMethod,pathname:o,routeId:a});return eb(r,a,e,{flushSync:p}),!0}return!1}if(eg(),J.delete(r),!m&&b(i))return;let E=_.fetchers.get(r);ew(r,(g=v,w=E,{state:"submitting",formMethod:g.formMethod,formAction:g.formAction,formEncType:g.formEncType,formData:g.formData,json:g.json,text:g.text,data:w?w.data:void 0}),{flushSync:p});let x=new AbortController,S=eW(e.history,o,x.signal,v);if(m){let e=await ez(s,o,S.signal,r);if("aborted"===e.type)return;if("error"===e.type)return void eb(r,a,e.error,{flushSync:p});if(!e.matches)return void eb(r,a,e1(404,{pathname:o}),{flushSync:p});if(b(i=tn(s=e.matches,o)))return}U.set(r,x);let C=z,P=eH(u,c,S,s,i,l,f),k=(await eu(S,P,f,r))[i.route.id];if(S.signal.aborted){U.get(r)===x&&U.delete(r);return}if(X.has(r)){if(e9(k)||e5(k))return void ew(r,tl(void 0))}else{if(e9(k))return(U.delete(r),B>C)?void ew(r,tl(void 0)):(W.add(r),ew(r,ts(v)),el(S,k,!1,{fetcherSubmission:v,preventScrollReset:y}));if(e5(k))return void eb(r,a,k.error)}let j=_.navigation.location||_.location,T=eW(e.history,j,x.signal),N=t||d,M="idle"!==_.navigation.state?$(N,_.navigation.location,h):_.matches;R(M,"Didn't find any matches after fetcher action");let O=++z;q.set(r,O);let D=ts(v,k.data);_.fetchers.set(r,D);let{dsMatches:A,revalidatingFetchers:F}=eC(T,f,u,c,e.history,_,M,v,j,l,!1,I,H,X,J,W,N,h,[i.route.id,k]);F.filter(e=>e.key!==r).forEach(e=>{let t=e.key,r=_.fetchers.get(t),a=ts(void 0,r?r.data:void 0);_.fetchers.set(t,a),e_(t),e.controller&&U.set(t,e.controller)}),Z({fetchers:new Map(_.fetchers)});let Y=()=>F.forEach(e=>e_(e.key));x.signal.addEventListener("abort",Y);let{loaderResults:V,fetcherResults:G}=await ec(A,F,T,f);if(x.signal.aborted)return;x.signal.removeEventListener("abort",Y),q.delete(r),U.delete(r),F.forEach(e=>U.delete(e.key));let K=e2(V);if(K)return el(T,K.result,!1,{preventScrollReset:y});if(K=e2(G))return W.add(K.key),el(T,K.result,!1,{preventScrollReset:y});let{loaderData:ee,errors:et}=eG(_,M,V,void 0,F,G);if(_.fetchers.has(r)){let e=tl(k.data);_.fetchers.set(r,e)}eT(O),"loading"===_.navigation.state&&O>B?(R(L,"Expected pending action"),n&&n.abort(),Q(_.navigation.location,{matches:M,loaderData:ee,errors:et,fetchers:new Map(_.fetchers)})):(Z({errors:et,loaderData:eK(_.loaderData,ee,M,et),fetchers:new Map(_.fetchers)}),I=!1)}async function es(t,r,a,n,o,i,s,d,h,f){let m=_.fetchers.get(t);ew(t,ts(f,m?m.data:void 0),{flushSync:d});let p=new AbortController,y=eW(e.history,a,p.signal);if(s){let e=await ez(o,a,y.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void eb(t,r,e.error,{flushSync:d});if(!e.matches)return void eb(t,r,e1(404,{pathname:a}),{flushSync:d});n=tn(o=e.matches,a)}U.set(t,p);let v=z,g=eH(u,c,y,o,n,l,i),w=(await eu(y,g,i,t))[n.route.id];if(U.get(t)===p&&U.delete(t),!y.signal.aborted){if(X.has(t))return void ew(t,tl(void 0));if(e9(w))if(B>v)return void ew(t,tl(void 0));else{W.add(t),await el(y,w,!1,{preventScrollReset:h});return}if(e5(w))return void eb(t,r,w.error);ew(t,tl(w.data))}}async function el(t,r,a,{submission:o,fetcherSubmission:l,preventScrollReset:u,replace:c}={}){r.response.headers.has("X-Remix-Revalidate")&&(I=!0);let d=r.response.headers.get("Location");R(d,"Expected a Location header on the redirect Response"),d=eq(d,new URL(t.url),h);let f=P(_.location,d,{_isRedirect:!0});if(s){let t=!1;if(r.response.headers.has("X-Remix-Reload-Document"))t=!0;else if(ep.test(d)){let r=e.history.createURL(d);t=r.origin!==i.location.origin||null==Y(r.pathname,h)}if(t)return void(c?i.location.replace(d):i.location.assign(d))}n=null;let m=!0===c||r.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:p,formAction:y,formEncType:v}=_.navigation;!o&&!l&&p&&y&&v&&(o=to(_.navigation));let g=o||l;if(ed.has(r.response.status)&&g&&tr(g.formMethod))await et(m,f,{submission:{...g,formAction:d},preventScrollReset:u||k,enableViewTransition:a?j:void 0});else{let e=ti(f,o);await et(m,f,{overrideNavigation:e,fetcherSubmission:l,preventScrollReset:u||k,enableViewTransition:a?j:void 0})}}async function eu(e,t,r,a){let n,o={};try{n=await eU(f,e,t,a,r,!1)}catch(e){return t.filter(e=>e.shouldLoad).forEach(t=>{o[t.route.id]={type:"error",error:e}}),o}for(let[r,a]of Object.entries(n))if(e3(a)){let n=a.result;o[r]={type:"redirect",response:eY(n,e,r,t,h)}}else o[r]=await eB(a);return o}async function ec(e,t,r,a){let n=eu(r,e,a,null),o=Promise.all(t.map(async e=>{if(!e.matches||!e.match||!e.request||!e.controller)return Promise.resolve({[e.key]:{type:"error",error:e1(404,{pathname:e.path})}});{let t=(await eu(e.request,e.matches,a,e.key))[e.match.route.id];return{[e.key]:t}}}));return{loaderResults:await n,fetcherResults:(await o).reduce((e,t)=>Object.assign(e,t),{})}}function eg(){I=!0,J.forEach((e,t)=>{U.has(t)&&H.add(t),e_(t)})}function ew(e,t,r={}){_.fetchers.set(e,t),Z({fetchers:new Map(_.fetchers)},{flushSync:!0===(r&&r.flushSync)})}function eb(e,t,r,a={}){let n=eQ(_.matches,t);ex(e),Z({errors:{[n.route.id]:r},fetchers:new Map(_.fetchers)},{flushSync:!0===(a&&a.flushSync)})}function eE(e){return V.set(e,(V.get(e)||0)+1),X.has(e)&&X.delete(e),_.fetchers.get(e)||ef}function ex(e){let t=_.fetchers.get(e);U.has(e)&&!(t&&"loading"===t.state&&q.has(e))&&e_(e),J.delete(e),q.delete(e),W.delete(e),X.delete(e),H.delete(e),_.fetchers.delete(e)}function e_(e){let t=U.get(e);t&&(t.abort(),U.delete(e))}function ek(e){for(let t of e){let e=tl(eE(t).data);_.fetchers.set(t,e)}}function ej(){let e=[],t=!1;for(let r of W){let a=_.fetchers.get(r);R(a,`Expected fetcher: ${r}`),"loading"===a.state&&(W.delete(r),e.push(r),t=!0)}return ek(e),t}function eT(e){let t=[];for(let[r,a]of q)if(a<e){let e=_.fetchers.get(r);R(e,`Expected fetcher: ${r}`),"loading"===e.state&&(e_(r),q.delete(r),t.push(r))}return ek(t),t.length>0}function eN(e){_.blockers.delete(e),G.delete(e)}function eM(e,t){let r=_.blockers.get(e)||em;R("unblocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"proceeding"===t.state||"blocked"===r.state&&"unblocked"===t.state||"proceeding"===r.state&&"unblocked"===t.state,`Invalid blocker state transition: ${r.state} -> ${t.state}`);let a=new Map(_.blockers);a.set(e,t),Z({blockers:a})}function eO({currentLocation:e,nextLocation:t,historyAction:r}){if(0===G.size)return;G.size>1&&S(!1,"A router only supports one blocker at a time");let a=Array.from(G.entries()),[n,o]=a[a.length-1],i=_.blockers.get(n);if((!i||"proceeding"!==i.state)&&o({currentLocation:e,nextLocation:t,historyAction:r}))return n}function eD(e){let r=e1(404,{pathname:e}),{matches:a,route:n}=e0(t||d);return{notFoundMatches:a,route:n,error:r}}function eA(e,t){return g&&g(e,t.map(e=>A(e,_.loaderData)))||e.key}function eF(e,t){if(v){let r=v[eA(e,t)];if("number"==typeof r)return r}return null}function eI(t,r,a){if(e.patchRoutesOnNavigation){if(!t)return{active:!0,matches:D(r,a,h,!0)||[]};else if(Object.keys(t[0].params).length>0)return{active:!0,matches:D(r,a,h,!0)}}return{active:!1,matches:null}}async function ez(r,a,n,o){if(!e.patchRoutesOnNavigation)return{type:"success",matches:r};let i=r;for(;;){let r=null==t,s=t||d,l=c;try{await e.patchRoutesOnNavigation({signal:n,path:a,matches:i,fetcherKey:o,patch:(e,t)=>{n.aborted||eL(e,t,s,l,u)}})}catch(e){return{type:"error",error:e,partialMatches:i}}finally{r&&!n.aborted&&(d=[...d])}if(n.aborted)return{type:"aborted"};let f=$(s,a,h);if(f)return{type:"success",matches:f};let m=D(s,a,h,!0);if(!m||i.length===m.length&&i.every((e,t)=>e.route.id===m[t].route.id))return{type:"success",matches:null};i=m}}return a={get basename(){return h},get future(){return m},get state(){return _},get routes(){return d},get window(){return i},initialize:function(){if(p=e.history.listen(({action:t,location:r,delta:a})=>{if(o){o(),o=void 0;return}S(0===G.size||null!=a,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let n=eO({currentLocation:_.location,nextLocation:r,historyAction:t});if(n&&null!=a){let t=new Promise(e=>{o=e});e.history.go(-1*a),eM(n,{state:"blocked",location:r,proceed(){eM(n,{state:"proceeding",proceed:void 0,reset:void 0,location:r}),t.then(()=>e.history.go(a))},reset(){let e=new Map(_.blockers);e.set(n,em),Z({blockers:e})}});return}return et(t,r)}),s){var t=i,r=N;try{let e=t.sessionStorage.getItem(ev);if(e){let t=JSON.parse(e);for(let[e,a]of Object.entries(t||{}))a&&Array.isArray(a)&&r.set(e,new Set(a||[]))}}catch(e){}let e=()=>(function(e,t){if(t.size>0){let r={};for(let[e,a]of t)r[e]=[...a];try{e.sessionStorage.setItem(ev,JSON.stringify(r))}catch(e){S(!1,`Failed to save applied view transitions in sessionStorage (${e}).`)}}})(i,N);i.addEventListener("pagehide",e),M=()=>i.removeEventListener("pagehide",e)}return _.initialized||et("POP",_.location,{initialHydration:!0}),a},subscribe:function(e){return y.add(e),()=>y.delete(e)},enableScrollRestoration:function(e,t,r){if(v=e,w=t,g=r||null,!b&&_.navigation===eh){b=!0;let e=eF(_.location,_.matches);null!=e&&Z({restoreScrollPosition:e})}return()=>{v=null,w=null,g=null}},navigate:ee,fetch:async function r(r,a,n,o){e_(r);let i=!0===(o&&o.flushSync),s=t||d,l=eR(_.location,_.matches,h,n,a,o?.relative),u=$(s,l,h),c=eI(u,s,l);if(c.active&&c.matches&&(u=c.matches),!u)return void eb(r,a,e1(404,{pathname:l}),{flushSync:i});let{path:f,submission:m,error:p}=eS(!0,l,o);if(p)return void eb(r,a,p,{flushSync:i});let y=tn(u,f),v=new T(e.unstable_getContext?await e.unstable_getContext():void 0),g=!0===(o&&o.preventScrollReset);if(m&&tr(m.formMethod))return void await eo(r,a,f,y,u,v,c.active,i,g,m);J.set(r,{routeId:a,path:f}),await es(r,a,f,y,u,v,c.active,i,g,m)},revalidate:function(){let e,t,r;K||(K={promise:r=new Promise((a,n)=>{e=async e=>{a(e);try{await r}catch(e){}},t=async e=>{n(e);try{await r}catch(e){}}}),resolve:e,reject:t}),eg(),Z({revalidation:"loading"});let a=K.promise;return"submitting"===_.navigation.state||("idle"===_.navigation.state?et(_.historyAction,_.location,{startUninterruptedRevalidation:!0}):et(L||_.historyAction,_.navigation.location,{overrideNavigation:_.navigation,enableViewTransition:!0===j})),a},createHref:t=>e.history.createHref(t),encodeLocation:t=>e.history.encodeLocation(t),getFetcher:eE,deleteFetcher:function(e){let t=(V.get(e)||0)-1;t<=0?(V.delete(e),X.add(e)):V.set(e,t),Z({fetchers:new Map(_.fetchers)})},dispose:function(){p&&p(),M&&M(),y.clear(),n&&n.abort(),_.fetchers.forEach((e,t)=>ex(t)),_.blockers.forEach((e,t)=>eN(t))},getBlocker:function(e,t){let r=_.blockers.get(e)||em;return G.get(e)!==t&&G.set(e,t),r},deleteBlocker:eN,patchRoutes:function(e,r){let a=null==t;eL(e,r,t||d,c,u),a&&(d=[...d],Z({}))},_internalFetchControllers:U,_internalSetRoutes:function(e){t=O(e,u,void 0,c={})}}}function eb(e,t){R(e.length>0,"You must provide a non-empty routes array to createStaticHandler");let r={},a=(t?t.basename:null)||"/",n=t?.mapRouteProperties||ey,o=O(e,n,void 0,r);async function i(e,{requestContext:t,filterMatchesToLoad:s,skipLoaderErrorBubbling:u,skipRevalidation:c,dataStrategy:d,unstable_respond:h}={}){let f=new URL(e.url),m=e.method,p=P("",_(f),null,"default"),y=$(o,p,a);if(t=null!=t?t:new T,tt(m)||"HEAD"===m){if(!y){let e=e1(404,{pathname:p.pathname}),{matches:t,route:r}=e0(o),n={basename:a,location:p,matches:t,loaderData:{},actionData:null,errors:{[r.id]:e},statusCode:e.status,loaderHeaders:{},actionHeaders:{}};return h?h(n):n}}else{let e=e1(405,{method:m}),{matches:t,route:r}=e0(o),n={basename:a,location:p,matches:t,loaderData:{},actionData:null,errors:{[r.id]:e},statusCode:e.status,loaderHeaders:{},actionHeaders:{}};return h?h(n):n}if(h&&y.some(e=>e.route.unstable_middleware||"object"==typeof e.route.lazy&&e.route.lazy.unstable_middleware)){R(t instanceof T,"When using middleware in `staticHandler.query()`, any provided `requestContext` must be an instance of `unstable_RouterContextProvider`");try{let i;await eM(y,r,n);let f=await eD({request:e,matches:y,params:y[0].params,context:t},!0,async()=>{let r=await l(e,p,y,t,d||null,!0===u,null,s||null,!0===c);return e7(r)?r:(i={location:p,basename:a,...r},await h(i))},async(e,t)=>{if(e7(e))return e;if(i)return t in i.loaderData&&(i.loaderData[t]=void 0),h(eE(o,i,e,eQ(y,t).route.id));{let t=y.findIndex(e=>e.route.loader),r=t>=0?eQ(y,y[t].route.id):eQ(y);return h({matches:y,location:p,basename:a,loaderData:{},actionData:null,errors:{[r.route.id]:e},statusCode:ei(e)?e.status:500,actionHeaders:{},loaderHeaders:{}})}});return R(e7(f),"Expected a response in query()"),f}catch(e){if(e7(e))return e;throw e}}let v=await l(e,p,y,t,d||null,!0===u,null,s||null,!0===c);return e7(v)?v:{location:p,basename:a,...v}}async function s(e,{routeId:t,requestContext:i,dataStrategy:u,unstable_respond:c}={}){let d=new URL(e.url),h=e.method,f=P("",_(d),null,"default"),m=$(o,f,a);if(i=null!=i?i:new T,tt(h)||"HEAD"===h||"OPTIONS"===h){if(!m)throw e1(404,{pathname:f.pathname})}else throw e1(405,{method:h});let p=t?m.find(e=>e.route.id===t):tn(m,f);if(t&&!p)throw e1(403,{pathname:f.pathname,routeId:t});if(!p)throw e1(404,{pathname:f.pathname});if(c&&m.some(e=>e.route.unstable_middleware||"object"==typeof e.route.lazy&&e.route.lazy.unstable_middleware))return R(i instanceof T,"When using middleware in `staticHandler.queryRoute()`, any provided `requestContext` must be an instance of `unstable_RouterContextProvider`"),await eM(m,r,n),await eD({request:e,matches:m,params:m[0].params,context:i},!0,async()=>{let t=await l(e,f,m,i,u||null,!1,p,null,!1);if(e7(t))return c(t);let r=t.errors?Object.values(t.errors)[0]:void 0;if(void 0!==r)throw r;let a=t.actionData?Object.values(t.actionData)[0]:Object.values(t.loaderData)[0];return"string"==typeof a?new Response(a):Response.json(a)},e=>e7(e)?c(e):new Response(String(e),{status:500,statusText:"Unexpected Server Error"}));let y=await l(e,f,m,i,u||null,!1,p,null,!1);if(e7(y))return y;let v=y.errors?Object.values(y.errors)[0]:void 0;if(void 0!==v)throw v;return y.actionData?Object.values(y.actionData)[0]:y.loaderData?Object.values(y.loaderData)[0]:void 0}async function l(e,t,r,a,n,o,i,s,l){R(e.signal,"query()/queryRoute() requests must contain an AbortController signal");try{if(tr(e.method))return await u(e,r,i||tn(r,t),a,n,o,null!=i,s,l);let d=await c(e,r,a,n,o,i,s);return e7(d)?d:{...d,actionData:null,actionHeaders:{}}}catch(e){var d;if(null!=(d=e)&&"object"==typeof d&&"type"in d&&"result"in d&&("data"===d.type||"error"===d.type)&&e7(e.result)){if("error"===e.type)throw e.result;return e.result}if(te(e))return e;throw e}}async function u(e,t,a,o,i,s,l,u,h){let f;if(a.route.action||a.route.lazy){let s=eH(n,r,e,t,a,[],o);f=(await d(e,s,l,o,i))[a.route.id],e.signal.aborted&&ex(e,l)}else{let t=e1(405,{method:e.method,pathname:new URL(e.url).pathname,routeId:a.route.id});if(l)throw t;f={type:"error",error:t}}if(e9(f))throw new Response(null,{status:f.response.status,headers:{Location:f.response.headers.get("Location")}});if(l){if(e5(f))throw f.error;return{matches:[a],loaderData:{},actionData:{[a.route.id]:f.data},errors:null,statusCode:200,loaderHeaders:{},actionHeaders:{}}}if(h)if(!e5(f))return{actionData:{[a.route.id]:f.data},actionHeaders:f.headers?{[a.route.id]:f.headers}:{},matches:t,loaderData:{},errors:null,statusCode:f.statusCode||200,loaderHeaders:{}};else{let e=s?a:eQ(t,a.route.id);return{statusCode:ei(f.error)?f.error.status:null!=f.statusCode?f.statusCode:500,actionData:null,actionHeaders:{...f.headers?{[a.route.id]:f.headers}:{}},matches:t,loaderData:{},errors:{[e.route.id]:f.error},loaderHeaders:{}}}let m=new Request(e.url,{headers:e.headers,redirect:e.redirect,signal:e.signal});if(e5(f)){let e=s?a:eQ(t,a.route.id);return{...await c(m,t,o,i,s,null,u,[e.route.id,f]),statusCode:ei(f.error)?f.error.status:null!=f.statusCode?f.statusCode:500,actionData:null,actionHeaders:{...f.headers?{[a.route.id]:f.headers}:{}}}}return{...await c(m,t,o,i,s,null,u),actionData:{[a.route.id]:f.data},...f.statusCode?{statusCode:f.statusCode}:{},actionHeaders:f.headers?{[a.route.id]:f.headers}:{}}}async function c(e,t,a,o,i,s,l,u){let c,h=null!=s;if(h&&!s?.route.loader&&!s?.route.lazy)throw e1(400,{method:e.method,pathname:new URL(e.url).pathname,routeId:s?.route.id});if(s)c=eH(n,r,e,t,s,[],a);else{let o=u&&e5(u[1])?t.findIndex(e=>e.route.id===u[0])-1:void 0;c=t.map((t,i)=>null!=o&&i>o?eI(n,r,e,t,[],a,!1):eI(n,r,e,t,[],a,null!=(t.route.loader||t.route.lazy)&&(!l||l(t))))}if(!o&&!c.some(e=>e.shouldLoad))return{matches:t,loaderData:{},errors:u&&e5(u[1])?{[u[0]]:u[1].error}:null,statusCode:200,loaderHeaders:{}};let f=await d(e,c,h,a,o);return e.signal.aborted&&ex(e,h),{...eX(t,f,u,!0,i),matches:t}}async function d(e,t,r,n,o){let i=await eU(o||eO,e,t,null,n,!0),s={};return await Promise.all(t.map(async n=>{if(!(n.route.id in i))return;let o=i[n.route.id];if(e3(o))throw eY(o.result,e,n.route.id,t,a);if(e7(o.result)&&r)throw o;s[n.route.id]=await eB(o)})),s}return{dataRoutes:o,query:i,queryRoute:s}}function eE(e,t,r,a){let n=a||t._deepestRenderedBoundaryId||e[0].id;return{...t,statusCode:ei(r)?r.status:500,errors:{[n]:r}}}function ex(e,t){if(void 0!==e.signal.reason)throw e.signal.reason;throw Error(`${t?"queryRoute":"query"}() call aborted without an \`AbortSignal.reason\`: ${e.method} ${e.url}`)}function eR(e,t,r,a,n,o){let i,s;if(n){for(let e of(i=[],t))if(i.push(e),e.route.id===n){s=e;break}}else i=t,s=t[t.length-1];let l=X(a||".",V(i),Y(e.pathname,r)||e.pathname,"path"===o);if(null==a&&(l.search=e.search,l.hash=e.hash),(null==a||""===a||"."===a)&&s){let e=ta(l.search);if(s.route.index&&!e)l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index";else if(!s.route.index&&e){let e=new URLSearchParams(l.search),t=e.getAll("index");e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let r=e.toString();l.search=r?`?${r}`:""}}return"/"!==r&&(l.pathname="/"===l.pathname?r:G([r,l.pathname])),_(l)}function eS(e,t,r){let a,n;if(!r||!(null!=r&&("formData"in r&&null!=r.formData||"body"in r&&void 0!==r.body)))return{path:t};if(r.formMethod&&!tt(r.formMethod))return{path:t,error:e1(405,{method:r.formMethod})};let o=()=>({path:t,error:e1(400,{type:"invalid-body"})}),i=(r.formMethod||"get").toUpperCase(),s=e4(t);if(void 0!==r.body){if("text/plain"===r.formEncType){if(!tr(i))return o();let e="string"==typeof r.body?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((e,[t,r])=>`${e}${t}=${r}
`,""):String(r.body);return{path:t,submission:{formMethod:i,formAction:s,formEncType:r.formEncType,formData:void 0,json:void 0,text:e}}}else if("application/json"===r.formEncType){if(!tr(i))return o();try{let e="string"==typeof r.body?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:i,formAction:s,formEncType:r.formEncType,formData:void 0,json:e,text:void 0}}}catch(e){return o()}}}if(R("function"==typeof FormData,"FormData is not available in this environment"),r.formData)a=eJ(r.formData),n=r.formData;else if(r.body instanceof FormData)a=eJ(r.body),n=r.body;else if(r.body instanceof URLSearchParams)n=eV(a=r.body);else if(null==r.body)a=new URLSearchParams,n=new FormData;else try{a=new URLSearchParams(r.body),n=eV(a)}catch(e){return o()}let l={formMethod:i,formAction:s,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:n,json:void 0,text:void 0};if(tr(l.formMethod))return{path:t,submission:l};let u=L(t);return e&&u.search&&ta(u.search)&&a.append("index",""),u.search=`?${a}`,{path:_(u),submission:l}}function eC(e,t,r,a,n,o,i,s,l,u,c,d,h,f,m,p,y,v,g){let w,b=g?e5(g[1])?g[1].error:g[1].data:void 0,E=n.createURL(o.location),x=n.createURL(l);if(c&&o.errors){let e=Object.keys(o.errors)[0];w=i.findIndex(t=>t.route.id===e)}else if(g&&e5(g[1])){let e=g[0];w=i.findIndex(t=>t.route.id===e)-1}let R=g?g[1].statusCode:void 0,S=R&&R>=400,C={currentUrl:E,currentParams:o.matches[0]?.params||{},nextUrl:x,nextParams:i[0].params,...s,actionResult:b,actionStatus:R},P=i.map((n,i)=>{var s,l,h,f,m;let p,y,v,{route:g}=n,b=null;if(null!=w&&i>w?b=!1:g.lazy?b=!0:null==g.loader?b=!1:c?b=eP(g,o.loaderData,o.errors):(s=o.loaderData,l=o.matches[i],h=n,p=!l||h.route.id!==l.route.id,y=!s.hasOwnProperty(h.route.id),(p||y)&&(b=!0)),null!==b)return eI(r,a,e,n,u,t,b);let R=!S&&(d||E.pathname+E.search===x.pathname+x.search||E.search!==x.search||(f=o.matches[i],m=n,v=f.route.path,f.pathname!==m.pathname||null!=v&&v.endsWith("*")&&f.params["*"]!==m.params["*"])),P={...C,defaultShouldRevalidate:R},_=e_(n,P);return eI(r,a,e,n,u,t,_,P)}),_=[];return m.forEach((e,s)=>{if(c||!i.some(t=>t.route.id===e.routeId)||f.has(s))return;let l=$(y,e.path,v);if(!l)return void _.push({key:s,routeId:e.routeId,path:e.path,matches:null,match:null,request:null,controller:null});if(p.has(s))return;let m=o.fetchers.get(s),g=tn(l,e.path),w=new AbortController,b=eW(n,e.path,w.signal),E=null;if(h.has(s))h.delete(s),E=eH(r,a,b,l,g,u,t);else if(m&&"idle"!==m.state&&void 0===m.data)d&&(E=eH(r,a,b,l,g,u,t));else{let e={...C,defaultShouldRevalidate:!S&&d};e_(g,e)&&(E=eH(r,a,b,l,g,u,t,e))}E&&_.push({key:s,routeId:e.routeId,path:e.path,matches:E,match:g,request:b,controller:w})}),{dsMatches:P,revalidatingFetchers:_}}function eP(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let a=null!=t&&void 0!==t[e.id],n=null!=r&&void 0!==r[e.id];return(!!a||!n)&&("function"==typeof e.loader&&!0===e.loader.hydrate||!a&&!n)}function e_(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if("boolean"==typeof r)return r}return t.defaultShouldRevalidate}function eL(e,t,r,a,n){let o;if(e){let t=a[e];R(t,`No route found to patch children into: routeId = ${e}`),t.children||(t.children=[]),o=t.children}else o=r;let i=O(t.filter(e=>!o.some(t=>(function e(t,r){return"id"in t&&"id"in r&&t.id===r.id||t.index===r.index&&t.path===r.path&&t.caseSensitive===r.caseSensitive&&((!t.children||0===t.children.length)&&(!r.children||0===r.children.length)||t.children.every((t,a)=>r.children?.some(r=>e(t,r))))})(e,t))),n,[e||"_","patch",String(o?.length||"0")],a);o.push(...i)}var ek=new WeakMap,ej=({key:e,route:t,manifest:r,mapRouteProperties:a})=>{let n=r[t.id];if(R(n,"No route found in manifest"),!n.lazy||"object"!=typeof n.lazy)return;let o=n.lazy[e];if(!o)return;let i=ek.get(n);i||(i={},ek.set(n,i));let s=i[e];if(s)return s;let l=(async()=>{let t=N.has(e),r=void 0!==n[e]&&"hasErrorBoundary"!==e;if(t)S(!t,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),i[e]=Promise.resolve();else if(r)S(!1,`Route "${n.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let t=await o();null!=t&&(Object.assign(n,{[e]:t}),Object.assign(n,a(n)))}"object"==typeof n.lazy&&(n.lazy[e]=void 0,Object.values(n.lazy).every(e=>void 0===e)&&(n.lazy=void 0))})();return i[e]=l,l},eT=new WeakMap;function eN(e){return void 0!==e}function eM(e,t,r){let a=e.map(({route:e})=>{if("object"==typeof e.lazy&&e.lazy.unstable_middleware)return ej({key:"unstable_middleware",route:e,manifest:t,mapRouteProperties:r})}).filter(eN);return a.length>0?Promise.all(a):void 0}async function eO(e){let t=e.matches.filter(e=>e.shouldLoad),r={};return(await Promise.all(t.map(e=>e.resolve()))).forEach((e,a)=>{r[t[a].route.id]=e}),r}async function e$(e){return e.matches.some(e=>e.route.unstable_middleware)?eD(e,!1,()=>eO(e),(e,t)=>({[t]:{type:"error",result:e}})):eO(e)}async function eD(e,t,r,a){let{matches:n,request:o,params:i,context:s}=e,l={handlerResult:void 0};try{let e=n.flatMap(e=>e.route.unstable_middleware?e.route.unstable_middleware.map(t=>[e.route.id,t]):[]),a=await eA({request:o,params:i,context:s},e,t,l,r);return t?a:l.handlerResult}catch(r){if(!l.middlewareError)throw r;let e=await a(l.middlewareError.error,l.middlewareError.routeId);if(t||!l.handlerResult)return e;return Object.assign(l.handlerResult,e)}}async function eA(e,t,r,a,n,o=0){let i,{request:s}=e;if(s.signal.aborted){if(s.signal.reason)throw s.signal.reason;throw Error(`Request aborted without an \`AbortSignal.reason\`: ${s.method} ${s.url}`)}let l=t[o];if(!l)return a.handlerResult=await n(),a.handlerResult;let[u,c]=l,d=!1,h=async()=>{if(d)throw Error("You may only call `next()` once per middleware");d=!0;let s=await eA(e,t,r,a,n,o+1);if(r)return i=s};try{let t=await c({request:e.request,params:e.params,context:e.context},h);if(!d)return h();if(void 0===t)return i;return t}catch(e){throw a.middlewareError?a.middlewareError.error!==e&&(a.middlewareError={routeId:u,error:e}):a.middlewareError={routeId:u,error:e},e}}function eF(e,t,r,a,n){let o=ej({key:"unstable_middleware",route:a.route,manifest:t,mapRouteProperties:e}),i=function(e,t,r,a,n){let o,i=r[e.id];if(R(i,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if("function"==typeof e.lazy){let t=eT.get(i);if(t)return{lazyRoutePromise:t,lazyHandlerPromise:t};let r=(async()=>{R("function"==typeof e.lazy,"No lazy route function found");let t=await e.lazy(),r={};for(let e in t){let a=t[e];if(void 0===a)continue;let n=M.has(e),o=void 0!==i[e]&&"hasErrorBoundary"!==e;n?S(!n,"Route property "+e+" is not a supported property to be returned from a lazy route function. This property will be ignored."):o?S(!o,`Route "${i.id}" has a static property "${e}" defined but its lazy function is also returning a value for this property. The lazy route property "${e}" will be ignored.`):r[e]=a}Object.assign(i,r),Object.assign(i,{...a(i),lazy:void 0})})();return eT.set(i,r),r.catch(()=>{}),{lazyRoutePromise:r,lazyHandlerPromise:r}}let s=Object.keys(e.lazy),l=[];for(let i of s){if(n&&n.includes(i))continue;let s=ej({key:i,route:e,manifest:r,mapRouteProperties:a});s&&(l.push(s),i===t&&(o=s))}let u=l.length>0?Promise.all(l).then(()=>{}):void 0;return u?.catch(()=>{}),o?.catch(()=>{}),{lazyRoutePromise:u,lazyHandlerPromise:o}}(a.route,tr(r.method)?"action":"loader",t,e,n);return{middleware:o,route:i.lazyRoutePromise,handler:i.lazyHandlerPromise}}function eI(e,t,r,a,n,o,i,s=null){let l=!1,u=eF(e,t,r,a,n);return{...a,_lazyPromises:u,shouldLoad:i,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler:e=>(l=!0,s)?"boolean"==typeof e?e_(a,{...s,defaultShouldRevalidate:e}):e_(a,s):i,resolve:e=>l||i||e&&"GET"===r.method&&(a.route.lazy||a.route.loader)?ez({request:r,match:a,lazyHandlerPromise:u?.handler,lazyRoutePromise:u?.route,handlerOverride:e,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}function eH(e,t,r,a,n,o,i,s=null){return a.map(a=>a.route.id!==n.route.id?{...a,shouldLoad:!1,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler:()=>!1,_lazyPromises:eF(e,t,r,a,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:eI(e,t,r,a,o,i,!0,s))}async function eU(e,t,r,a,n,o){r.some(e=>e._lazyPromises?.middleware)&&await Promise.all(r.map(e=>e._lazyPromises?.middleware));let i={request:t,params:r[0].params,context:n,matches:r},s=o?()=>{throw Error("You cannot call `unstable_runClientMiddleware()` from a static handler `dataStrategy`. Middleware is run outside of `dataStrategy` during SSR in order to bubble up the Response.  You can enable middleware via the `respond` API in `query`/`queryRoute`")}:e=>eD(i,!1,()=>e({...i,fetcherKey:a,unstable_runClientMiddleware:()=>{throw Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(e,t)=>({[t]:{type:"error",result:e}})),l=await e({...i,fetcherKey:a,unstable_runClientMiddleware:s});try{await Promise.all(r.flatMap(e=>[e._lazyPromises?.handler,e._lazyPromises?.route]))}catch(e){}return l}async function ez({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:a,handlerOverride:n,scopedContext:o}){let i,s,l=tr(e.method),u=l?"action":"loader",c=r=>{let a,i=new Promise((e,t)=>a=t);s=()=>a(),e.signal.addEventListener("abort",s);let l=a=>"function"!=typeof r?Promise.reject(Error(`You cannot call the handler for a route which defines a boolean "${u}" [routeId: ${t.route.id}]`)):r({request:e,params:t.params,context:o},...void 0!==a?[a]:[]);return Promise.race([(async()=>{try{let e=await (n?n(e=>l(e)):l());return{type:"data",result:e}}catch(e){return{type:"error",result:e}}})(),i])};try{let n=l?t.route.action:t.route.loader;if(r||a)if(n){let e,[t]=await Promise.all([c(n).catch(t=>{e=t}),r,a]);if(void 0!==e)throw e;i=t}else{await r;let n=l?t.route.action:t.route.loader;if(n)[i]=await Promise.all([c(n),a]);else{if("action"!==u)return{type:"data",result:void 0};let r=new URL(e.url),a=r.pathname+r.search;throw e1(405,{method:e.method,pathname:a,routeId:t.route.id})}}else if(n)i=await c(n);else{let t=new URL(e.url),r=t.pathname+t.search;throw e1(404,{pathname:r})}}catch(e){return{type:"error",result:e}}finally{s&&e.signal.removeEventListener("abort",s)}return i}async function eB(e){let{result:t,type:r}=e;if(e7(t)){let e;try{let r=t.headers.get("Content-Type");e=r&&/\bapplication\/json\b/.test(r)?null==t.body?null:await t.json():await t.text()}catch(e){return{type:"error",error:e}}return"error"===r?{type:"error",error:new eo(t.status,t.statusText,e),statusCode:t.status,headers:t.headers}:{type:"data",data:e,statusCode:t.status,headers:t.headers}}if("error"===r)return e8(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new eo(t.init?.status||500,void 0,t.data),statusCode:ei(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:ei(t)?t.status:void 0};return e8(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function eY(e,t,r,a,n){let o=e.headers.get("Location");if(R(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!ep.test(o)){let i=a.slice(0,a.findIndex(e=>e.route.id===r)+1);o=eR(new URL(t.url),i,n,o),e.headers.set("Location",o)}return e}function eq(e,t,r){if(ep.test(e)){let a=new URL(e.startsWith("//")?t.protocol+e:e),n=null!=Y(a.pathname,r);if(a.origin===t.origin&&n)return a.pathname+a.search+a.hash}return e}function eW(e,t,r,a){let n=e.createURL(e4(t)).toString(),o={signal:r};if(a&&tr(a.formMethod)){let{formMethod:e,formEncType:t}=a;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(a.json)):"text/plain"===t?o.body=a.text:"application/x-www-form-urlencoded"===t&&a.formData?o.body=eJ(a.formData):o.body=a.formData}return new Request(n,o)}function eJ(e){let t=new URLSearchParams;for(let[r,a]of e.entries())t.append(r,"string"==typeof a?a:a.name);return t}function eV(e){let t=new FormData;for(let[r,a]of e.entries())t.append(r,a);return t}function eX(e,t,r,a=!1,n=!1){let o,i={},s=null,l=!1,u={},c=r&&e5(r[1])?r[1].error:void 0;return e.forEach(r=>{if(!(r.route.id in t))return;let d=r.route.id,h=t[d];if(R(!e9(h),"Cannot handle redirect results in processLoaderData"),e5(h)){let t=h.error;if(void 0!==c&&(t=c,c=void 0),s=s||{},n)s[d]=t;else{let r=eQ(e,d);null==s[r.route.id]&&(s[r.route.id]=t)}a||(i[d]=eg),l||(l=!0,o=ei(h.error)?h.error.status:500),h.headers&&(u[d]=h.headers)}else i[d]=h.data,h.statusCode&&200!==h.statusCode&&!l&&(o=h.statusCode),h.headers&&(u[d]=h.headers)}),void 0!==c&&r&&(s={[r[0]]:c},i[r[0]]=void 0),{loaderData:i,errors:s,statusCode:o||200,loaderHeaders:u}}function eG(e,t,r,a,n,o){let{loaderData:i,errors:s}=eX(t,r,a);return n.filter(e=>!e.matches||e.matches.some(e=>e.shouldLoad)).forEach(t=>{let{key:r,match:a,controller:n}=t,i=o[r];if(R(i,"Did not find corresponding fetcher result"),!n||!n.signal.aborted)if(e5(i)){let t=eQ(e.matches,a?.route.id);s&&s[t.route.id]||(s={...s,[t.route.id]:i.error}),e.fetchers.delete(r)}else if(e9(i))R(!1,"Unhandled fetcher revalidation redirect");else{let t=tl(i.data);e.fetchers.set(r,t)}}),{loaderData:i,errors:s}}function eK(e,t,r,a){let n=Object.entries(t).filter(([,e])=>e!==eg).reduce((e,[t,r])=>(e[t]=r,e),{});for(let o of r){let r=o.route.id;if(!t.hasOwnProperty(r)&&e.hasOwnProperty(r)&&o.route.loader&&(n[r]=e[r]),a&&a.hasOwnProperty(r))break}return n}function eZ(e){return e?e5(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function eQ(e,t){return(t?e.slice(0,e.findIndex(e=>e.route.id===t)+1):[...e]).reverse().find(e=>!0===e.route.hasErrorBoundary)||e[0]}function e0(e){let t=1===e.length?e[0]:e.find(e=>e.index||!e.path||"/"===e.path)||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function e1(e,{pathname:t,routeId:r,method:a,type:n,message:o}={}){let i="Unknown Server Error",s="Unknown @remix-run/router error";return 400===e?(i="Bad Request",a&&t&&r?s=`You made a ${a} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:"invalid-body"===n&&(s="Unable to encode submission body")):403===e?(i="Forbidden",s=`Route "${r}" does not match URL "${t}"`):404===e?(i="Not Found",s=`No route matches URL "${t}"`):405===e&&(i="Method Not Allowed",a&&t&&r?s=`You made a ${a.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:a&&(s=`Invalid request method "${a.toUpperCase()}"`)),new eo(e||500,i,Error(s),!0)}function e2(e){let t=Object.entries(e);for(let e=t.length-1;e>=0;e--){let[r,a]=t[e];if(e9(a))return{key:r,result:a}}}function e4(e){return _({..."string"==typeof e?L(e):e,hash:""})}function e3(e){return e7(e.result)&&ec.has(e.result.status)}function e5(e){return"error"===e.type}function e9(e){return"redirect"===(e&&e.type)}function e8(e){return"object"==typeof e&&null!=e&&"type"in e&&"data"in e&&"init"in e&&"DataWithResponseInit"===e.type}function e7(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"object"==typeof e.headers&&void 0!==e.body}function e6(e){return ec.has(e)}function te(e){return e7(e)&&e6(e.status)&&e.headers.has("Location")}function tt(e){return eu.has(e.toUpperCase())}function tr(e){return el.has(e.toUpperCase())}function ta(e){return new URLSearchParams(e).getAll("index").some(e=>""===e)}function tn(e,t){let r="string"==typeof t?L(t).search:t.search;if(e[e.length-1].route.index&&ta(r||""))return e[e.length-1];let a=J(e);return a[a.length-1]}function to(e){let{formMethod:t,formAction:r,formEncType:a,text:n,formData:o,json:i}=e;if(t&&r&&a){if(null!=n)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:void 0,text:n};else if(null!=o)return{formMethod:t,formAction:r,formEncType:a,formData:o,json:void 0,text:void 0};else if(void 0!==i)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:i,text:void 0}}}function ti(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function ts(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function tl(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}var tu=f(r(43210)),tc=f(r(43210)),td=tc.createContext(null);td.displayName="DataRouter";var th=tc.createContext(null);th.displayName="DataRouterState";var tf=tc.createContext({isTransitioning:!1});tf.displayName="ViewTransition";var tm=tc.createContext(new Map);tm.displayName="Fetchers";var tp=tc.createContext(null);tp.displayName="Await";var ty=tc.createContext(null);ty.displayName="Navigation";var tv=tc.createContext(null);tv.displayName="Location";var tg=tc.createContext({outlet:null,matches:[],isDataRoute:!1});tg.displayName="Route";var tw=tc.createContext(null);tw.displayName="RouteError";var tb=f(r(43210));function tE(e,{relative:t}={}){R(tx(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=tb.useContext(ty),{hash:n,pathname:o,search:i}=tM(e,{relative:t}),s=o;return"/"!==r&&(s="/"===o?r:G([r,o])),a.createHref({pathname:s,search:i,hash:n})}function tx(){return null!=tb.useContext(tv)}function tR(){return R(tx(),"useLocation() may be used only in the context of a <Router> component."),tb.useContext(tv).location}function tS(){return tb.useContext(tv).navigationType}function tC(e){R(tx(),"useMatch() may be used only in the context of a <Router> component.");let{pathname:t}=tR();return tb.useMemo(()=>U(e,B(t)),[t,e])}var tP="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function t_(e){tb.useContext(ty).static||tb.useLayoutEffect(e)}function tL(){let{isDataRoute:e}=tb.useContext(tg);return e?function(){let{router:e}=tU("useNavigate"),t=tB("useNavigate"),r=tb.useRef(!1);return t_(()=>{r.current=!0}),tb.useCallback(async(a,n={})=>{S(r.current,tP),r.current&&("number"==typeof a?e.navigate(a):await e.navigate(a,{fromRouteId:t,...n}))},[e,t])}():function(){R(tx(),"useNavigate() may be used only in the context of a <Router> component.");let e=tb.useContext(td),{basename:t,navigator:r}=tb.useContext(ty),{matches:a}=tb.useContext(tg),{pathname:n}=tR(),o=JSON.stringify(V(a)),i=tb.useRef(!1);return t_(()=>{i.current=!0}),tb.useCallback((a,s={})=>{if(S(i.current,tP),!i.current)return;if("number"==typeof a)return void r.go(a);let l=X(a,JSON.parse(o),n,"path"===s.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:G([t,l.pathname])),(s.replace?r.replace:r.push)(l,s.state,s)},[t,r,o,n,e])}()}var tk=tb.createContext(null);function tj(){return tb.useContext(tk)}function tT(e){let t=tb.useContext(tg).outlet;return t?tb.createElement(tk.Provider,{value:e},t):t}function tN(){let{matches:e}=tb.useContext(tg),t=e[e.length-1];return t?t.params:{}}function tM(e,{relative:t}={}){let{matches:r}=tb.useContext(tg),{pathname:a}=tR(),n=JSON.stringify(V(r));return tb.useMemo(()=>X(e,JSON.parse(n),a,"path"===t),[e,n,a,t])}function tO(e,t){return t$(e,t)}function t$(e,t,r,a){let n;R(tx(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o,static:i}=tb.useContext(ty),{matches:s}=tb.useContext(tg),l=s[s.length-1],u=l?l.params:{},c=l?l.pathname:"/",d=l?l.pathnameBase:"/",h=l&&l.route;{let e=h&&h.path||"";t2(c,!h||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${c}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let f=tR();if(t){let e="string"==typeof t?L(t):t;R("/"===d||e.pathname?.startsWith(d),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${d}" but pathname "${e.pathname}" was given in the \`location\` prop.`),n=e}else n=f;let m=n.pathname||"/",p=m;if("/"!==d){let e=d.replace(/^\//,"").split("/");p="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=!i&&r&&r.matches&&r.matches.length>0?r.matches:$(e,{pathname:p});S(h||null!=y,`No routes matched location "${n.pathname}${n.search}${n.hash}" `),S(null==y||void 0!==y[y.length-1].route.element||void 0!==y[y.length-1].route.Component||void 0!==y[y.length-1].route.lazy,`Matched leaf route at location "${n.pathname}${n.search}${n.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let v=tI(y&&y.map(e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:G([d,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:G([d,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),s,r,a);return t&&v?tb.createElement(tv.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...n},navigationType:"POP"}},v):v}var tD=tb.createElement(function(){let e=tG(),t=ei(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",n={padding:"2px 4px",backgroundColor:a},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=tb.createElement(tb.Fragment,null,tb.createElement("p",null,"\uD83D\uDCBF Hey developer \uD83D\uDC4B"),tb.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",tb.createElement("code",{style:n},"ErrorBoundary")," or"," ",tb.createElement("code",{style:n},"errorElement")," prop on your route.")),tb.createElement(tb.Fragment,null,tb.createElement("h2",null,"Unexpected Application Error!"),tb.createElement("h3",{style:{fontStyle:"italic"}},t),r?tb.createElement("pre",{style:{padding:"0.5rem",backgroundColor:a}},r):null,o)},null),tA=class extends tb.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?tb.createElement(tg.Provider,{value:this.props.routeContext},tb.createElement(tw.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function tF({routeContext:e,match:t,children:r}){let a=tb.useContext(td);return a&&a.static&&a.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=t.route.id),tb.createElement(tg.Provider,{value:e},r)}function tI(e,t=[],r=null,a=null){if(null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(0!==t.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let n=e,o=r?.errors;if(null!=o){let e=n.findIndex(e=>e.route.id&&o?.[e.route.id]!==void 0);R(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),n=n.slice(0,Math.min(n.length,e+1))}let i=!1,s=-1;if(r)for(let e=0;e<n.length;e++){let t=n[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(s=e),t.route.id){let{loaderData:e,errors:a}=r,o=t.route.loader&&!e.hasOwnProperty(t.route.id)&&(!a||void 0===a[t.route.id]);if(t.route.lazy||o){i=!0,n=s>=0?n.slice(0,s+1):[n[0]];break}}}return n.reduceRight((e,a,l)=>{let u,c=!1,d=null,h=null;r&&(u=o&&a.route.id?o[a.route.id]:void 0,d=a.route.errorElement||tD,i&&(s<0&&0===l?(t2("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),c=!0,h=null):s===l&&(c=!0,h=a.route.hydrateFallbackElement||null)));let f=t.concat(n.slice(0,l+1)),m=()=>{let t;return t=u?d:c?h:a.route.Component?tb.createElement(a.route.Component,null):a.route.element?a.route.element:e,tb.createElement(tF,{match:a,routeContext:{outlet:e,matches:f,isDataRoute:null!=r},children:t})};return r&&(a.route.ErrorBoundary||a.route.errorElement||0===l)?tb.createElement(tA,{location:r.location,revalidation:r.revalidation,component:d,error:u,children:m(),routeContext:{outlet:null,matches:f,isDataRoute:!0}}):m()},null)}function tH(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function tU(e){let t=tb.useContext(td);return R(t,tH(e)),t}function tz(e){let t=tb.useContext(th);return R(t,tH(e)),t}function tB(e){let t,r=(R(t=tb.useContext(tg),tH(e)),t),a=r.matches[r.matches.length-1];return R(a.route.id,`${e} can only be used on routes that contain a unique "id"`),a.route.id}function tY(){return tz("useNavigation").navigation}function tq(){let e=tU("useRevalidator"),t=tz("useRevalidator");return tb.useMemo(()=>({async revalidate(){await e.router.revalidate()},state:t.revalidation}),[e.router,t.revalidation])}function tW(){let{matches:e,loaderData:t}=tz("useMatches");return tb.useMemo(()=>e.map(e=>A(e,t)),[e,t])}function tJ(){let e=tz("useLoaderData"),t=tB("useLoaderData");return e.loaderData[t]}function tV(e){return tz("useRouteLoaderData").loaderData[e]}function tX(){let e=tz("useActionData"),t=tB("useLoaderData");return e.actionData?e.actionData[t]:void 0}function tG(){let e=tb.useContext(tw),t=tz("useRouteError"),r=tB("useRouteError");return void 0!==e?e:t.errors?.[r]}function tK(){let e=tb.useContext(tp);return e?._data}function tZ(){let e=tb.useContext(tp);return e?._error}var tQ=0;function t0(e){let{router:t,basename:r}=tU("useBlocker"),a=tz("useBlocker"),[n,o]=tb.useState(""),i=tb.useCallback(t=>{if("function"!=typeof e)return!!e;if("/"===r)return e(t);let{currentLocation:a,nextLocation:n,historyAction:o}=t;return e({currentLocation:{...a,pathname:Y(a.pathname,r)||a.pathname},nextLocation:{...n,pathname:Y(n.pathname,r)||n.pathname},historyAction:o})},[r,e]);return tb.useEffect(()=>{let e=String(++tQ);return o(e),()=>t.deleteBlocker(e)},[t]),tb.useEffect(()=>{""!==n&&t.getBlocker(n,i)},[t,n,i]),n&&a.blockers.has(n)?a.blockers.get(n):em}var t1={};function t2(e,t,r){t||t1[e]||(t1[e]=!0,S(!1,r))}var t4={};function t3(e,t){e||t4[t]||(t4[t]=!0,console.warn(t))}function t5(e){let t={hasErrorBoundary:e.hasErrorBoundary||null!=e.ErrorBoundary||null!=e.errorElement};return e.Component&&(e.element&&S(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:tu.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&S(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:tu.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&S(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:tu.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var t9=["HydrateFallback","hydrateFallbackElement"];function t8(e,t){return ew({basename:t?.basename,unstable_getContext:t?.unstable_getContext,future:t?.future,history:b({initialEntries:t?.initialEntries,initialIndex:t?.initialIndex}),hydrationData:t?.hydrationData,routes:e,hydrationRouteProperties:t9,mapRouteProperties:t5,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation}).initialize()}var t7=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}})}};function t6({router:e,flushSync:t}){let[r,a]=tu.useState(e.state),[n,o]=tu.useState(),[i,s]=tu.useState({isTransitioning:!1}),[l,u]=tu.useState(),[c,d]=tu.useState(),[h,f]=tu.useState(),m=tu.useRef(new Map),p=tu.useCallback((r,{deletedFetchers:n,flushSync:i,viewTransitionOpts:h})=>{r.fetchers.forEach((e,t)=>{void 0!==e.data&&m.current.set(t,e.data)}),n.forEach(e=>m.current.delete(e)),t3(!1===i||null!=t,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let p=null!=e.window&&null!=e.window.document&&"function"==typeof e.window.document.startViewTransition;if(t3(null==h||p,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!h||!p)return void(t&&i?t(()=>a(r)):tu.startTransition(()=>a(r)));if(t&&i){t(()=>{c&&(l&&l.resolve(),c.skipTransition()),s({isTransitioning:!0,flushSync:!0,currentLocation:h.currentLocation,nextLocation:h.nextLocation})});let n=e.window.document.startViewTransition(()=>{t(()=>a(r))});n.finished.finally(()=>{t(()=>{u(void 0),d(void 0),o(void 0),s({isTransitioning:!1})})}),t(()=>d(n));return}c?(l&&l.resolve(),c.skipTransition(),f({state:r,currentLocation:h.currentLocation,nextLocation:h.nextLocation})):(o(r),s({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}))},[e.window,t,c,l]);tu.useLayoutEffect(()=>e.subscribe(p),[e,p]),tu.useEffect(()=>{i.isTransitioning&&!i.flushSync&&u(new t7)},[i]),tu.useEffect(()=>{if(l&&n&&e.window){let t=l.promise,r=e.window.document.startViewTransition(async()=>{tu.startTransition(()=>a(n)),await t});r.finished.finally(()=>{u(void 0),d(void 0),o(void 0),s({isTransitioning:!1})}),d(r)}},[n,l,e.window]),tu.useEffect(()=>{l&&n&&r.location.key===n.location.key&&l.resolve()},[l,c,r.location,n]),tu.useEffect(()=>{!i.isTransitioning&&h&&(o(h.state),s({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}),f(void 0))},[i.isTransitioning,h]);let y=tu.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:t=>e.navigate(t),push:(t,r,a)=>e.navigate(t,{state:r,preventScrollReset:a?.preventScrollReset}),replace:(t,r,a)=>e.navigate(t,{replace:!0,state:r,preventScrollReset:a?.preventScrollReset})}),[e]),v=e.basename||"/",g=tu.useMemo(()=>({router:e,navigator:y,static:!1,basename:v}),[e,y,v]);return tu.createElement(tu.Fragment,null,tu.createElement(td.Provider,{value:g},tu.createElement(th.Provider,{value:r},tu.createElement(tm.Provider,{value:m.current},tu.createElement(tf.Provider,{value:i},tu.createElement(ro,{basename:v,location:r.location,navigationType:r.historyAction,navigator:y},tu.createElement(re,{routes:e.routes,future:e.future,state:r})))))),null)}var re=tu.memo(function({routes:e,future:t,state:r}){return t$(e,void 0,r,t)});function rt({basename:e,children:t,initialEntries:r,initialIndex:a}){let n=tu.useRef();null==n.current&&(n.current=b({initialEntries:r,initialIndex:a,v5Compat:!0}));let o=n.current,[i,s]=tu.useState({action:o.action,location:o.location}),l=tu.useCallback(e=>{tu.startTransition(()=>s(e))},[s]);return tu.useLayoutEffect(()=>o.listen(l),[o,l]),tu.createElement(ro,{basename:e,children:t,location:i.location,navigationType:i.action,navigator:o})}function rr({to:e,replace:t,state:r,relative:a}){R(tx(),"<Navigate> may be used only in the context of a <Router> component.");let{static:n}=tu.useContext(ty);S(!n,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=tu.useContext(tg),{pathname:i}=tR(),s=tL(),l=JSON.stringify(X(e,V(o),i,"path"===a));return tu.useEffect(()=>{s(JSON.parse(l),{replace:t,state:r,relative:a})},[s,l,a,t,r]),null}function ra(e){return tT(e.context)}function rn(e){R(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function ro({basename:e="/",children:t=null,location:r,navigationType:a="POP",navigator:n,static:o=!1}){R(!tx(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let i=e.replace(/^\/*/,"/"),s=tu.useMemo(()=>({basename:i,navigator:n,static:o,future:{}}),[i,n,o]);"string"==typeof r&&(r=L(r));let{pathname:l="/",search:u="",hash:c="",state:d=null,key:h="default"}=r,f=tu.useMemo(()=>{let e=Y(l,i);return null==e?null:{location:{pathname:e,search:u,hash:c,state:d,key:h},navigationType:a}},[i,l,u,c,d,h,a]);return(S(null!=f,`<Router basename="${i}"> is not able to match the URL "${l}${u}${c}" because it does not start with the basename, so the <Router> won't render anything.`),null==f)?null:tu.createElement(ty.Provider,{value:s},tu.createElement(tv.Provider,{children:t,value:f}))}function ri({children:e,location:t}){return t$(rc(e),t)}function rs({children:e,errorElement:t,resolve:r}){return tu.createElement(rl,{resolve:r,errorElement:t},tu.createElement(ru,null,e))}var rl=class extends tu.Component{constructor(e){super(e),this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){console.error("<Await> caught the following error during render",e,t)}render(){let{children:e,errorElement:t,resolve:r}=this.props,a=null,n=0;if(r instanceof Promise)if(this.state.error){n=2;let e=this.state.error;Object.defineProperty(a=Promise.reject().catch(()=>{}),"_tracked",{get:()=>!0}),Object.defineProperty(a,"_error",{get:()=>e})}else r._tracked?n="_error"in(a=r)?2:+("_data"in a):(n=0,Object.defineProperty(r,"_tracked",{get:()=>!0}),a=r.then(e=>Object.defineProperty(r,"_data",{get:()=>e}),e=>Object.defineProperty(r,"_error",{get:()=>e})));else n=1,Object.defineProperty(a=Promise.resolve(),"_tracked",{get:()=>!0}),Object.defineProperty(a,"_data",{get:()=>r});if(2===n&&!t)throw a._error;if(2===n)return tu.createElement(tp.Provider,{value:a,children:t});if(1===n)return tu.createElement(tp.Provider,{value:a,children:e});throw a}};function ru({children:e}){let t=tK(),r="function"==typeof e?e(t):e;return tu.createElement(tu.Fragment,null,r)}function rc(e,t=[]){let r=[];return tu.Children.forEach(e,(e,a)=>{if(!tu.isValidElement(e))return;let n=[...t,a];if(e.type===tu.Fragment)return void r.push.apply(r,rc(e.props.children,n));R(e.type===rn,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),R(!e.props.index||!e.props.children,"An index route cannot have child routes.");let o={id:e.props.id||n.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=rc(e.props.children,n)),r.push(o)}),r}var rd=rc;function rh(e){return tI(e)}var rf=f(r(43210)),rm="get",rp="application/x-www-form-urlencoded";function ry(e){return null!=e&&"string"==typeof e.tagName}function rv(e=""){return new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let a=e[r];return t.concat(Array.isArray(a)?a.map(e=>[r,e]):[[r,a]])},[]))}var rg=null,rw=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function rb(e){return null==e||rw.has(e)?e:(S(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${rp}"`),null)}var rE=f(r(43210));function rx(e,t){if(!1===e||null==e)throw Error(t)}async function rR(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(t){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(t),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function rS(e){return e.css?e.css.map(e=>({rel:"stylesheet",href:e})):[]}async function rC(e){if(!e.css)return;let t=rS(e);await Promise.all(t.map(r_))}async function rP(e,t){if(!e.css&&!t.links||!function(){if(void 0!==n)return n;let e=document.createElement("link");return n=e.relList.supports("preload"),e=null,n}())return;let r=[];if(e.css&&r.push(...rS(e)),t.links&&r.push(...t.links()),0===r.length)return;let a=[];for(let e of r)rL(e)||"stylesheet"!==e.rel||a.push({...e,rel:"preload",as:"style"});await Promise.all(a.map(r_))}async function r_(e){return new Promise(t=>{if(e.media&&!window.matchMedia(e.media).matches||document.querySelector(`link[rel="stylesheet"][href="${e.href}"]`))return t();let r=document.createElement("link");function a(){document.head.contains(r)&&document.head.removeChild(r)}Object.assign(r,e),r.onload=()=>{a(),t()},r.onerror=()=>{a(),t()},document.head.appendChild(r)})}function rL(e){return null!=e&&"string"==typeof e.page}function rk(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}async function rj(e,t,r){return rM((await Promise.all(e.map(async e=>{let a=t.routes[e.route.id];if(a){let e=await rR(a,r);return e.links?e.links():[]}return[]}))).flat(1).filter(rk).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"}))}function rT(e,t,r,a,n,o){let i=(e,t)=>!r[t]||e.route.id!==r[t].route.id,s=(e,t)=>r[t].pathname!==e.pathname||r[t].route.path?.endsWith("*")&&r[t].params["*"]!==e.params["*"];return"assets"===o?t.filter((e,t)=>i(e,t)||s(e,t)):"data"===o?t.filter((t,o)=>{let l=a.routes[t.route.id];if(!l||!l.hasLoader)return!1;if(i(t,o)||s(t,o))return!0;if(t.route.shouldRevalidate){let a=t.route.shouldRevalidate({currentUrl:new URL(n.pathname+n.search+n.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof a)return a}return!0}):[]}function rN(e,t,{includeHydrateFallback:r}={}){return[...new Set(e.map(e=>{let a=t.routes[e.route.id];if(!a)return[];let n=[a.module];return a.clientActionModule&&(n=n.concat(a.clientActionModule)),a.clientLoaderModule&&(n=n.concat(a.clientLoaderModule)),r&&a.hydrateFallbackModule&&(n=n.concat(a.hydrateFallbackModule)),a.imports&&(n=n.concat(a.imports)),n}).flat(1))]}function rM(e,t){let r=new Set,a=new Set(t);return e.reduce((e,n)=>{if(t&&!rL(n)&&"script"===n.as&&n.href&&a.has(n.href))return e;let o=JSON.stringify(function(e){let t={};for(let r of Object.keys(e).sort())t[r]=e[r];return t}(n));return r.has(o)||(r.add(o),e.push({key:o,link:n})),e},[])}var rO={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},r$=/[&><\u2028\u2029]/g,rD=f(r(43210)),rA=r(14791);async function rF(e){let t={signal:e.signal};if("GET"!==e.method){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}var rI=Symbol("SingleFetchRedirect"),rH=new Set([100,101,204,205]);function rU({context:e,identifier:t,reader:r,textDecoder:a,nonce:n}){if(!e.renderMeta||!e.renderMeta.didRenderScripts)return null;e.renderMeta.streamCache||(e.renderMeta.streamCache={});let{streamCache:o}=e.renderMeta,i=o[t];if(i||(i=o[t]=r.read().then(e=>{o[t].result={done:e.done,value:a.decode(e.value,{stream:!0})}}).catch(e=>{o[t].error=e})),i.error)throw i.error;if(void 0===i.result)throw i;let{done:s,value:l}=i.result,u=l?rD.createElement("script",{nonce:n,dangerouslySetInnerHTML:{__html:`window.__reactRouterContext.streamController.enqueue(${JSON.stringify(l).replace(r$,e=>rO[e])});`}}):null;return s?rD.createElement(rD.Fragment,null,u,rD.createElement("script",{nonce:n,dangerouslySetInnerHTML:{__html:"window.__reactRouterContext.streamController.close();"}})):rD.createElement(rD.Fragment,null,u,rD.createElement(rD.Suspense,null,rD.createElement(rU,{context:e,identifier:t+1,reader:r,textDecoder:a,nonce:n})))}function rz(e,t,r,a,n){var o,i,s,l,u;let c=(o=e,i=e=>{let a=t.routes[e.route.id];rx(a,"Route not found in manifest");let n=r[e.route.id];return{hasLoader:a.hasLoader,hasClientLoader:a.hasClientLoader,hasShouldRevalidate:!!n?.shouldRevalidate}},s=rV,l=a,u=n,async e=>{let{request:t,matches:r,fetcherKey:a}=e,n=o();if("GET"!==t.method)return rB(e,s,u);let c=r.some(e=>{let{hasLoader:t,hasClientLoader:r}=i(e);return e.unstable_shouldCallHandler()&&t&&!r});return l||c?a?rW(e,s,u):rq(e,n,i,s,l,u):rY(e,i,s,u)});return async e=>e.unstable_runClientMiddleware(c)}async function rB(e,t,r){let a,n=e.matches.find(e=>e.unstable_shouldCallHandler());rx(n,"No action match found");let o=await n.resolve(async o=>await o(async()=>{let{data:o,status:i}=await t(e,r,[n.route.id]);return a=i,rG(o,n.route.id)}));return e7(o.result)||ei(o.result)?{[n.route.id]:o}:{[n.route.id]:{type:o.type,result:et(o.result,a)}}}async function rY(e,t,r,a){let n=e.matches.filter(e=>e.unstable_shouldCallHandler()),o={};return await Promise.all(n.map(n=>n.resolve(async i=>{try{let{hasClientLoader:s}=t(n),l=n.route.id,u=s?await i(async()=>{let{data:t}=await r(e,a,[l]);return rG(t,l)}):await i();o[n.route.id]={type:"data",result:u}}catch(e){o[n.route.id]={type:"error",result:e}}}))),o}async function rq(e,t,r,a,n,o){let i=new Set,s=!1,l=e.matches.map(()=>rK()),u=rK(),c={},d=Promise.all(e.matches.map(async(t,n)=>t.resolve(async d=>{l[n].resolve();let h=t.route.id,{hasLoader:f,hasClientLoader:m,hasShouldRevalidate:p}=r(t),y=!t.unstable_shouldRevalidateArgs||null==t.unstable_shouldRevalidateArgs.actionStatus||t.unstable_shouldRevalidateArgs.actionStatus<400;if(!t.unstable_shouldCallHandler(y)){s||(s=null!=t.unstable_shouldRevalidateArgs&&f&&!0===p);return}if(m){f&&(s=!0);try{let t=await d(async()=>{let{data:t}=await a(e,o,[h]);return rG(t,h)});c[h]={type:"data",result:t}}catch(e){c[h]={type:"error",result:e}}return}f&&i.add(h);try{let e=await d(async()=>{let e=await u.promise;return rG(e,h)});c[h]={type:"data",result:e}}catch(e){c[h]={type:"error",result:e}}})));if(await Promise.all(l.map(e=>e.promise)),t.state.initialized&&0!==i.size||window.__reactRouterHdrActive){let t=n&&s&&i.size>0?[...i.keys()]:void 0;try{let r=await a(e,o,t);u.resolve(r.data)}catch(e){u.reject(e)}}else u.resolve({routes:{}});return await d,c}async function rW(e,t,r){let a=e.matches.find(e=>e.unstable_shouldCallHandler());rx(a,"No fetcher match found");let n=a.route.id,o=await a.resolve(async a=>a(async()=>{let{data:a}=await t(e,r,[n]);return rG(a,n)}));return{[a.route.id]:o}}function rJ(e,t){let r="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===r.pathname?r.pathname="_root.data":t&&"/"===Y(r.pathname,t)?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}async function rV(e,t,r){let{request:a}=e,n=rJ(a.url,t);"GET"===a.method&&(n=function(e){let t=e.searchParams.getAll("index");e.searchParams.delete("index");let r=[];for(let e of t)e&&r.push(e);for(let t of r)e.searchParams.append("index",t);return e}(n),r&&n.searchParams.set("_routes",r.join(",")));let o=await fetch(n,await rF(a));if(404===o.status&&!o.headers.has("X-Remix-Response"))throw new eo(404,"Not Found",!0);if(204===o.status&&o.headers.has("X-Remix-Redirect"))return{status:202,data:{redirect:{redirect:o.headers.get("X-Remix-Redirect"),status:Number(o.headers.get("X-Remix-Status")||"302"),revalidate:"true"===o.headers.get("X-Remix-Revalidate"),reload:"true"===o.headers.get("X-Remix-Reload-Document"),replace:"true"===o.headers.get("X-Remix-Replace")}}};if(rH.has(o.status)){let e={};return r&&"GET"!==a.method&&(e[r[0]]={data:void 0}),{status:o.status,data:{routes:e}}}rx(o.body,"No response body to decode");try{let e,t=await rX(o.body,window);if("GET"===a.method){let r=t.value;e=rI in r?{redirect:r[rI]}:{routes:r}}else{let a=t.value,n=r?.[0];rx(n,"No routeId found for single fetch call decoding"),e="redirect"in a?{redirect:a}:{routes:{[n]:a}}}return{status:o.status,data:e}}catch(e){throw Error("Unable to decode turbo-stream response")}}function rX(e,t){return(0,rA.decode)(e,{plugins:[(e,...r)=>{if("SanitizedError"===e){let[e,a,n]=r,o=Error;e&&e in t&&"function"==typeof t[e]&&(o=t[e]);let i=new o(a);return i.stack=n,{value:i}}if("ErrorResponse"===e){let[e,t,a]=r;return{value:new eo(t,a,e)}}return"SingleFetchRedirect"===e?{value:{[rI]:r[0]}}:"SingleFetchClassInstance"===e?{value:r[0]}:"SingleFetchFallback"===e?{value:void 0}:void 0}]})}function rG(e,t){if("redirect"in e){let{redirect:t,revalidate:r,reload:a,replace:n,status:o}=e.redirect;throw er(t,{status:o,headers:{...r?{"X-Remix-Revalidate":"yes"}:null,...a?{"X-Remix-Reload-Document":"yes"}:null,...n?{"X-Remix-Replace":"yes"}:null}})}let r=e.routes[t];if("error"in r)throw r.error;if("data"in r)return r.data;throw Error(`No response found for routeId "${t}"`)}function rK(){let e,t,r=new Promise((a,n)=>{e=async e=>{a(e);try{await r}catch(e){}},t=async e=>{n(e);try{await r}catch(e){}}});return{promise:r,resolve:e,reject:t}}var rZ=f(r(43210)),rQ=f(r(43210)),r0=f(r(43210)),r1=class extends r0.Component{constructor(e){super(e),this.state={error:e.error||null,location:e.location}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location?{error:e.error||null,location:e.location}:{error:e.error||t.error,location:t.location}}render(){return this.state.error?r0.createElement(r2,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}};function r2({error:e,isOutsideRemixApp:t}){let r;console.error(e);let a=r0.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "\u{1F4BF} Hey developer \u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information."
        );
      `}});return ei(e)?r0.createElement(r4,{title:"Unhandled Thrown Response!"},r0.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),a):(r=e instanceof Error?e:Error(null==e?"Unknown Error":"object"==typeof e&&"toString"in e?e.toString():JSON.stringify(e)),r0.createElement(r4,{title:"Application Error!",isOutsideRemixApp:t},r0.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),r0.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},r.stack),a))}function r4({title:e,renderScripts:t,isOutsideRemixApp:r,children:a}){let{routeModules:n}=ap();return n.root?.Layout&&!r?a:r0.createElement("html",{lang:"en"},r0.createElement("head",null,r0.createElement("meta",{charSet:"utf-8"}),r0.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),r0.createElement("title",null,e)),r0.createElement("body",null,r0.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},a,t?r0.createElement(aR,null):null)))}var r3=f(r(43210));function r5(){return r3.createElement(r4,{title:"Loading...",renderScripts:!0},r3.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "\u{1F4BF} Hey developer \u{1F44B}. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://remix.run/route/hydrate-fallback " +
                "for more information."
              );
            `}}))}function r9(e){let t={};return Object.values(e).forEach(e=>{if(e){let r=e.parentId||"";t[r]||(t[r]=[]),t[r].push(e)}}),t}function r8(e,t,r){let a=an(t),n=t.HydrateFallback&&(!r||"root"===e.id)?t.HydrateFallback:"root"===e.id?r5:void 0,o=t.ErrorBoundary?t.ErrorBoundary:"root"===e.id?()=>rQ.createElement(r2,{error:tG()}):void 0;return"root"===e.id&&t.Layout?{...a?{element:rQ.createElement(t.Layout,null,rQ.createElement(a,null))}:{Component:a},...o?{errorElement:rQ.createElement(t.Layout,null,rQ.createElement(o,null))}:{ErrorBoundary:o},...n?{hydrateFallbackElement:rQ.createElement(t.Layout,null,rQ.createElement(n,null))}:{HydrateFallback:n}}:{Component:a,ErrorBoundary:o,HydrateFallback:n}}function r7(e,t,r,a,n,o){return at(t,r,a,n,o,"",r9(t),e)}function r6(e,t){if("loader"===e&&!t.hasLoader||"action"===e&&!t.hasAction){let r=`You are trying to call ${"action"===e?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;throw console.error(r),new eo(400,"Bad Request",Error(r),!0)}}function ae(e,t){let r="clientAction"===e?"a":"an",a=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(a),new eo(405,"Method Not Allowed",Error(a),!0)}function at(e,t,r,a,n,o="",i=r9(e),s){return(i[o]||[]).map(o=>{let l=t[o.id];function u(e){return rx("function"==typeof e,"No single fetch function available for route handler"),e()}function c(e){return o.hasLoader?u(e):Promise.resolve(null)}function d(e){if(!o.hasAction)throw ae("action",o.id);return u(e)}function h(e){e.clientActionModule&&import(e.clientActionModule),e.clientLoaderModule&&import(e.clientLoaderModule)}async function f(e){let r=t[o.id],a=r?rP(o,r):Promise.resolve();try{return e()}finally{await a}}let m={id:o.id,index:o.index,path:o.path};if(l){Object.assign(m,{...m,...r8(o,l,n),unstable_middleware:l.unstable_clientMiddleware,handle:l.handle,shouldRevalidate:ar(m.path,l,o,a,s)});let e=r&&r.loaderData&&o.id in r.loaderData,t=e?r?.loaderData?.[o.id]:void 0,i=r&&r.errors&&o.id in r.errors,u=i?r?.errors?.[o.id]:void 0,h=null==s&&(l.clientLoader?.hydrate===!0||!o.hasLoader);m.loader=async({request:r,params:a,context:n},s)=>{try{return await f(async()=>(rx(l,"No `routeModule` available for critical-route loader"),l.clientLoader)?l.clientLoader({request:r,params:a,context:n,async serverLoader(){if(r6("loader",o),h){if(e)return t;if(i)throw u}return c(s)}}):c(s))}finally{h=!1}},m.loader.hydrate=ao(o.id,l.clientLoader,o.hasLoader,n),m.action=({request:e,params:t,context:r},a)=>f(async()=>{if(rx(l,"No `routeModule` available for critical-route action"),!l.clientAction){if(n)throw ae("clientAction",o.id);return d(a)}return l.clientAction({request:e,params:t,context:r,serverAction:async()=>(r6("action",o),d(a))})})}else{let e;async function p(){return e||(e=(async()=>{(o.clientLoaderModule||o.clientActionModule)&&await new Promise(e=>setTimeout(e,0));let e=aa(o,t);return h(o),await e})()),await e}o.hasClientLoader||(m.loader=(e,t)=>f(()=>c(t))),o.hasClientAction||(m.action=(e,t)=>f(()=>{if(n)throw ae("clientAction",o.id);return d(t)})),m.lazy={loader:o.hasClientLoader?async()=>{let{clientLoader:e}=o.clientLoaderModule?await import(o.clientLoaderModule):await p();return rx(e,"No `clientLoader` export found"),(t,r)=>e({...t,serverLoader:async()=>(r6("loader",o),c(r))})}:void 0,action:o.hasClientAction?async()=>{let e=o.clientActionModule?import(o.clientActionModule):p();h(o);let{clientAction:t}=await e;return rx(t,"No `clientAction` export found"),(e,r)=>t({...e,serverAction:async()=>(r6("action",o),d(r))})}:void 0,unstable_middleware:o.hasClientMiddleware?async()=>{let{unstable_clientMiddleware:e}=o.clientMiddlewareModule?await import(o.clientMiddlewareModule):await p();return rx(e,"No `unstable_clientMiddleware` export found"),e}:void 0,shouldRevalidate:async()=>{let e=await p();return ar(m.path,e,o,a,s)},handle:async()=>(await p()).handle,Component:async()=>(await p()).Component,ErrorBoundary:o.hasErrorBoundary?async()=>(await p()).ErrorBoundary:void 0}}let y=at(e,t,r,a,n,o.id,i,s);return y.length>0&&(m.children=y),m})}function ar(e,t,r,a,n){if(n){var o,i,s;let e;return o=r.id,i=t.shouldRevalidate,s=n,e=!1,t=>e?i?i(t):t.defaultShouldRevalidate:(e=!0,s.has(o))}if(!a&&r.hasLoader&&!r.hasClientLoader){let r=e?z(e)[1].map(e=>e.paramName):[],a=e=>r.some(t=>e.currentParams[t]!==e.nextParams[t]);if(!t.shouldRevalidate)return e=>a(e);{let e=t.shouldRevalidate;return t=>e({...t,defaultShouldRevalidate:a(t)})}}if(a&&t.shouldRevalidate){let e=t.shouldRevalidate;return t=>e({...t,defaultShouldRevalidate:!0})}return t.shouldRevalidate}async function aa(e,t){let r=rR(e,t),a=rC(e),n=await r;return await Promise.all([a,rP(e,n)]),{Component:an(n),ErrorBoundary:n.ErrorBoundary,unstable_clientMiddleware:n.unstable_clientMiddleware,clientAction:n.clientAction,clientLoader:n.clientLoader,handle:n.handle,links:n.links,meta:n.meta,shouldRevalidate:n.shouldRevalidate}}function an(e){if(null!=e.default&&("object"!=typeof e.default||0!==Object.keys(e.default).length))return e.default}function ao(e,t,r,a){return a&&"root"!==e||null!=t&&(!0===t.hydrate||!0!==r)}var ai=new Set,as=new Set;function al(e,t,r,a,n){if(!0===r)return async({path:o,patch:i,signal:s,fetcherKey:l})=>{as.has(o)||await ad([o],l?window.location.href:o,e,t,r,a,n,i,s)}}function au(e,t,r,a,n){rZ.useEffect(()=>{var o,i;let s;if(!0!==a||navigator.connection?.saveData===!0)return;function l(e){let t="FORM"===e.tagName?e.getAttribute("action"):e.getAttribute("href");if(!t)return;let r="A"===e.tagName?e.pathname:new URL(t,window.location.origin).pathname;as.has(r)||ai.add(r)}async function u(){document.querySelectorAll("a[data-discover], form[data-discover]").forEach(l);let o=Array.from(ai.keys()).filter(e=>!as.has(e)||(ai.delete(e),!1));if(0!==o.length)try{await ad(o,null,t,r,a,n,e.basename,e.patchRoutes)}catch(e){console.error("Failed to fetch manifest patches",e)}}let c=(o=u,i=100,(...e)=>{window.clearTimeout(s),s=window.setTimeout(()=>o(...e),100)});u();let d=new MutationObserver(()=>c());return d.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>d.disconnect()},[a,n,t,r,e])}var ac="react-router-manifest-version";async function ad(e,t,r,a,n,o,i,s,l){let u,c=new URL(`${null!=i?i:"/"}/__manifest`.replace(/\/+/g,"/"),window.location.origin);if(e.sort().forEach(e=>c.searchParams.append("p",e)),c.searchParams.set("version",r.version),c.toString().length>7680)return void ai.clear();try{let e=await fetch(c,{signal:l});if(e.ok){if(204===e.status&&e.headers.has("X-Remix-Reload-Document")){if(!t)return void console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");if(sessionStorage.getItem(ac)===r.version)return void console.error("Unable to discover routes due to manifest version mismatch.");throw sessionStorage.setItem(ac,r.version),window.location.href=t,Error("Detected manifest version mismatch, reloading...")}else if(e.status>=400)throw Error(await e.text())}else throw Error(`${e.status} ${e.statusText}`);sessionStorage.removeItem(ac),u=await e.json()}catch(e){if(l?.aborted)return;throw e}let d=new Set(Object.keys(r.routes)),h=Object.values(u).reduce((e,t)=>(t&&!d.has(t.id)&&(e[t.id]=t),e),{});Object.assign(r.routes,h),e.forEach(e=>(function(e,t){if(t.size>=1e3){let e=t.values().next().value;t.delete(e)}t.add(e)})(e,as));let f=new Set;Object.values(h).forEach(e=>{!e||e.parentId&&h[e.parentId]||f.add(e.parentId)}),f.forEach(e=>s(e||null,at(h,a,null,n,o,e)))}function ah(){let e=rE.useContext(td);return rx(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function af(){let e=rE.useContext(th);return rx(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var am=rE.createContext(void 0);function ap(){let e=rE.useContext(am);return rx(e,"You must render this element inside a <HydratedRouter> element"),e}function ay(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function av(e,t,r){if(r&&!ax)return[e[0]];if(t){let r=e.findIndex(e=>void 0!==t[e.route.id]);return e.slice(0,r+1)}return e}function ag(){let{isSpaMode:e,manifest:t,routeModules:r,criticalCss:a}=ap(),{errors:n,matches:o}=af(),i=av(o,n,e),s=rE.useMemo(()=>rM(i.map(e=>{let a=r[e.route.id],n=t.routes[e.route.id];return[n&&n.css?n.css.map(e=>({rel:"stylesheet",href:e})):[],a?.links?.()||[]]}).flat(2),rN(i,t)),[i,r,t]);return rE.createElement(rE.Fragment,null,"string"==typeof a?rE.createElement("style",{dangerouslySetInnerHTML:{__html:a}}):null,"object"==typeof a?rE.createElement("link",{rel:"stylesheet",href:a.href}):null,s.map(({key:e,link:t})=>rL(t)?rE.createElement(aw,{key:e,...t}):rE.createElement("link",{key:e,...t})))}function aw({page:e,...t}){let{router:r}=ah(),a=rE.useMemo(()=>$(r.routes,e,r.basename),[r.routes,e,r.basename]);return a?rE.createElement(ab,{page:e,matches:a,...t}):null}function ab({page:e,matches:t,...r}){let a=tR(),{manifest:n,routeModules:o}=ap(),{basename:i}=ah(),{loaderData:s,matches:l}=af(),u=rE.useMemo(()=>rT(e,t,l,n,a,"data"),[e,t,l,n,a]),c=rE.useMemo(()=>rT(e,t,l,n,a,"assets"),[e,t,l,n,a]),d=rE.useMemo(()=>{if(e===a.pathname+a.search+a.hash)return[];let r=new Set,l=!1;if(t.forEach(e=>{let t=n.routes[e.route.id];t&&t.hasLoader&&(!u.some(t=>t.route.id===e.route.id)&&e.route.id in s&&o[e.route.id]?.shouldRevalidate||t.hasClientLoader?l=!0:r.add(e.route.id))}),0===r.size)return[];let c=rJ(e,i);return l&&r.size>0&&c.searchParams.set("_routes",t.filter(e=>r.has(e.route.id)).map(e=>e.route.id).join(",")),[c.pathname+c.search]},[i,s,a,n,u,t,e,o]),h=rE.useMemo(()=>rN(c,n),[c,n]),f=function(e){let{manifest:t,routeModules:r}=ap(),[a,n]=rE.useState([]);return rE.useEffect(()=>{let a=!1;return rj(e,t,r).then(e=>{a||n(e)}),()=>{a=!0}},[e,t,r]),a}(c);return rE.createElement(rE.Fragment,null,d.map(e=>rE.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...r})),h.map(e=>rE.createElement("link",{key:e,rel:"modulepreload",href:e,...r})),f.map(({key:e,link:t})=>rE.createElement("link",{key:e,...t})))}function aE(){let{isSpaMode:e,routeModules:t}=ap(),{errors:r,matches:a,loaderData:n}=af(),o=tR(),i=av(a,r,e),s=null;r&&(s=r[i[i.length-1].route.id]);let l=[],u=null,c=[];for(let e=0;e<i.length;e++){let r=i[e],a=r.route.id,d=n[a],h=r.params,f=t[a],m=[],p={id:a,data:d,meta:[],params:r.params,pathname:r.pathname,handle:r.route.handle,error:s};if(c[e]=p,f?.meta?m="function"==typeof f.meta?f.meta({data:d,params:h,location:o,matches:c,error:s}):Array.isArray(f.meta)?[...f.meta]:f.meta:u&&(m=[...u]),!Array.isArray(m=m||[]))throw Error("The route at "+r.route.path+" returns an invalid value. All route meta functions must return an array of meta objects.\n\nTo reference the meta function API, see https://remix.run/route/meta");p.meta=m,c[e]=p,u=l=[...m]}return rE.createElement(rE.Fragment,null,l.flat().map(e=>{if(!e)return null;if("tagName"in e){var t;let{tagName:r,...a}=e;return"string"==typeof(t=r)&&/^(meta|link)$/.test(t)?rE.createElement(r,{key:JSON.stringify(a),...a}):(console.warn(`A meta object uses an invalid tagName: ${r}. Expected either 'link' or 'meta'`),null)}if("title"in e)return rE.createElement("title",{key:"title"},String(e.title));if("charset"in e&&(e.charSet??(e.charSet=e.charset),delete e.charset),"charSet"in e&&null!=e.charSet)return"string"==typeof e.charSet?rE.createElement("meta",{key:"charSet",charSet:e.charSet}):null;if("script:ld+json"in e)try{let t=JSON.stringify(e["script:ld+json"]);return rE.createElement("script",{key:`script:ld+json:${t}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:t}})}catch(e){return null}return rE.createElement("meta",{key:JSON.stringify(e),...e})}))}am.displayName="FrameworkContext";var ax=!1;function aR(e){let{manifest:t,serverHandoffString:r,isSpaMode:a,ssr:n,renderMeta:o}=ap(),{router:i,static:s,staticContext:l}=ah(),{matches:u}=af(),c=!0===n;o&&(o.didRenderScripts=!0);let d=av(u,null,a);rE.useEffect(()=>{ax=!0},[]);let h=rE.useMemo(()=>{let a=l?`window.__reactRouterContext = ${r};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",n=s?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${!c?`import ${JSON.stringify(t.url)}`:""};
${d.map((e,r)=>{let a=`route${r}`,n=t.routes[e.route.id];rx(n,`Route ${e.route.id} not found in manifest`);let{clientActionModule:o,clientLoaderModule:i,clientMiddlewareModule:s,hydrateFallbackModule:l,module:u}=n,c=[...o?[{module:o,varName:`${a}_clientAction`}]:[],...i?[{module:i,varName:`${a}_clientLoader`}]:[],...s?[{module:s,varName:`${a}_clientMiddleware`}]:[],...l?[{module:l,varName:`${a}_HydrateFallback`}]:[],{module:u,varName:`${a}_main`}];return 1===c.length?`import * as ${a} from ${JSON.stringify(u)};`:[c.map(e=>`import * as ${e.varName} from "${e.module}";`).join("\n"),`const ${a} = {${c.map(e=>`...${e.varName}`).join(",")}};`].join("\n")}).join("\n")}
  ${c?`window.__reactRouterManifest = ${JSON.stringify(function({sri:e,...t},r){let a=new Set(r.state.matches.map(e=>e.route.id)),n=r.state.location.pathname.split("/").filter(Boolean),o=["/"];for(n.pop();n.length>0;)o.push(`/${n.join("/")}`),n.pop();o.forEach(e=>{let t=$(r.routes,e,r.basename);t&&t.forEach(e=>a.add(e.route.id))});let i=[...a].reduce((e,r)=>Object.assign(e,{[r]:t.routes[r]}),{});return{...t,routes:i,sri:!!e||void 0}}(t,i),null,2)};`:""}
  window.__reactRouterRouteModules = {${d.map((e,t)=>`${JSON.stringify(e.route.id)}:route${t}`).join(",")}};

import(${JSON.stringify(t.entry.module)});`:" ";return rE.createElement(rE.Fragment,null,rE.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:a},type:void 0}),rE.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:n},type:"module",async:!0}))},[]),f=ax?[]:[...new Set(t.entry.imports.concat(rN(d,t,{includeHydrateFallback:!0})))],m="object"==typeof t.sri?t.sri:{};return ax?null:rE.createElement(rE.Fragment,null,"object"==typeof t.sri?rE.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:m})}}):null,c?null:rE.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:m[t.url],suppressHydrationWarning:!0}),rE.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:m[t.entry.module],suppressHydrationWarning:!0}),f.map(t=>rE.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:m[t],suppressHydrationWarning:!0})),h)}var aS="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{aS&&(window.__reactRouterVersion="7.5.2")}catch(e){}function aC(e,t){return ew({basename:t?.basename,unstable_getContext:t?.unstable_getContext,future:t?.future,history:E({window:t?.window}),hydrationData:t?.hydrationData||a_(),routes:e,mapRouteProperties:t5,hydrationRouteProperties:t9,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation,window:t?.window}).initialize()}function aP(e,t){return ew({basename:t?.basename,unstable_getContext:t?.unstable_getContext,future:t?.future,history:x({window:t?.window}),hydrationData:t?.hydrationData||a_(),routes:e,mapRouteProperties:t5,hydrationRouteProperties:t9,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation,window:t?.window}).initialize()}function a_(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:function(e){if(!e)return null;let t=Object.entries(e),r={};for(let[e,a]of t)if(a&&"RouteErrorResponse"===a.__type)r[e]=new eo(a.status,a.statusText,a.data,!0===a.internal);else if(a&&"Error"===a.__type){if(a.__subType){let t=window[a.__subType];if("function"==typeof t)try{let n=new t(a.message);n.stack="",r[e]=n}catch(e){}}if(null==r[e]){let t=Error(a.message);t.stack="",r[e]=t}}else r[e]=a;return r}(e.errors)}),e}function aL({basename:e,children:t,window:r}){let a=rf.useRef();null==a.current&&(a.current=E({window:r,v5Compat:!0}));let n=a.current,[o,i]=rf.useState({action:n.action,location:n.location}),s=rf.useCallback(e=>{rf.startTransition(()=>i(e))},[i]);return rf.useLayoutEffect(()=>n.listen(s),[n,s]),rf.createElement(ro,{basename:e,children:t,location:o.location,navigationType:o.action,navigator:n})}function ak({basename:e,children:t,window:r}){let a=rf.useRef();null==a.current&&(a.current=x({window:r,v5Compat:!0}));let n=a.current,[o,i]=rf.useState({action:n.action,location:n.location}),s=rf.useCallback(e=>{rf.startTransition(()=>i(e))},[i]);return rf.useLayoutEffect(()=>n.listen(s),[n,s]),rf.createElement(ro,{basename:e,children:t,location:o.location,navigationType:o.action,navigator:n})}function aj({basename:e,children:t,history:r}){let[a,n]=rf.useState({action:r.action,location:r.location}),o=rf.useCallback(e=>{rf.startTransition(()=>n(e))},[n]);return rf.useLayoutEffect(()=>r.listen(o),[r,o]),rf.createElement(ro,{basename:e,children:t,location:a.location,navigationType:a.action,navigator:r})}aj.displayName="unstable_HistoryRouter";var aT=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,aN=rf.forwardRef(function({onClick:e,discover:t="render",prefetch:r="none",relative:a,reloadDocument:n,replace:o,state:i,target:s,to:l,preventScrollReset:u,viewTransition:c,...d},h){let f,{basename:m}=rf.useContext(ty),p="string"==typeof l&&aT.test(l),y=!1;if("string"==typeof l&&p&&(f=l,aS))try{let e=new URL(window.location.href),t=new URL(l.startsWith("//")?e.protocol+l:l),r=Y(t.pathname,m);t.origin===e.origin&&null!=r?l=r+t.search+t.hash:y=!0}catch(e){S(!1,`<Link to="${l}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let v=tE(l,{relative:a}),[g,w,b]=function(e,t){let r=rE.useContext(am),[a,n]=rE.useState(!1),[o,i]=rE.useState(!1),{onFocus:s,onBlur:l,onMouseEnter:u,onMouseLeave:c,onTouchStart:d}=t,h=rE.useRef(null);rE.useEffect(()=>{if("render"===e&&i(!0),"viewport"===e){let e=new IntersectionObserver(e=>{e.forEach(e=>{i(e.isIntersecting)})},{threshold:.5});return h.current&&e.observe(h.current),()=>{e.disconnect()}}},[e]),rE.useEffect(()=>{if(a){let e=setTimeout(()=>{i(!0)},100);return()=>{clearTimeout(e)}}},[a]);let f=()=>{n(!0)},m=()=>{n(!1),i(!1)};return r?"intent"!==e?[o,h,{}]:[o,h,{onFocus:ay(s,f),onBlur:ay(l,m),onMouseEnter:ay(u,f),onMouseLeave:ay(c,m),onTouchStart:ay(d,f)}]:[!1,h,{}]}(r,d),E=aI(l,{replace:o,state:i,target:s,preventScrollReset:u,relative:a,viewTransition:c}),x=rf.createElement("a",{...d,...b,href:f||v,onClick:y||n?e:function(t){e&&e(t),t.defaultPrevented||E(t)},ref:function(...e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}(h,w),target:s,"data-discover":p||"render"!==t?void 0:"true"});return g&&!p?rf.createElement(rf.Fragment,null,x,rf.createElement(aw,{page:v})):x});aN.displayName="Link";var aM=rf.forwardRef(function({"aria-current":e="page",caseSensitive:t=!1,className:r="",end:a=!1,style:n,to:o,viewTransition:i,children:s,...l},u){let c,d=tM(o,{relative:l.relative}),h=tR(),f=rf.useContext(th),{navigator:m,basename:p}=rf.useContext(ty),y=null!=f&&aQ(d)&&!0===i,v=m.encodeLocation?m.encodeLocation(d).pathname:d.pathname,g=h.pathname,w=f&&f.navigation&&f.navigation.location?f.navigation.location.pathname:null;t||(g=g.toLowerCase(),w=w?w.toLowerCase():null,v=v.toLowerCase()),w&&p&&(w=Y(w,p)||w);let b="/"!==v&&v.endsWith("/")?v.length-1:v.length,E=g===v||!a&&g.startsWith(v)&&"/"===g.charAt(b),x=null!=w&&(w===v||!a&&w.startsWith(v)&&"/"===w.charAt(v.length)),R={isActive:E,isPending:x,isTransitioning:y},S=E?e:void 0;c="function"==typeof r?r(R):[r,E?"active":null,x?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let C="function"==typeof n?n(R):n;return rf.createElement(aN,{...l,"aria-current":S,className:c,ref:u,style:C,to:o,viewTransition:i},"function"==typeof s?s(R):s)});aM.displayName="NavLink";var aO=rf.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:a,replace:n,state:o,method:i=rm,action:s,onSubmit:l,relative:u,preventScrollReset:c,viewTransition:d,...h},f)=>{let m=aB(),p=aY(s,{relative:u}),y="get"===i.toLowerCase()?"get":"post",v="string"==typeof s&&aT.test(s);return rf.createElement("form",{ref:f,method:y,action:p,onSubmit:a?l:e=>{if(l&&l(e),e.defaultPrevented)return;e.preventDefault();let a=e.nativeEvent.submitter,s=a?.getAttribute("formmethod")||i;m(a||e.currentTarget,{fetcherKey:t,method:s,navigate:r,replace:n,state:o,relative:u,preventScrollReset:c,viewTransition:d})},...h,"data-discover":v||"render"!==e?void 0:"true"})});function a$({getKey:e,storageKey:t,...r}){let a=rf.useContext(am),{basename:n}=rf.useContext(ty),o=tR(),i=tW();aG({getKey:e,storageKey:t});let s=rf.useMemo(()=>{if(!a||!e)return null;let t=aX(o,i,n,e);return t!==o.key?t:null},[]);if(!a||a.isSpaMode)return null;let l=((e,t)=>{if(!window.history.state||!window.history.state.key){let e=Math.random().toString(32).slice(2);window.history.replaceState({key:e},"")}try{let r=JSON.parse(sessionStorage.getItem(e)||"{}")[t||window.history.state.key];"number"==typeof r&&window.scrollTo(0,r)}catch(t){console.error(t),sessionStorage.removeItem(e)}}).toString();return rf.createElement("script",{...r,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:`(${l})(${JSON.stringify(t||aJ)}, ${JSON.stringify(s)})`}})}function aD(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function aA(e){let t=rf.useContext(td);return R(t,aD(e)),t}function aF(e){let t=rf.useContext(th);return R(t,aD(e)),t}function aI(e,{target:t,replace:r,state:a,preventScrollReset:n,relative:o,viewTransition:i}={}){let s=tL(),l=tR(),u=tM(e,{relative:o});return rf.useCallback(c=>{0!==c.button||t&&"_self"!==t||c.metaKey||c.altKey||c.ctrlKey||c.shiftKey||(c.preventDefault(),s(e,{replace:void 0!==r?r:_(l)===_(u),state:a,preventScrollReset:n,relative:o,viewTransition:i}))},[l,s,u,r,a,t,e,n,o,i])}function aH(e){S("undefined"!=typeof URLSearchParams,"You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let t=rf.useRef(rv(e)),r=rf.useRef(!1),a=tR(),n=rf.useMemo(()=>{var e,n;let o;return e=a.search,n=r.current?null:t.current,o=rv(e),n&&n.forEach((e,t)=>{o.has(t)||n.getAll(t).forEach(e=>{o.append(t,e)})}),o},[a.search]),o=tL(),i=rf.useCallback((e,t)=>{let a=rv("function"==typeof e?e(n):e);r.current=!0,o("?"+a,t)},[o,n]);return[n,i]}aO.displayName="Form",a$.displayName="ScrollRestoration";var aU=0,az=()=>`__${String(++aU)}__`;function aB(){let{router:e}=aA("useSubmit"),{basename:t}=rf.useContext(ty),r=tB("useRouteId");return rf.useCallback(async(a,n={})=>{let{action:o,method:i,encType:s,formData:l,body:u}=function(e,t){let r,a,n,o,i;if(ry(e)&&"form"===e.tagName.toLowerCase()){let i=e.getAttribute("action");a=i?Y(i,t):null,r=e.getAttribute("method")||rm,n=rb(e.getAttribute("enctype"))||rp,o=new FormData(e)}else if(ry(e)&&"button"===e.tagName.toLowerCase()||ry(e)&&"input"===e.tagName.toLowerCase()&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||i.getAttribute("action");if(a=s?Y(s,t):null,r=e.getAttribute("formmethod")||i.getAttribute("method")||rm,n=rb(e.getAttribute("formenctype"))||rb(i.getAttribute("enctype"))||rp,o=new FormData(i,e),!function(){if(null===rg)try{new FormData(document.createElement("form"),0),rg=!1}catch(e){rg=!0}return rg}()){let{name:t,type:r,value:a}=e;if("image"===r){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,a)}}else if(ry(e))throw Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');else r=rm,a=null,n=rp,i=e;return o&&"text/plain"===n&&(i=o,o=void 0),{action:a,method:r.toLowerCase(),encType:n,formData:o,body:i}}(a,t);if(!1===n.navigate){let t=n.fetcherKey||az();await e.fetch(t,r,n.action||o,{preventScrollReset:n.preventScrollReset,formData:l,body:u,formMethod:n.method||i,formEncType:n.encType||s,flushSync:n.flushSync})}else await e.navigate(n.action||o,{preventScrollReset:n.preventScrollReset,formData:l,body:u,formMethod:n.method||i,formEncType:n.encType||s,replace:n.replace,state:n.state,fromRouteId:r,flushSync:n.flushSync,viewTransition:n.viewTransition})},[e,t,r])}function aY(e,{relative:t}={}){let{basename:r}=rf.useContext(ty),a=rf.useContext(tg);R(a,"useFormAction must be used inside a RouteContext");let[n]=a.matches.slice(-1),o={...tM(e||".",{relative:t})},i=tR();if(null==e){o.search=i.search;let e=new URLSearchParams(o.search),t=e.getAll("index");if(t.some(e=>""===e)){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let r=e.toString();o.search=r?`?${r}`:""}}return(!e||"."===e)&&n.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),"/"!==r&&(o.pathname="/"===o.pathname?r:G([r,o.pathname])),_(o)}function aq({key:e}={}){let{router:t}=aA("useFetcher"),r=aF("useFetcher"),a=rf.useContext(tm),n=rf.useContext(tg),o=n.matches[n.matches.length-1]?.route.id;R(a,"useFetcher must be used inside a FetchersContext"),R(n,"useFetcher must be used inside a RouteContext"),R(null!=o,'useFetcher can only be used on routes that contain a unique "id"');let i=rf.useId(),[s,l]=rf.useState(e||i);e&&e!==s&&l(e),rf.useEffect(()=>(t.getFetcher(s),()=>t.deleteFetcher(s)),[t,s]);let u=rf.useCallback(async(e,r)=>{R(o,"No routeId available for fetcher.load()"),await t.fetch(s,o,e,r)},[s,o,t]),c=aB(),d=rf.useCallback(async(e,t)=>{await c(e,{...t,navigate:!1,fetcherKey:s})},[s,c]),h=rf.useMemo(()=>{let e=rf.forwardRef((e,t)=>rf.createElement(aO,{...e,navigate:!1,fetcherKey:s,ref:t}));return e.displayName="fetcher.Form",e},[s]),f=r.fetchers.get(s)||ef,m=a.get(s);return rf.useMemo(()=>({Form:h,submit:d,load:u,...f,data:m}),[h,d,u,f,m])}function aW(){return Array.from(aF("useFetchers").fetchers.entries()).map(([e,t])=>({...t,key:e}))}var aJ="react-router-scroll-positions",aV={};function aX(e,t,r,a){let n=null;return a&&(n=a("/"!==r?{...e,pathname:Y(e.pathname,r)||e.pathname}:e,t)),null==n&&(n=e.key),n}function aG({getKey:e,storageKey:t}={}){let{router:r}=aA("useScrollRestoration"),{restoreScrollPosition:a,preventScrollReset:n}=aF("useScrollRestoration"),{basename:o}=rf.useContext(ty),i=tR(),s=tW(),l=tY();rf.useEffect(()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"}),[]),function(e,t){let{capture:r}={};rf.useEffect(()=>{let t=null!=r?{capture:r}:void 0;return window.addEventListener("pagehide",e,t),()=>{window.removeEventListener("pagehide",e,t)}},[e,r])}(rf.useCallback(()=>{"idle"===l.state&&(aV[aX(i,s,o,e)]=window.scrollY);try{sessionStorage.setItem(t||aJ,JSON.stringify(aV))}catch(e){S(!1,`Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${e}).`)}window.history.scrollRestoration="auto"},[l.state,e,o,i,s,t])),"undefined"!=typeof document&&(rf.useLayoutEffect(()=>{try{let e=sessionStorage.getItem(t||aJ);e&&(aV=JSON.parse(e))}catch(e){}},[t]),rf.useLayoutEffect(()=>{let t=r?.enableScrollRestoration(aV,()=>window.scrollY,e?(t,r)=>aX(t,r,o,e):void 0);return()=>t&&t()},[r,o,e]),rf.useLayoutEffect(()=>{if(!1!==a){if("number"==typeof a)return void window.scrollTo(0,a);if(i.hash){let e=document.getElementById(decodeURIComponent(i.hash.slice(1)));if(e)return void e.scrollIntoView()}!0!==n&&window.scrollTo(0,0)}},[i,a,n]))}function aK(e,t){let{capture:r}=t||{};rf.useEffect(()=>{let t=null!=r?{capture:r}:void 0;return window.addEventListener("beforeunload",e,t),()=>{window.removeEventListener("beforeunload",e,t)}},[e,r])}function aZ({when:e,message:t}){let r=t0(e);rf.useEffect(()=>{"blocked"===r.state&&(window.confirm(t)?setTimeout(r.proceed,0):r.reset())},[r,t]),rf.useEffect(()=>{"blocked"!==r.state||e||r.reset()},[r,e])}function aQ(e,t={}){let r=rf.useContext(tf);R(null!=r,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=aA("useViewTransitionState"),n=tM(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=Y(r.currentLocation.pathname,a)||r.currentLocation.pathname,i=Y(r.nextLocation.pathname,a)||r.nextLocation.pathname;return null!=U(n.pathname,i)||null!=U(n.pathname,o)}var a0=f(r(43210));function a1({basename:e,children:t,location:r="/"}){"string"==typeof r&&(r=L(r));let a={pathname:r.pathname||"/",search:r.search||"",hash:r.hash||"",state:null!=r.state?r.state:null,key:r.key||"default"},n=a3();return a0.createElement(ro,{basename:e,children:t,location:a,navigationType:"POP",navigator:n,static:!0})}function a2({context:e,router:t,hydrate:r=!0,nonce:a}){R(t&&e,"You must provide `router` and `context` to <StaticRouterProvider>");let n={router:t,navigator:a3(),static:!0,staticContext:e,basename:e.basename||"/"},o=new Map,i="";if(!1!==r){let t=JSON.stringify(JSON.stringify({loaderData:e.loaderData,actionData:e.actionData,errors:function(e){if(!e)return null;let t=Object.entries(e),r={};for(let[e,a]of t)ei(a)?r[e]={...a,__type:"RouteErrorResponse"}:a instanceof Error?r[e]={message:a.message,__type:"Error",..."Error"!==a.name?{__subType:a.name}:{}}:r[e]=a;return r}(e.errors)})).replace(nt,e=>ne[e]);i=`window.__staticRouterHydrationData = JSON.parse(${t});`}let{state:s}=n.router;return a0.createElement(a0.Fragment,null,a0.createElement(td.Provider,{value:n},a0.createElement(th.Provider,{value:s},a0.createElement(tm.Provider,{value:o},a0.createElement(tf.Provider,{value:{isTransitioning:!1}},a0.createElement(ro,{basename:n.basename,location:s.location,navigationType:s.historyAction,navigator:n.navigator,static:n.static},a0.createElement(a4,{routes:t.routes,future:t.future,state:s})))))),i?a0.createElement("script",{suppressHydrationWarning:!0,nonce:a,dangerouslySetInnerHTML:{__html:i}}):null)}function a4({routes:e,future:t,state:r}){return t$(e,void 0,r,t)}function a3(){return{createHref:a8,encodeLocation:a7,push(e){throw Error(`You cannot use navigator.push() on the server because it is a stateless environment. This error was probably triggered when you did a \`navigate(${JSON.stringify(e)})\` somewhere in your app.`)},replace(e){throw Error(`You cannot use navigator.replace() on the server because it is a stateless environment. This error was probably triggered when you did a \`navigate(${JSON.stringify(e)}, { replace: true })\` somewhere in your app.`)},go(e){throw Error(`You cannot use navigator.go() on the server because it is a stateless environment. This error was probably triggered when you did a \`navigate(${e})\` somewhere in your app.`)},back(){throw Error("You cannot use navigator.back() on the server because it is a stateless environment.")},forward(){throw Error("You cannot use navigator.forward() on the server because it is a stateless environment.")}}}function a5(e,t){return eb(e,{...t,mapRouteProperties:t5})}function a9(e,t,r={}){let a={},n=O(e,t5,void 0,a),o=t.matches.map(e=>{let t=a[e.route.id]||e.route;return{...e,route:t}}),i=e=>`You cannot use router.${e}() on the server because it is a stateless environment`;return{get basename(){return t.basename},get future(){return{unstable_middleware:!1,...r?.future}},get state(){return{historyAction:"POP",location:t.location,matches:o,loaderData:t.loaderData,actionData:t.actionData,errors:t.errors,initialized:!0,navigation:eh,restoreScrollPosition:null,preventScrollReset:!1,revalidation:"idle",fetchers:new Map,blockers:new Map}},get routes(){return n},get window(){return},initialize(){throw i("initialize")},subscribe(){throw i("subscribe")},enableScrollRestoration(){throw i("enableScrollRestoration")},navigate(){throw i("navigate")},fetch(){throw i("fetch")},revalidate(){throw i("revalidate")},createHref:a8,encodeLocation:a7,getFetcher:()=>ef,deleteFetcher(){throw i("deleteFetcher")},dispose(){throw i("dispose")},getBlocker:()=>em,deleteBlocker(){throw i("deleteBlocker")},patchRoutes(){throw i("patchRoutes")},_internalFetchControllers:new Map,_internalSetRoutes(){throw i("_internalSetRoutes")}}}function a8(e){return"string"==typeof e?e:_(e)}function a7(e){let t="string"==typeof e?e:_(e),r=a6.test(t=t.replace(/ $/,"%20"))?new URL(t):new URL(t,"http://localhost");return{pathname:r.pathname,search:r.search,hash:r.hash}}var a6=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ne={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},nt=/[&><\u2028\u2029]/g,nr=f(r(43210));function na({context:e,url:t,nonce:r}){"string"==typeof t&&(t=new URL(t));let{manifest:a,routeModules:n,criticalCss:o,serverHandoffString:i}=e,s=function e(t,r,a,n,o="",i=r9(t),s=Promise.resolve({Component:()=>null})){return(i[o]||[]).map(o=>{let l=r[o.id];rx(l,"No `routeModule` available to create server routes");let u={...r8(o,l,n),caseSensitive:o.caseSensitive,id:o.id,index:o.index,path:o.path,handle:l.handle,lazy:n?()=>s:void 0,loader:o.hasLoader||o.hasClientLoader?()=>null:void 0},c=e(t,r,a,n,o.id,i,s);return c.length>0&&(u.children=c),u})}(a.routes,n,e.future,e.isSpaMode);for(let t of(e.staticHandlerContext.loaderData={...e.staticHandlerContext.loaderData},e.staticHandlerContext.matches)){let r=t.route.id,a=n[r],o=e.manifest.routes[r];a&&o&&ao(r,a.clientLoader,o.hasLoader,e.isSpaMode)&&(a.HydrateFallback||!o.hasLoader)&&delete e.staticHandlerContext.loaderData[r]}let l=a9(s,e.staticHandlerContext);return nr.createElement(nr.Fragment,null,nr.createElement(am.Provider,{value:{manifest:a,routeModules:n,criticalCss:o,serverHandoffString:i,future:e.future,ssr:e.ssr,isSpaMode:e.isSpaMode,serializeError:e.serializeError,renderMeta:e.renderMeta}},nr.createElement(r1,{location:l.state.location},nr.createElement(a2,{router:l,context:e.staticHandlerContext,hydrate:!1}))),e.serverHandoffStream?nr.createElement(nr.Suspense,null,nr.createElement(rU,{context:e,identifier:0,reader:e.serverHandoffStream.getReader(),textDecoder:new TextDecoder,nonce:r})):null)}var nn=f(r(43210));function no(e,t){return function({initialEntries:r,initialIndex:a,hydrationData:n,future:o}){let i=nn.useRef(),s=nn.useRef();return null==i.current&&(s.current={future:{unstable_subResourceIntegrity:o?.unstable_subResourceIntegrity===!0,unstable_middleware:o?.unstable_middleware===!0},manifest:{routes:{},entry:{imports:[],module:""},url:"",version:""},routeModules:{},ssr:!1,isSpaMode:!1},i.current=t8(function e(t,r,a,n){return t.map(t=>{if(!t.id)throw Error("Expected a route.id in @remix-run/testing processRoutes() function");let o={id:t.id,path:t.path,index:t.index,Component:t.Component,HydrateFallback:t.HydrateFallback,ErrorBoundary:t.ErrorBoundary,action:t.action,loader:t.loader,handle:t.handle,shouldRevalidate:t.shouldRevalidate},i={id:t.id,path:t.path,index:t.index,parentId:n,hasAction:null!=t.action,hasLoader:null!=t.loader,hasClientAction:!1,hasClientLoader:!1,hasClientMiddleware:!1,hasErrorBoundary:null!=t.ErrorBoundary,module:"build/stub-path-to-module.js",clientActionModule:void 0,clientLoaderModule:void 0,clientMiddlewareModule:void 0,hydrateFallbackModule:void 0};return r.routes[o.id]=i,a[t.id]={default:t.Component||ra,ErrorBoundary:t.ErrorBoundary||void 0,handle:t.handle,links:t.links,meta:t.meta,shouldRevalidate:t.shouldRevalidate},t.children&&(o.children=e(t.children,r,a,o.id)),o})}(O(e,e=>e),s.current.manifest,s.current.routeModules),{unstable_getContext:t,initialEntries:r,initialIndex:a,hydrationData:n})),nn.createElement(am.Provider,{value:s.current},nn.createElement(t6,{router:i.current}))}}var ni=r(33057),ns=new TextEncoder,nl=async(e,t)=>{let r=ns.encode(e),a=await nc(t,["sign"]);return e+"."+btoa(String.fromCharCode(...new Uint8Array(await crypto.subtle.sign("HMAC",a,r)))).replace(/=+$/,"")},nu=async(e,t)=>{let r=e.lastIndexOf("."),a=e.slice(0,r),n=e.slice(r+1),o=ns.encode(a),i=await nc(t,["verify"]),s=function(e){let t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}(atob(n));return!!await crypto.subtle.verify("HMAC",i,s,o)&&a},nc=async(e,t)=>crypto.subtle.importKey("raw",ns.encode(e),{name:"HMAC",hash:"SHA-256"},!1,t),nd=(e,t={})=>{var r;let{secrets:a=[],...n}={path:"/",sameSite:"lax",...t};return r=e,t3(!n.expires,`The "${r}" cookie has an "expires" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \`commitSession(session, { expires })\` if using a session storage object, or \`cookie.serialize("value", { expires })\` if you're using the cookie directly.`),{get name(){return e},get isSigned(){return a.length>0},get expires(){return void 0!==n.maxAge?new Date(Date.now()+1e3*n.maxAge):n.expires},async parse(t,r){if(!t)return null;let o=(0,ni.parse)(t,{...n,...r});if(!(e in o))return null;{let t=o[e];return"string"==typeof t&&""!==t?await nm(t,a):""}},serialize:async(t,r)=>(0,ni.serialize)(e,""===t?"":await nf(t,a),{...n,...r})}},nh=e=>null!=e&&"string"==typeof e.name&&"boolean"==typeof e.isSigned&&"function"==typeof e.parse&&"function"==typeof e.serialize;async function nf(e,t){let r=btoa(function(e){let t,r,a=e.toString(),n="",o=0;for(;o<a.length;){if("%"===(t=a.charAt(o++))){if("u"===a.charAt(o)){if(r=a.slice(o+1,o+5),/^[\da-f]{4}$/i.exec(r)){n+=String.fromCharCode(parseInt(r,16)),o+=5;continue}}else if(r=a.slice(o,o+2),/^[\da-f]{2}$/i.exec(r)){n+=String.fromCharCode(parseInt(r,16)),o+=2;continue}}n+=t}return n}(encodeURIComponent(JSON.stringify(e))));return t.length>0&&(r=await nl(r,t[0])),r}async function nm(e,t){if(t.length>0){for(let r of t){let t=await nu(e,r);if(!1!==t)return np(t)}return null}return np(e)}function np(e){try{return JSON.parse(decodeURIComponent(function(e){let t,r,a=e.toString(),n="",o=0;for(;o<a.length;)t=a.charAt(o++),/[\w*+\-./@]/.exec(t)?n+=t:(r=t.charCodeAt(0))<256?n+="%"+ny(r,2):n+="%u"+ny(r,4).toUpperCase();return n}(atob(e))))}catch(e){return{}}}function ny(e,t){let r=e.toString(16);for(;r.length<t;)r="0"+r;return r}var nv=(e=>(e.Development="development",e.Production="production",e.Test="test",e))(nv||{});function ng(e,t){if(e instanceof Error&&"development"!==t){let e=Error("Unexpected Server Error");return e.stack=void 0,e}return e}function nw(e,t){return Object.entries(e).reduce((e,[r,a])=>Object.assign(e,{[r]:ng(a,t)}),{})}function nb(e,t){let r=ng(e,t);return{message:r.message,stack:r.stack}}function nE(e,t){if(!e)return null;let r=Object.entries(e),a={};for(let[e,n]of r)if(ei(n))a[e]={...n,__type:"RouteErrorResponse"};else if(n instanceof Error){let r=ng(n,t);a[e]={message:r.message,stack:r.stack,__type:"Error",..."Error"!==r.name?{__subType:r.name}:{}}}else a[e]=n;return a}function nx(e,t,r){let a=$(e,t,r);return a?a.map(e=>({params:e.params,pathname:e.pathname,route:e.route})):null}async function nR(e,t){var r;let a,n,o=await e({request:(r=function(e){let t=new URL(e.url),r=t.searchParams.getAll("index");t.searchParams.delete("index");let a=[];for(let e of r)e&&a.push(e);for(let e of a)t.searchParams.append("index",e);let n={method:e.method,body:e.body,headers:e.headers,signal:e.signal};return n.body&&(n.duplex="half"),new Request(t.href,n)}(t.request),(a=new URL(r.url)).searchParams.delete("_routes"),(n={method:r.method,body:r.body,headers:r.headers,signal:r.signal}).body&&(n.duplex="half"),new Request(a.href,n)),params:t.params,context:t.context});if(e8(o)&&o.init&&o.init.status&&e6(o.init.status))throw new Response(null,o.init);return o}function nS(e,t){if(!1===e||null==e)throw console.error("The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose"),Error(t)}var nC="__reactRouterDevServerHooks";function nP(e){globalThis[nC]=e}function n_(){return globalThis[nC]}function nL(e,t){if("undefined"!=typeof process)try{if(process.env?.IS_RR_BUILD_REQUEST==="yes")return e.headers.get(t)}catch(e){}return null}function nk(e){let t={};return Object.values(e).forEach(e=>{if(e){let r=e.parentId||"";t[r]||(t[r]=[]),t[r].push(e)}}),t}var nj={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},nT=/[&><\u2028\u2029]/g;function nN(e){return JSON.stringify(e).replace(nT,e=>nj[e])}var nM=r(14791),nO=r(28356);function n$(e,t){let r,a=t.errors?t.matches.findIndex(e=>t.errors[e.route.id]):-1,n=a>=0?t.matches.slice(0,a+1):t.matches;if(a>=0){let{actionHeaders:e,actionData:n,loaderHeaders:o,loaderData:i}=t;t.matches.slice(a).some(t=>{let a=t.route.id;return!e[a]||n&&n.hasOwnProperty(a)?o[a]&&!i.hasOwnProperty(a)&&(r=o[a]):r=e[a],null!=r})}return n.reduce((a,o,i)=>{let{id:s}=o.route,l=e.routes[s];nS(l,`Route with id "${s}" not found in build`);let u=l.module,c=t.loaderHeaders[s]||new Headers,d=t.actionHeaders[s]||new Headers,h=null!=r&&i===n.length-1,f=h&&r!==c&&r!==d;if(null==u.headers){let e=new Headers(a);return f&&nD(r,e),nD(d,e),nD(c,e),e}let m=new Headers(u.headers?"function"==typeof u.headers?u.headers({loaderHeaders:c,parentHeaders:a,actionHeaders:d,errorHeaders:h?r:void 0}):u.headers:void 0);return f&&nD(r,m),nD(d,m),nD(c,m),nD(a,m),m},new Headers)}function nD(e,t){let r=e.get("Set-Cookie");if(r){let e=(0,nO.splitCookiesString)(r),a=new Set(t.getSetCookie());e.forEach(e=>{a.has(e)||t.append("Set-Cookie",e)})}}var nA=new Set([...rH,304]);async function nF(e,t,r,a,n,o,i){try{let s=function(r){let n,o=n$(e,r);return e6(r.statusCode)&&o.has("Location")?nH(a,e,t,{result:nU(r.statusCode,o,e.basename),headers:o,status:202}):(r.errors&&(Object.values(r.errors).forEach(e=>{(!ei(e)||e.error)&&i(e)}),r.errors=nw(r.errors,t)),n=r.errors?{error:Object.values(r.errors)[0]}:{data:Object.values(r.actionData||{})[0]},nH(a,e,t,{result:n,headers:o,status:r.statusCode}))},l=new Request(n,{method:a.method,body:a.body,headers:a.headers,signal:a.signal,...a.body?{duplex:"half"}:void 0}),u=await r.query(l,{requestContext:o,skipLoaderErrorBubbling:!0,skipRevalidation:!0,unstable_respond:s});if(e7(u)||(u=s(u)),te(u))return nH(a,e,t,{result:nU(u.status,u.headers,e.basename),headers:u.headers,status:202});return u}catch(r){return i(r),nH(a,e,t,{result:{error:r},headers:new Headers,status:500})}}async function nI(e,t,r,a,n,o,i){try{let s=function(r){let n=n$(e,r);if(e6(r.statusCode)&&n.has("Location"))return nH(a,e,t,{result:{[rI]:nU(r.statusCode,n,e.basename)},headers:n,status:202});r.errors&&(Object.values(r.errors).forEach(e=>{(!ei(e)||e.error)&&i(e)}),r.errors=nw(r.errors,t));let o={},s=new Set(r.matches.filter(e=>c?c.has(e.route.id):null!=e.route.loader).map(e=>e.route.id));if(r.errors)for(let[e,t]of Object.entries(r.errors))o[e]={error:t};for(let[e,t]of Object.entries(r.loaderData))!(e in o)&&s.has(e)&&(o[e]={data:t});return nH(a,e,t,{result:o,headers:n,status:r.statusCode})},l=new Request(n,{headers:a.headers,signal:a.signal}),u=new URL(a.url).searchParams.get("_routes"),c=u?new Set(u.split(",")):null,d=await r.query(l,{requestContext:o,filterMatchesToLoad:e=>!c||c.has(e.route.id),skipLoaderErrorBubbling:!0,unstable_respond:s});if(e7(d)||(d=s(d)),te(d))return nH(a,e,t,{result:{[rI]:nU(d.status,d.headers,e.basename)},headers:d.headers,status:202});return d}catch(r){return i(r),nH(a,e,t,{result:{root:{error:r}},headers:new Headers,status:500})}}function nH(e,t,r,{result:a,headers:n,status:o}){let i=new Headers(n);return(i.set("X-Remix-Response","yes"),nA.has(o))?new Response(null,{status:o,headers:i}):(i.set("Content-Type","text/x-script"),new Response(nz(a,e.signal,t.entry.module.streamTimeout,r),{status:o||200,headers:i}))}function nU(e,t,r){let a=t.get("Location");return r&&(a=Y(a,r)||a),{redirect:a,status:e,revalidate:t.has("X-Remix-Revalidate")||t.has("Set-Cookie"),reload:t.has("X-Remix-Reload-Document"),replace:t.has("X-Remix-Replace")}}function nz(e,t,r,a){let n=new AbortController,o=setTimeout(()=>n.abort(Error("Server Timeout")),"number"==typeof r?r:4950);return t.addEventListener("abort",()=>clearTimeout(o)),(0,nM.encode)(e,{signal:n.signal,plugins:[e=>{if(e instanceof Error){let{name:t,message:r,stack:n}="production"===a?ng(e,a):e;return["SanitizedError",t,r,n]}if(e instanceof eo){let{data:t,status:r,statusText:a}=e;return["ErrorResponse",t,r,a]}if(e&&"object"==typeof e&&rI in e)return["SingleFetchRedirect",e[rI]]}],postPlugins:[e=>{if(e&&"object"==typeof e)return["SingleFetchClassInstance",Object.fromEntries(Object.entries(e))]},()=>["SingleFetchFallback"]]})}function nB(e,t){let r=function e(t,r="",a=nk(t)){return(a[r]||[]).map(r=>({...r,children:e(t,r.id,a)}))}(e.routes),a=function e(t,r,a="",n=nk(t)){return(n[a]||[]).map(a=>{let o={hasErrorBoundary:"root"===a.id||null!=a.module.ErrorBoundary,id:a.id,path:a.path,unstable_middleware:a.module.unstable_middleware,loader:a.module.loader?async e=>{let t=nL(e.request,"X-React-Router-Prerender-Data");if(null!=t){let e=t?decodeURI(t):t;nS(e,"Missing prerendered data for route");let r=new TextEncoder().encode(e),n=new ReadableStream({start(e){e.enqueue(r),e.close()}}),o=(await rX(n,global)).value;if(o&&rI in o){let e=o[rI],t={status:e.status};if(e.reload)throw ea(e.redirect,t);if(e.replace)throw en(e.redirect,t);throw er(e.redirect,t)}{nS(o&&a.id in o,"Unable to decode prerendered data");let e=o[a.id];return nS("data"in e,"Unable to process prerendered data"),e.data}}return await nR(a.module.loader,e)}:void 0,action:a.module.action?e=>nR(a.module.action,e):void 0,handle:a.module.handle};return a.index?{index:!0,...o}:{caseSensitive:a.caseSensitive,children:e(t,r,a.id,n),...o}})}(e.routes,e.future),n="development"===t||"production"===t||"test"===t?t:"production",o=eb(a,{basename:e.basename}),i=e.entry.module.handleError||((e,{request:t})=>{"test"===n||t.signal.aborted||console.error(ei(e)&&e.error?e.error:e)});return{routes:r,dataRoutes:a,serverMode:n,staticHandler:o,errorHandler:i}}var nY=(e,t)=>{let r,a,n,o,i;return async function(s,l){let u,c;if(r="function"==typeof e?await e():e,"function"==typeof e){let e=nB(r,t);a=e.routes,n=e.serverMode,o=e.staticHandler,i=e.errorHandler}else if(!a||!n||!o||!i){let e=nB(r,t);a=e.routes,n=e.serverMode,o=e.staticHandler,i=e.errorHandler}let d={},h=e=>{"development"===t&&n_()?.processRequestError?.(e),i(e,{context:u,params:d,request:s})};if(r.future.unstable_middleware)if(null==l)u=new T;else try{u=new T(l)}catch(t){let e=Error(`Unable to create initial \`unstable_RouterContextProvider\` instance. Please confirm you are returning an instance of \`Map<unstable_routerContext, unknown>\` from your \`getLoadContext\` function.

Error: ${t instanceof Error?t.toString():t}`);return h(e),nX(e,n)}else u=l||{};let f=new URL(s.url),m=r.basename||"/",p=f.pathname;"/_root.data"===Y(p,m)?p=m:p.endsWith(".data")&&(p=p.replace(/\.data$/,"")),"/"!==Y(p,m)&&p.endsWith("/")&&(p=p.slice(0,-1));let y="yes"===nL(s,"X-React-Router-SPA-Mode");if(!r.ssr){if(0===r.prerender.length)y=!0;else if(!r.prerender.includes(p)&&!r.prerender.includes(p+"/"))if(f.pathname.endsWith(".data"))return i(new eo(404,"Not Found",`Refusing to SSR the path \`${p}\` because \`ssr:false\` is set and the path is not included in the \`prerender\` config, so in production the path will be a 404.`),{context:u,params:d,request:s}),new Response("Not Found",{status:404,statusText:"Not Found"});else y=!0}let v=`${m}/__manifest`.replace(/\/+/g,"/");if(f.pathname===v)try{return await nq(r,a,f)}catch(e){return h(e),new Response("Unknown Server Error",{status:500})}let g=nx(a,f.pathname,r.basename);if(g&&g.length>0&&Object.assign(d,g[0].params),f.pathname.endsWith(".data")){let e=new URL(s.url);e.pathname=p;let t=nx(a,e.pathname,r.basename);if(c=await nW(n,r,o,s,e,u,h),r.entry.module.handleDataRequest&&te(c=await r.entry.module.handleDataRequest(c,{context:u,params:t?t[0].params:{},request:s}))){let e=nU(c.status,c.headers,r.basename);"GET"===s.method&&(e={[rI]:e});let t=new Headers(c.headers);return t.set("Content-Type","text/x-script"),new Response(nz(e,s.signal,r.entry.module.streamTimeout,n),{status:202,headers:t})}}else if(!y&&g&&null==g[g.length-1].route.module.default&&null==g[g.length-1].route.module.ErrorBoundary)c=await nV(n,r,o,g.slice(-1)[0].route.id,s,u,h);else{let e,{pathname:a}=f;r.unstable_getCriticalCss?e=await r.unstable_getCriticalCss({pathname:a}):"development"===t&&n_()?.getCriticalCss&&(e=await n_()?.getCriticalCss?.(a)),c=await nJ(n,r,o,s,u,h,y,e)}return"HEAD"===s.method?new Response(null,{headers:c.headers,status:c.status,statusText:c.statusText}):c}};async function nq(e,t,r){if(e.assets.version!==r.searchParams.get("version"))return new Response(null,{status:204,headers:{"X-Remix-Reload-Document":"true"}});let a={};if(r.searchParams.has("p")){let n=new Set;for(let o of(r.searchParams.getAll("p").forEach(e=>{e.startsWith("/")||(e=`/${e}`);let t=e.split("/").slice(1);t.forEach((e,r)=>{let a=t.slice(0,r+1).join("/");n.add(`/${a}`)})}),n)){let r=nx(t,o,e.basename);if(r)for(let t of r){let r=t.route.id,n=e.assets.routes[r];n&&(a[r]=n)}}return Response.json(a,{headers:{"Cache-Control":"public, max-age=31536000, immutable"}})}return new Response("Invalid Request",{status:400})}async function nW(e,t,r,a,n,o,i){return"GET"!==a.method?await nF(t,e,r,a,n,o,i):await nI(t,e,r,a,n,o,i)}async function nJ(e,t,r,a,n,o,i,s){try{let e=await r.query(a,{requestContext:n,unstable_respond:t.future.unstable_middleware?e=>l(e,i):void 0});return e7(e)?e:l(e,i)}catch(e){return o(e),new Response(null,{status:500})}async function l(i,l){var u,c;if(e7(i))return i;let d=n$(t,i);if(nA.has(i.statusCode))return new Response(null,{status:i.statusCode,headers:d});i.errors&&(Object.values(i.errors).forEach(e=>{(!ei(e)||e.error)&&o(e)}),i.errors=nw(i.errors,e));let h={loaderData:i.loaderData,actionData:i.actionData,errors:nE(i.errors,e)},f={manifest:t.assets,routeModules:Object.keys(u=t.routes).reduce((e,t)=>{let r=u[t];return r&&(e[t]=r.module),e},{}),staticHandlerContext:i,criticalCss:s,serverHandoffString:nN({basename:t.basename,criticalCss:s,future:t.future,ssr:t.ssr,isSpaMode:l}),serverHandoffStream:nz(h,a.signal,t.entry.module.streamTimeout,e),renderMeta:{},future:t.future,ssr:t.ssr,isSpaMode:l,serializeError:t=>nb(t,e)},m=t.entry.module.default;try{return await m(a,i.statusCode,d,f,n)}catch(h){o(h);let s=h;if(e7(h))try{let e,t=await ((e=(c=h).headers.get("Content-Type"))&&/\bapplication\/json\b/.test(e)?null==c.body?null:c.json():c.text());s=new eo(h.status,h.statusText,t)}catch(e){}(i=eE(r.dataRoutes,i,s)).errors&&(i.errors=nw(i.errors,e));let u={loaderData:i.loaderData,actionData:i.actionData,errors:nE(i.errors,e)};f={...f,staticHandlerContext:i,serverHandoffString:nN({basename:t.basename,future:t.future,ssr:t.ssr,isSpaMode:l}),serverHandoffStream:nz(u,a.signal,t.entry.module.streamTimeout,e),renderMeta:{}};try{return await m(a,i.statusCode,d,f,n)}catch(t){return o(t),nX(t,e)}}}}async function nV(e,t,r,a,n,o,i){try{let e=await r.queryRoute(n,{routeId:a,requestContext:o,unstable_respond:t.future.unstable_middleware?e=>e:void 0});if(e7(e))return e;if("string"==typeof e)return new Response(e);return Response.json(e)}catch(t){if(e7(t))return t.headers.set("X-Remix-Catch","yes"),t;if(ei(t)){var s,l;return t&&i(t),s=t,l=e,Response.json(nb(s.error||Error("Unexpected Server Error"),l),{status:s.status,statusText:s.statusText,headers:{"X-Remix-Error":"yes"}})}if(t instanceof Error&&"Expected a response from queryRoute"===t.message){let t=Error("Expected a Response to be returned from resource route handler");return i(t),nX(t,e)}return i(t),nX(t,e)}}function nX(e,t){let r="Unexpected Server Error";return"production"!==t&&(r+=`

${String(e)}`),new Response(r,{status:500,headers:{"Content-Type":"text/plain"}})}function nG(e){return`__flash_${e}__`}var nK=(e={},t="")=>{let r=new Map(Object.entries(e));return{get id(){return t},get data(){return Object.fromEntries(r)},has:e=>r.has(e)||r.has(nG(e)),get(e){if(r.has(e))return r.get(e);let t=nG(e);if(r.has(t)){let e=r.get(t);return r.delete(t),e}},set(e,t){r.set(e,t)},flash(e,t){r.set(nG(e),t)},unset(e){r.delete(e)}}},nZ=e=>null!=e&&"string"==typeof e.id&&void 0!==e.data&&"function"==typeof e.has&&"function"==typeof e.get&&"function"==typeof e.set&&"function"==typeof e.flash&&"function"==typeof e.unset;function nQ({cookie:e,createData:t,readData:r,updateData:a,deleteData:n}){let o=nh(e)?e:nd(e?.name||"__session",e);return n0(o),{async getSession(e,t){let a=e&&await o.parse(e,t);return nK(a&&await r(a)||{},a||"")},async commitSession(e,r){let{id:n,data:i}=e,s=r?.maxAge!=null?new Date(Date.now()+1e3*r.maxAge):r?.expires!=null?r.expires:o.expires;return n?await a(n,i,s):n=await t(i,s),o.serialize(n,r)},destroySession:async(e,t)=>(await n(e.id),o.serialize("",{...t,maxAge:void 0,expires:new Date(0)}))}}function n0(e){t3(e.isSigned,`The "${e.name}" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`)}function n1({cookie:e}={}){let t=nh(e)?e:nd(e?.name||"__session",e);return n0(t),{getSession:async(e,r)=>nK(e&&await t.parse(e,r)||{}),async commitSession(e,r){let a=await t.serialize(e.data,r);if(a.length>4096)throw Error("Cookie length will exceed browser maximum. Length: "+a.length);return a},destroySession:async(e,r)=>t.serialize("",{...r,maxAge:void 0,expires:new Date(0)})}}function n2({cookie:e}={}){let t=new Map;return nQ({cookie:e,async createData(e,r){let a=Math.random().toString(36).substring(2,10);return t.set(a,{data:e,expires:r}),a},async readData(e){if(t.has(e)){let{data:r,expires:a}=t.get(e);if(!a||a>new Date)return r;a&&t.delete(e)}return null},async updateData(e,r,a){t.set(e,{data:r,expires:a})},async deleteData(e){t.delete(e)}})}function n4(e,...t){let r=t[0];return e.split("/").map(t=>{let a=t.match(/^:([\w-]+)(\?)?/);if(!a)return t;let n=a[1],o=r?r[n]:void 0;if(void 0===a[2]&&void 0===o)throw Error(`Path '${e}' requires param '${n}' but it was not provided`);return o}).filter(e=>void 0!==e).join("/")}function n3(e){if(!e)return null;let t=Object.entries(e),r={};for(let[e,a]of t)if(a&&"RouteErrorResponse"===a.__type)r[e]=new eo(a.status,a.statusText,a.data,!0===a.internal);else if(a&&"Error"===a.__type){if(a.__subType){let t=window[a.__subType];if("function"==typeof t)try{let n=new t(a.message);n.stack=a.stack,r[e]=n}catch(e){}}if(null==r[e]){let t=Error(a.message);t.stack=a.stack,r[e]=t}}else r[e]=a;return r}function n5(e,t,r,a,n,o){let i={...e,loaderData:{...e.loaderData}},s=$(t,a,n);if(s)for(let e of s){let t=e.route.id,a=r(t);ao(t,a.clientLoader,a.hasLoader,o)&&(a.hasHydrateFallback||!a.hasLoader)?delete i.loaderData[t]:a.hasLoader||(i.loaderData[t]=null)}return i}},72852:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var a=r(60687),n=r(43210),o=r(82989),i=r(35421);let s=()=>{let[e,t]=(0,n.useState)(""),[r,s]=(0,n.useState)(""),[l,u]=(0,n.useState)(""),[c,d]=(0,n.useState)(""),[h,f]=(0,n.useState)(null),[m,p]=(0,n.useState)({}),[y,v]=(0,n.useState)(!1),g=(0,o.useNavigate)(),w=async t=>{if(t.preventDefault(),l!==c)return void f("Passwords do not match");try{v(!0),f(null),p({}),await i.uR.register({name:e,email:r,password:l,password_confirmation:c}),g("/")}catch(e){console.error("Registration error:",e),e.response?.data?.errors?p(e.response.data.errors):f(e.response?.data?.message||"Registration failed. Please try again.")}finally{v(!1)}};return(0,a.jsxs)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create a new account"}),(0,a.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,a.jsx)(o.Link,{to:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"sign in to your existing account"})]})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[h&&(0,a.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:h})})]})}),(0,a.jsxs)("form",{className:"space-y-6",onSubmit:w,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Name"}),(0,a.jsxs)("div",{className:"mt-1",children:[(0,a.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,value:e,onChange:e=>t(e.target.value),className:`appearance-none block w-full px-3 py-2 border ${m.name?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),m.name&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600",children:m.name[0]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,a.jsxs)("div",{className:"mt-1",children:[(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:r,onChange:e=>s(e.target.value),className:`appearance-none block w-full px-3 py-2 border ${m.email?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),m.email&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600",children:m.email[0]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsxs)("div",{className:"mt-1",children:[(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:l,onChange:e=>u(e.target.value),className:`appearance-none block w-full px-3 py-2 border ${m.password?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),m.password&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600",children:m.password[0]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password_confirmation",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"password_confirmation",name:"password_confirmation",type:"password",autoComplete:"new-password",required:!0,value:c,onChange:e=>d(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:y,className:`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${y?"opacity-70 cursor-not-allowed":""}`,children:y?"Creating account...":"Create account"})})]})]})})]})}},73143:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createLineSplittingTransform=t.Deferred=t.TYPE_PREVIOUS_RESOLVED=t.TYPE_URL=t.TYPE_SYMBOL=t.TYPE_SET=t.TYPE_REGEXP=t.TYPE_PROMISE=t.TYPE_NULL_OBJECT=t.TYPE_MAP=t.TYPE_ERROR=t.TYPE_DATE=t.TYPE_BIGINT=t.UNDEFINED=t.POSITIVE_INFINITY=t.NULL=t.NEGATIVE_ZERO=t.NEGATIVE_INFINITY=t.NAN=t.HOLE=void 0,t.HOLE=-1,t.NAN=-2,t.NEGATIVE_INFINITY=-3,t.NEGATIVE_ZERO=-4,t.NULL=-5,t.POSITIVE_INFINITY=-6,t.UNDEFINED=-7,t.TYPE_BIGINT="B",t.TYPE_DATE="D",t.TYPE_ERROR="E",t.TYPE_MAP="M",t.TYPE_NULL_OBJECT="N",t.TYPE_PROMISE="P",t.TYPE_REGEXP="R",t.TYPE_SET="S",t.TYPE_SYMBOL="Y",t.TYPE_URL="U",t.TYPE_PREVIOUS_RESOLVED="Z";class r{promise;resolve;reject;constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}t.Deferred=r,t.createLineSplittingTransform=function(){let e=new TextDecoder,t="";return new TransformStream({transform(r,a){let n=e.decode(r,{stream:!0}),o=(t+n).split("\n");for(let e of(t=o.pop()||"",o))a.enqueue(e)},flush(e){t&&e.enqueue(t)}})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82989:(e,t,r)=>{"use strict";var a=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of o(t))i.call(e,l)||l===r||a(e,l,{get:()=>t[l],enumerable:!(s=n(t,l))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)a(e,r,{get:t[r],enumerable:!0})})(l,{HydratedRouter:()=>u.HydratedRouter,RouterProvider:()=>u.RouterProvider}),e.exports=s(a({},"__esModule",{value:!0}),l);var u=r(8327);((e,t,r)=>(s(e,t,"default"),r&&s(r,t,"default")))(l,r(72124),e.exports)},83997:e=>{"use strict";e.exports=require("tty")},92560:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\page-components\\\\HomePage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\HomePage.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},95196:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var a=r(65239),n=r(48088),o=r(88170),i=r.n(o),s=r(30893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,39709)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,318,658,299,905,102],()=>r(95196));module.exports=a})();