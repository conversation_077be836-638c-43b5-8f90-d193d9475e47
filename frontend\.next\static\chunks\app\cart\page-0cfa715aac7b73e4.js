(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5],{722:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let a=r(3464).A.create({baseURL:"",withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json",Accept:"application/json"}});a.interceptors.request.use(e=>{var t;console.log("Making request to: ".concat(null==(t=e.method)?void 0:t.toUpperCase()," ").concat(e.url));let r=localStorage.getItem("token");return r&&(e.headers.Authorization="Bearer ".concat(r)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&419===e.response.status&&console.error("CSRF token mismatch. Refreshing token..."),Promise.reject(e)));let s=a},2050:(e,t,r)=>{Promise.resolve().then(r.bind(r,7101))},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},7101:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(5155),s=r(2115),o=r(6874),n=r.n(o),i=r(5695),c=r(8258);let d=s.forwardRef(function(e,t){let{title:r,titleId:a,...o}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))});function l(){let[e,t]=(0,s.useState)({items:[],total:0}),[r,o]=(0,s.useState)(!0),l=(0,i.useRouter)();(0,s.useEffect)(()=>{t(c.mx.getCart()),o(!1)},[]);let u=(e,r)=>{t(c.mx.updateQuantity(e,r)),window.dispatchEvent(new Event("storage"))},m=e=>{t(c.mx.removeFromCart(e)),window.dispatchEvent(new Event("storage"))};return r?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):0===e.items.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Your Cart"}),(0,a.jsx)("p",{className:"text-gray-500 mb-8",children:"Your cart is empty."}),(0,a.jsx)(n(),{href:"/products",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Continue Shopping"})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Your Cart"}),(0,a.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-6",children:(0,a.jsx)("div",{className:"border-t border-gray-200",children:(0,a.jsx)("dl",{children:e.items.map(e=>(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-6 sm:gap-4 sm:px-6 border-b border-gray-200",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 sm:col-span-2",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.product.image?(0,a.jsx)("img",{src:e.product.image,alt:e.product.name,className:"h-16 w-16 object-cover rounded mr-4"}):(0,a.jsx)("div",{className:"h-16 w-16 bg-gray-100 rounded mr-4 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-400",children:"No image"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.product.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["$",e.product.price.toFixed(2)," each"]})]})]})}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{onClick:()=>u(e.product_id,e.quantity-1),className:"p-1 rounded-md border border-gray-300 text-gray-700",disabled:e.quantity<=1,children:"-"}),(0,a.jsx)("span",{className:"mx-2",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>u(e.product_id,e.quantity+1),className:"p-1 rounded-md border border-gray-300 text-gray-700",disabled:e.quantity>=e.product.quantity,children:"+"})]})}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("p",{className:"text-lg font-medium text-gray-900",children:["$",(e.product.price*e.quantity).toFixed(2)]})})}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1",children:(0,a.jsx)("div",{className:"text-right",children:(0,a.jsx)("button",{onClick:()=>m(e.product_id),className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(d,{className:"h-5 w-5"})})})})]},e.product_id))})})}),(0,a.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-6",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Order Summary"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["$",e.total.toFixed(2)]})]})})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(n(),{href:"/products",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Continue Shopping"}),(0,a.jsx)("button",{onClick:()=>{if(!c.uR.isAuthenticated())return void l.push("/login");l.push("/checkout")},className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Proceed to Checkout"})]})]})}},8258:(e,t,r)=>{"use strict";r.d(t,{uR:()=>o,mx:()=>u,M$:()=>i,QE:()=>c,bk:()=>n,d$:()=>m});var a=r(722);let s={login:async e=>{try{await a.A.get("/sanctum/csrf-cookie");let t=await a.A.post("/api/login",e);if(t.data.success&&t.data.data){let{user:e,token:r}=t.data.data;return localStorage.setItem("token",r),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Login error details:",e),e}},register:async e=>{try{await a.A.get("/sanctum/csrf-cookie");let t=await a.A.post("/api/register",e);if(t.data.success&&t.data.data){let{user:e,token:r}=t.data.data;return localStorage.setItem("token",r),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Registration error details:",e),e}},logout:async()=>{try{await a.A.get("/sanctum/csrf-cookie"),await a.A.post("/api/logout"),localStorage.removeItem("token"),localStorage.removeItem("user")}catch(e){console.error("Logout error:",e),localStorage.removeItem("token"),localStorage.removeItem("user")}},getCurrentUser:()=>{let e=localStorage.getItem("user");return e?JSON.parse(e):null},isAuthenticated:()=>!!localStorage.getItem("token"),isEmailVerified:()=>{let e=localStorage.getItem("user");return!!e&&!!JSON.parse(e).email_verified_at},resendVerificationEmail:async()=>{try{if(!s.isAuthenticated())throw Error("You must be logged in to resend verification email");console.log("Attempting to resend verification email...");let e=await a.A.get("/sanctum/csrf-cookie");console.log("CSRF cookie response:",e.status),console.log("Sending verification email request...");let t=await a.A.post("/api/email/verification-notification");return console.log("Verification email response:",t.status,t.data),t.data}catch(e){throw console.error("Resend verification email error:",e),e}},refreshUserData:async()=>{try{if(!s.isAuthenticated())return null;let e=await a.A.get("/api/user");if(console.log("Refresh user data response:",e.data),e.data&&e.data.success&&e.data.data&&e.data.data.user){let t=e.data.data.user;return localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),t}return null}catch(e){return console.error("Refresh user data error:",e),null}}},o=s,n={fetchProducts:async()=>{try{return(await a.A.get("/products")).data}catch(e){throw e}},getFeaturedProducts:async()=>{try{return(await a.A.get("/products?featured=true")).data}catch(e){throw e}},getProductBySlug:async e=>{try{let t=(await a.A.get("/products")).data.data.find(t=>t.slug===e);if(!t)throw Error("Product not found");return t}catch(e){throw e}},getAllProducts:async()=>{try{return(await a.A.get("/products")).data.data}catch(e){throw e}}},i={getAllCategories:async()=>{try{return(await a.A.get("/categories")).data.data}catch(e){throw e}},getCategoryBySlug:async e=>{try{let t=(await a.A.get("/categories")).data.data.find(t=>t.slug===e);if(!t)throw Error("Category not found");return t}catch(e){throw e}}},c={getAllOrders:async()=>{try{return(await a.A.get("/orders")).data.data}catch(e){throw e}},getOrderById:async e=>{try{return(await a.A.get("/orders/".concat(e))).data.data}catch(e){throw e}},createOrder:async e=>{try{return(await a.A.post("/orders",e)).data.data}catch(e){throw e}},updateOrder:async(e,t)=>{try{return(await a.A.put("/orders/".concat(e),{notes:t})).data.data}catch(e){throw e}},cancelOrder:async e=>{try{return(await a.A.post("/orders/".concat(e,"/cancel"))).data.data}catch(e){throw e}}},d="ecommerce_cart",l={getCart:()=>{let e=localStorage.getItem(d);return e?JSON.parse(e):{items:[],total:0}},saveCart:e=>{localStorage.setItem(d,JSON.stringify(e))},addToCart:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=l.getCart(),a=r.items.findIndex(t=>t.product_id===e.id);return -1!==a?r.items[a].quantity+=t:r.items.push({product_id:e.id,quantity:t,product:e}),r.total=r.items.reduce((e,t)=>e+t.product.price*t.quantity,0),l.saveCart(r),r},updateQuantity:(e,t)=>{let r=l.getCart(),a=r.items.findIndex(t=>t.product_id===e);return -1!==a&&(t<=0?r.items.splice(a,1):r.items[a].quantity=t,r.total=r.items.reduce((e,t)=>e+t.product.price*t.quantity,0),l.saveCart(r)),r},removeFromCart:e=>{let t=l.getCart(),r=t.items.findIndex(t=>t.product_id===e);return -1!==r&&(t.items.splice(r,1),t.total=t.items.reduce((e,t)=>e+t.product.price*t.quantity,0),l.saveCart(t)),t},clearCart:()=>{let e={items:[],total:0};return l.saveCart(e),e},getItemCount:()=>l.getCart().items.reduce((e,t)=>e+t.quantity,0)},u=l,m={createPaymentIntent:async e=>{try{return(await a.A.post("/payment/create-intent",e)).data}catch(e){throw e}},processPayment:async(e,t)=>{try{return(await a.A.post("/payment/process",{payment_method_id:e,items:t})).data}catch(e){throw e}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,441,684,358],()=>t(2050)),_N_E=e.O()}]);