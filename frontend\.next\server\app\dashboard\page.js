(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7426:(e,t,s)=>{Promise.resolve().then(s.bind(s,80559))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41930:(e,t,s)=>{Promise.resolve().then(s.bind(s,58061))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56568:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>l});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let l={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,80559)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\dashboard\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},58061:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),a=s(43210),i=s(16189),n=s(85814),d=s.n(n),o=s(35421),l=s(14329),c=s(31309);let m=()=>{let[e,t]=(0,a.useState)(null),[s,n]=(0,a.useState)([]),[m,x]=(0,a.useState)(!0),[p,u]=(0,a.useState)(!0),h=(0,i.useRouter)(),g=()=>{if(!o.uR.isAuthenticated())return void h.push("/login");t(o.uR.getCurrentUser()),x(!1)},f=async()=>{try{x(!0),await (0,c.nO)(),g()}catch(e){console.error("Failed to refresh user data:",e),g()}},y=async()=>{try{if(u(!0),o.uR.isAuthenticated()){let e=await o.QE.getAllOrders();n(e)}}catch(e){console.error("Failed to fetch orders:",e)}finally{u(!1)}};return((0,a.useEffect)(()=>(f(),y(),(0,c.eq)(()=>{g(),y()})),[h]),m)?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,r.jsx)("button",{onClick:f,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",disabled:m,children:m?"Refreshing...":"Refresh Data"})]}),e&&!o.uR.isEmailVerified()&&(0,r.jsx)(l.A,{email:e.email,showDismiss:!0}),(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-8",children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"User Information"}),(0,r.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Personal details and account information."})]}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:(0,r.jsxs)("dl",{children:[(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Full name"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:e?.name})]}),(0,r.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Email address"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:e?.email})]}),(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Email verification"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:o.uR.isEmailVerified()?(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Verified"}):(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"Not verified"})})]}),(0,r.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Account created"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:new Date(e?.created_at).toLocaleDateString()})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Your Orders"}),(0,r.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"View and manage your orders."})]}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:p?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"})}):s.length>0?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order ID"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",e.id]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"completed"===e.status?"bg-green-100 text-green-800":"processing"===e.status?"bg-blue-100 text-blue-800":"canceled"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["$",Number(e.total_amount).toFixed(2)]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,r.jsx)(d(),{href:`/order-confirmation/${e.id}`,className:"text-indigo-600 hover:text-indigo-900 mr-4",children:"View"}),"pending"===e.status&&(0,r.jsx)("button",{onClick:async()=>{if(window.confirm("Are you sure you want to cancel this order?"))try{await o.QE.cancelOrder(e.id),y()}catch(e){console.error("Failed to cancel order:",e),alert("Failed to cancel order. Please try again.")}},className:"text-red-600 hover:text-red-900",children:"Cancel"})]})]},e.id))})]})}):(0,r.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"You haven't placed any orders yet."}),(0,r.jsx)(d(),{href:"/products",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Start Shopping"})]})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\dashboard\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,318,658,905],()=>s(56568));module.exports=r})();