(()=>{var e={};e.id=454,e.ids=[454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8548:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(60687),a=s(43210),o=s(85814),n=s.n(o),i=s(16189),d=s(35421);let l=()=>{let[e,r]=(0,a.useState)(""),[s,o]=(0,a.useState)(""),[l,c]=(0,a.useState)(""),[u,m]=(0,a.useState)(""),[p,x]=(0,a.useState)(null),[h,f]=(0,a.useState)({}),[g,w]=(0,a.useState)(!1),v=(0,i.useRouter)(),b=async r=>{if(r.preventDefault(),l!==u)return void x("Passwords do not match");try{w(!0),x(null),f({}),await d.uR.register({name:e,email:s,password:l,password_confirmation:u}),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),v.push("/")}catch(e){console.error("Registration error:",e),e.response?.data?.errors?f(e.response.data.errors):x(e.response?.data?.message||"Registration failed. Please try again.")}finally{w(!1)}};return(0,t.jsxs)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create a new account"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,t.jsx)(n(),{href:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"sign in to your existing account"})]})]}),(0,t.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,t.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[p&&(0,t.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm text-red-700",children:p})})]})}),(0,t.jsxs)("form",{className:"space-y-6",onSubmit:b,children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Name"}),(0,t.jsxs)("div",{className:"mt-1",children:[(0,t.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,value:e,onChange:e=>r(e.target.value),className:`appearance-none block w-full px-3 py-2 border ${h.name?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),h.name&&(0,t.jsx)("p",{className:"mt-2 text-sm text-red-600",children:h.name[0]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,t.jsxs)("div",{className:"mt-1",children:[(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:s,onChange:e=>o(e.target.value),className:`appearance-none block w-full px-3 py-2 border ${h.email?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),h.email&&(0,t.jsx)("p",{className:"mt-2 text-sm text-red-600",children:h.email[0]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,t.jsxs)("div",{className:"mt-1",children:[(0,t.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:l,onChange:e=>c(e.target.value),className:`appearance-none block w-full px-3 py-2 border ${h.password?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),h.password&&(0,t.jsx)("p",{className:"mt-2 text-sm text-red-600",children:h.password[0]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password_confirmation",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)("input",{id:"password_confirmation",name:"password_confirmation",type:"password",autoComplete:"new-password",required:!0,value:u,onChange:e=>m(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",disabled:g,className:`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${g?"opacity-70 cursor-not-allowed":""}`,children:g?"Creating account...":"Create account"})})]})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,r,s)=>{"use strict";var t=s(65773);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52495:(e,r,s)=>{Promise.resolve().then(s.bind(s,8548))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64119:(e,r,s)=>{Promise.resolve().then(s.bind(s,94530))},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94530:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\register\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96778:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var t=s(65239),a=s(48088),o=s(88170),n=s.n(o),i=s(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(r,d);let l={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94530)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\register\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,318,658,905],()=>s(96778));module.exports=t})();