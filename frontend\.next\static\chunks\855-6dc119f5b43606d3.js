(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[855],{5855:function(e,t,n){(function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){u(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,o=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=o){var u=[],c=!0,i=!1;try{for(o=o.call(e);!(c=(n=o.next()).done)&&(u.push(n.value),!t||u.length!==t);c=!0);}catch(e){i=!0,r=e}finally{try{c||null==o.return||o.return()}finally{if(i)throw r}}return u}}(e,t)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var s,a,l,p,f,d={exports:{}};d.exports=(function(){if(f)return p;f=1;var e=l?a:(l=1,a="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");function t(){}function n(){}return n.resetWarningCache=t,p=function(){function r(t,n,r,o,u,c){if(c!==e){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function o(){return r}r.isRequired=r;var u={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:o,element:r,elementType:r,instanceOf:o,node:r,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:t};return u.PropTypes=u,u}})()();var m=(s=d.exports)&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s,h=function(e,n,r){var o=!!r,u=t.useRef(r);t.useEffect(function(){u.current=r},[r]),t.useEffect(function(){if(!o||!e)return function(){};var t=function(){u.current&&u.current.apply(u,arguments)};return e.on(n,t),function(){e.off(n,t)}},[o,n,e,u])},y=function(e){var n=t.useRef(e);return t.useEffect(function(){n.current=e},[e]),n.current},C=function(e){return null!==e&&"object"===o(e)},g="[object Object]",v=function e(t,n){if(!C(t)||!C(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var o=Object.prototype.toString.call(t)===g;if(o!==(Object.prototype.toString.call(n)===g))return!1;if(!o&&!r)return t===n;var u=Object.keys(t),c=Object.keys(n);if(u.length!==c.length)return!1;for(var i={},s=0;s<u.length;s+=1)i[u[s]]=!0;for(var a=0;a<c.length;a+=1)i[c[a]]=!0;var l=Object.keys(i);return l.length===u.length&&l.every(function(r){return e(t[r],n[r])})},E=function(e,t,n){return C(e)?Object.keys(e).reduce(function(o,c){var i=!C(t)||!v(e[c],t[c]);return n.includes(c)?(i&&console.warn("Unsupported prop change: options.".concat(c," is not a mutable property.")),o):i?r(r({},o||{}),{},u({},c,e[c])):o},null):null},k="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(null===e||C(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment)return e;throw Error(t)},S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(C(e)&&"function"==typeof e.then)return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return b(e,t)})};var n=b(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},P=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"2.8.1"}),e.registerAppInfo({name:"react-stripe-js",version:"2.8.1",url:"https://stripe.com/docs/stripe-js/react"}))},O=t.createContext(null);O.displayName="ElementsContext";var w=function(e,t){if(!e)throw Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},j=function(e){var n=e.stripe,r=e.options,o=e.children,u=t.useMemo(function(){return S(n)},[n]),i=c(t.useState(function(){return{stripe:"sync"===u.tag?u.stripe:null,elements:"sync"===u.tag?u.stripe.elements(r):null}}),2),s=i[0],a=i[1];t.useEffect(function(){var e=!0,t=function(e){a(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==u.tag||s.stripe?"sync"!==u.tag||s.stripe||t(u.stripe):u.stripePromise.then(function(n){n&&e&&t(n)}),function(){e=!1}},[u,s,r]);var l=y(n);t.useEffect(function(){null!==l&&l!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[l,n]);var p=y(r);return t.useEffect(function(){if(s.elements){var e=E(r,p,["clientSecret","fonts"]);e&&s.elements.update(e)}},[r,p,s.elements]),t.useEffect(function(){P(s.stripe)},[s.stripe]),t.createElement(O.Provider,{value:s},o)};j.propTypes={stripe:m.any,options:m.object};var x=function(e){return w(t.useContext(O),e)},A=function(e){return(0,e.children)(x("mounts <ElementsConsumer>"))};A.propTypes={children:m.func.isRequired};var R=["on","session"],N=t.createContext(null);N.displayName="CustomCheckoutSdkContext";var I=function(e,t){if(!e)throw Error("Could not find CustomCheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CustomCheckoutProvider> provider."));return e},_=t.createContext(null);_.displayName="CustomCheckoutContext";var T=function(e,t){if(!e)return null;e.on,e.session;var n=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},u=Object.keys(e);for(r=0;r<u.length;r++)n=u[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(e);for(r=0;r<u.length;r++)n=u[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,R);return t?r(r({},n),t):r(r({},n),e.session())},U=function(e){var n=e.stripe,r=e.options,o=e.children,u=t.useMemo(function(){return S(n,"Invalid prop `stripe` supplied to `CustomCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),i=c(t.useState(null),2),s=i[0],a=i[1],l=c(t.useState(function(){return{stripe:"sync"===u.tag?u.stripe:null,customCheckoutSdk:null}}),2),p=l[0],f=l[1],d=function(e,t){f(function(n){return n.stripe&&n.customCheckoutSdk?n:{stripe:e,customCheckoutSdk:t}})},m=t.useRef(!1);t.useEffect(function(){var e=!0;return"async"!==u.tag||p.stripe?"sync"===u.tag&&u.stripe&&!m.current&&(m.current=!0,u.stripe.initCustomCheckout(r).then(function(e){e&&(d(u.stripe,e),e.on("change",a))})):u.stripePromise.then(function(t){t&&e&&!m.current&&(m.current=!0,t.initCustomCheckout(r).then(function(e){e&&(d(t,e),e.on("change",a))}))}),function(){e=!1}},[u,p,r,a]);var h=y(n);t.useEffect(function(){null!==h&&h!==n&&console.warn("Unsupported prop change on CustomCheckoutProvider: You cannot change the `stripe` prop after setting it.")},[h,n]);var g=y(r);t.useEffect(function(){if(p.customCheckoutSdk){!r.clientSecret||C(g)||v(r.clientSecret,g.clientSecret)||console.warn("Unsupported prop change: options.client_secret is not a mutable property.");var e,t,n=null==g||null==(e=g.elementsOptions)?void 0:e.appearance,o=null==r||null==(t=r.elementsOptions)?void 0:t.appearance;o&&!v(o,n)&&p.customCheckoutSdk.changeAppearance(o)}},[r,g,p.customCheckoutSdk]),t.useEffect(function(){P(p.stripe)},[p.stripe]);var E=t.useMemo(function(){return T(p.customCheckoutSdk,s)},[p.customCheckoutSdk,s]);return p.customCheckoutSdk?t.createElement(N.Provider,{value:p},t.createElement(_.Provider,{value:E},o)):null};U.propTypes={stripe:m.any,options:m.shape({clientSecret:m.string.isRequired,elementsOptions:m.object}).isRequired};var B=function(e){var n=t.useContext(N),r=t.useContext(O);if(n&&r)throw Error("You cannot wrap the part of your app that ".concat(e," in both <CustomCheckoutProvider> and <Elements> providers."));return n?I(n,e):w(r,e)},M=function(e,n){var r="".concat(e.charAt(0).toUpperCase()+e.slice(1),"Element"),o=n?function(e){B("mounts <".concat(r,">"));var n=e.id,o=e.className;return t.createElement("div",{id:n,className:o})}:function(n){var o,u=n.id,i=n.className,s=n.options,a=void 0===s?{}:s,l=n.onBlur,p=n.onFocus,f=n.onReady,d=n.onChange,m=n.onEscape,C=n.onClick,g=n.onLoadError,v=n.onLoaderStart,k=n.onNetworksChange,b=n.onConfirm,S=n.onCancel,P=n.onShippingAddressChange,O=n.onShippingRateChange,w=B("mounts <".concat(r,">")),j="elements"in w?w.elements:null,x="customCheckoutSdk"in w?w.customCheckoutSdk:null,A=c(t.useState(null),2),R=A[0],N=A[1],I=t.useRef(null),_=t.useRef(null);h(R,"blur",l),h(R,"focus",p),h(R,"escape",m),h(R,"click",C),h(R,"loaderror",g),h(R,"loaderstart",v),h(R,"networkschange",k),h(R,"confirm",b),h(R,"cancel",S),h(R,"shippingaddresschange",P),h(R,"shippingratechange",O),h(R,"change",d),f&&(o="expressCheckout"===e?f:function(){f(R)}),h(R,"ready",o),t.useLayoutEffect(function(){if(null===I.current&&null!==_.current&&(j||x)){var t=null;x?t=x.createElement(e,a):j&&(t=j.create(e,a)),I.current=t,N(t),t&&t.mount(_.current)}},[j,x,a]);var T=y(a);return t.useEffect(function(){if(I.current){var e=E(a,T,["paymentRequest"]);e&&"update"in I.current&&I.current.update(e)}},[a,T]),t.useLayoutEffect(function(){return function(){if(I.current&&"function"==typeof I.current.destroy)try{I.current.destroy(),I.current=null}catch(e){}}},[]),t.createElement("div",{id:u,className:i,ref:_})};return o.propTypes={id:m.string,className:m.string,onChange:m.func,onBlur:m.func,onFocus:m.func,onReady:m.func,onEscape:m.func,onClick:m.func,onLoadError:m.func,onLoaderStart:m.func,onNetworksChange:m.func,onConfirm:m.func,onCancel:m.func,onShippingAddressChange:m.func,onShippingRateChange:m.func,options:m.object},o.displayName=r,o.__elementType=e,o},Y="undefined"==typeof window,L=t.createContext(null);L.displayName="EmbeddedCheckoutProviderContext";var D=function(){var e=t.useContext(L);if(!e)throw Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},W=Y?function(e){var n=e.id,r=e.className;return D(),t.createElement("div",{id:n,className:r})}:function(e){var n=e.id,r=e.className,o=D().embeddedCheckout,u=t.useRef(!1),c=t.useRef(null);return t.useLayoutEffect(function(){return!u.current&&o&&null!==c.current&&(o.mount(c.current),u.current=!0),function(){if(u.current&&o)try{o.unmount(),u.current=!1}catch(e){}}},[o]),t.createElement("div",{ref:c,id:n,className:r})},q=M("auBankAccount",Y),F=M("card",Y),H=M("cardNumber",Y),V=M("cardExpiry",Y),$=M("cardCvc",Y),z=M("fpxBank",Y),G=M("iban",Y),J=M("idealBank",Y),K=M("p24Bank",Y),Q=M("epsBank",Y),X=M("payment",Y),Z=M("expressCheckout",Y),ee=M("currencySelector",Y),et=M("paymentRequestButton",Y),en=M("linkAuthentication",Y),er=M("address",Y),eo=M("shippingAddress",Y),eu=M("paymentMethodMessaging",Y),ec=M("affirmMessage",Y),ei=M("afterpayClearpayMessage",Y);e.AddressElement=er,e.AffirmMessageElement=ec,e.AfterpayClearpayMessageElement=ei,e.AuBankAccountElement=q,e.CardCvcElement=$,e.CardElement=F,e.CardExpiryElement=V,e.CardNumberElement=H,e.CurrencySelectorElement=ee,e.CustomCheckoutProvider=U,e.Elements=j,e.ElementsConsumer=A,e.EmbeddedCheckout=W,e.EmbeddedCheckoutProvider=function(e){var n=e.stripe,r=e.options,o=e.children,u=t.useMemo(function(){return S(n,"Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),i=t.useRef(null),s=t.useRef(null),a=c(t.useState({embeddedCheckout:null}),2),l=a[0],p=a[1];t.useEffect(function(){if(!s.current&&!i.current){var e=function(e){s.current||i.current||(s.current=e,i.current=s.current.initEmbeddedCheckout(r).then(function(e){p({embeddedCheckout:e})}))};"async"===u.tag&&!s.current&&(r.clientSecret||r.fetchClientSecret)?u.stripePromise.then(function(t){t&&e(t)}):"sync"===u.tag&&!s.current&&(r.clientSecret||r.fetchClientSecret)&&e(u.stripe)}},[u,r,l,s]),t.useEffect(function(){return function(){l.embeddedCheckout?(i.current=null,l.embeddedCheckout.destroy()):i.current&&i.current.then(function(){i.current=null,l.embeddedCheckout&&l.embeddedCheckout.destroy()})}},[l.embeddedCheckout]),t.useEffect(function(){P(s)},[s]);var f=y(n);t.useEffect(function(){null!==f&&f!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")},[f,n]);var d=y(r);return t.useEffect(function(){if(null!=d){if(null==r)return void console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.");void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=d.clientSecret&&r.clientSecret!==d.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=d.fetchClientSecret&&r.fetchClientSecret!==d.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=d.onComplete&&r.onComplete!==d.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=d.onShippingDetailsChange&&r.onShippingDetailsChange!==d.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=d.onLineItemsChange&&r.onLineItemsChange!==d.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")}},[d,r]),t.createElement(L.Provider,{value:l},o)},e.EpsBankElement=Q,e.ExpressCheckoutElement=Z,e.FpxBankElement=z,e.IbanElement=G,e.IdealBankElement=J,e.LinkAuthenticationElement=en,e.P24BankElement=K,e.PaymentElement=X,e.PaymentMethodMessagingElement=eu,e.PaymentRequestButtonElement=et,e.ShippingAddressElement=eo,e.useCustomCheckout=function(){I(t.useContext(N),"calls useCustomCheckout()");var e=t.useContext(_);if(!e)throw Error("Could not find CustomCheckout Context; You need to wrap the part of your app that calls useCustomCheckout() in an <CustomCheckoutProvider> provider.");return e},e.useElements=function(){return x("calls useElements()").elements},e.useStripe=function(){return B("calls useStripe()").stripe}})(t,n(2115))}}]);