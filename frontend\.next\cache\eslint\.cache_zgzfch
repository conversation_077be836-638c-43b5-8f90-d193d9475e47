[{"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\api\\test\\route.ts": "1", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\cart\\page.tsx": "2", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\categories\\page.tsx": "3", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\categories\\[slug]\\page.tsx": "4", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\checkout\\page.tsx": "5", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\client-layout.tsx": "6", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\dashboard\\page.tsx": "7", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\email-verified\\page.tsx": "8", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\layout.tsx": "9", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\login\\page.tsx": "10", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\order-confirmation\\[id]\\page.tsx": "11", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\page.tsx": "12", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\products\\page.tsx": "13", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\products\\[slug]\\page.tsx": "14", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\profile\\page.tsx": "15", "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\register\\page.tsx": "16", "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\CategoryCard.tsx": "17", "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\CsrfToken.tsx": "18", "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\index.ts": "19", "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\Layout.tsx": "20", "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\ProductCard.tsx": "21", "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\StripePaymentForm.tsx": "22", "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\StripeWrapper.tsx": "23", "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\VerificationNotice.tsx": "24", "C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\HomePage.tsx": "25", "C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\index.ts": "26", "C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\LoginPage.tsx": "27", "C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\RegisterPage.tsx": "28", "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\api.ts": "29", "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\auth.service.ts": "30", "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\cart.service.ts": "31", "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\category.service.ts": "32", "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\index.ts": "33", "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\mockData.ts": "34", "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\order.service.ts": "35", "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\product.service.ts": "36", "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\stripe.service.ts": "37", "C:\\laragon\\www\\ecommerce\\frontend\\src\\utils\\auth-utils.ts": "38"}, {"size": 137, "mtime": 1745861865711, "results": "39", "hashOfConfig": "40"}, {"size": 6286, "mtime": 1745859545070, "results": "41", "hashOfConfig": "40"}, {"size": 2296, "mtime": 1745927153955, "results": "42", "hashOfConfig": "40"}, {"size": 3514, "mtime": 1746030177821, "results": "43", "hashOfConfig": "40"}, {"size": 23158, "mtime": 1748692162883, "results": "44", "hashOfConfig": "40"}, {"size": 11482, "mtime": 1746018369809, "results": "45", "hashOfConfig": "40"}, {"size": 10395, "mtime": 1746030291307, "results": "46", "hashOfConfig": "40"}, {"size": 7245, "mtime": 1746017852498, "results": "47", "hashOfConfig": "40"}, {"size": 765, "mtime": 1745859177781, "results": "48", "hashOfConfig": "40"}, {"size": 5223, "mtime": 1746006666589, "results": "49", "hashOfConfig": "40"}, {"size": 11258, "mtime": 1746030063612, "results": "50", "hashOfConfig": "40"}, {"size": 106, "mtime": 1745920429806, "results": "51", "hashOfConfig": "40"}, {"size": 2259, "mtime": 1745927151711, "results": "52", "hashOfConfig": "40"}, {"size": 6388, "mtime": 1746030147806, "results": "53", "hashOfConfig": "40"}, {"size": 4519, "mtime": 1746017762737, "results": "54", "hashOfConfig": "40"}, {"size": 7584, "mtime": 1746006651133, "results": "55", "hashOfConfig": "40"}, {"size": 1209, "mtime": 1745862835234, "results": "56", "hashOfConfig": "40"}, {"size": 592, "mtime": 1745952936134, "results": "57", "hashOfConfig": "40"}, {"size": 350, "mtime": 1748690273690, "results": "58", "hashOfConfig": "40"}, {"size": 9144, "mtime": 1745920500922, "results": "59", "hashOfConfig": "40"}, {"size": 2285, "mtime": 1745927153504, "results": "60", "hashOfConfig": "40"}, {"size": 4000, "mtime": 1748692186664, "results": "61", "hashOfConfig": "40"}, {"size": 891, "mtime": 1748690288946, "results": "62", "hashOfConfig": "40"}, {"size": 3764, "mtime": 1746018303978, "results": "63", "hashOfConfig": "40"}, {"size": 4729, "mtime": 1745927152972, "results": "64", "hashOfConfig": "40"}, {"size": 160, "mtime": 1745854435859, "results": "65", "hashOfConfig": "40"}, {"size": 4515, "mtime": 1745920508170, "results": "66", "hashOfConfig": "40"}, {"size": 7299, "mtime": 1745920514269, "results": "67", "hashOfConfig": "40"}, {"size": 1425, "mtime": 1745984859563, "results": "68", "hashOfConfig": "40"}, {"size": 5659, "mtime": 1748692221935, "results": "69", "hashOfConfig": "40"}, {"size": 2638, "mtime": 1745854123253, "results": "70", "hashOfConfig": "40"}, {"size": 1011, "mtime": 1746030120113, "results": "71", "hashOfConfig": "40"}, {"size": 661, "mtime": 1748690221353, "results": "72", "hashOfConfig": "40"}, {"size": 0, "mtime": 1745917719060, "results": "73", "hashOfConfig": "40"}, {"size": 2570, "mtime": 1745854106286, "results": "74", "hashOfConfig": "40"}, {"size": 1859, "mtime": 1748692210573, "results": "75", "hashOfConfig": "40"}, {"size": 1421, "mtime": 1748692199620, "results": "76", "hashOfConfig": "40"}, {"size": 1673, "mtime": 1746017711546, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f62c1m", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\api\\test\\route.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\cart\\page.tsx", ["192"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\categories\\page.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\categories\\[slug]\\page.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\checkout\\page.tsx", ["193", "194", "195", "196"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\client-layout.tsx", ["197", "198"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\dashboard\\page.tsx", ["199", "200", "201"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\email-verified\\page.tsx", ["202", "203"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\layout.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\login\\page.tsx", ["204"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\order-confirmation\\[id]\\page.tsx", ["205", "206"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\page.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\products\\page.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\products\\[slug]\\page.tsx", ["207"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\profile\\page.tsx", ["208", "209", "210"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\register\\page.tsx", ["211"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\CategoryCard.tsx", ["212"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\CsrfToken.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\index.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\Layout.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\ProductCard.tsx", ["213"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\StripePaymentForm.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\StripeWrapper.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\components\\VerificationNotice.tsx", ["214"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\HomePage.tsx", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\index.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\LoginPage.tsx", ["215"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\page-components\\RegisterPage.tsx", ["216"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\api.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\auth.service.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\cart.service.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\category.service.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\index.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\mockData.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\order.service.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\product.service.ts", [], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\services\\stripe.service.ts", ["217"], [], "C:\\laragon\\www\\ecommerce\\frontend\\src\\utils\\auth-utils.ts", [], [], {"ruleId": "218", "severity": 1, "message": "219", "line": 79, "column": 23, "nodeType": "220", "endLine": 83, "endColumn": 25}, {"ruleId": "221", "severity": 2, "message": "222", "line": 27, "column": 10, "nodeType": null, "messageId": "223", "endLine": 27, "endColumn": 25}, {"ruleId": "224", "severity": 2, "message": "225", "line": 119, "column": 21, "nodeType": "226", "messageId": "227", "endLine": 119, "endColumn": 24, "suggestions": "228"}, {"ruleId": "224", "severity": 2, "message": "225", "line": 167, "column": 21, "nodeType": "226", "messageId": "227", "endLine": 167, "endColumn": 24, "suggestions": "229"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 540, "column": 29, "nodeType": "220", "endLine": 544, "endColumn": 31}, {"ruleId": "224", "severity": 2, "message": "225", "line": 20, "column": 50, "nodeType": "226", "messageId": "227", "endLine": 20, "endColumn": 53, "suggestions": "230"}, {"ruleId": "231", "severity": 1, "message": "232", "line": 88, "column": 6, "nodeType": "233", "endLine": 88, "endColumn": 8, "suggestions": "234"}, {"ruleId": "224", "severity": 2, "message": "225", "line": 11, "column": 36, "nodeType": "226", "messageId": "227", "endLine": 11, "endColumn": 39, "suggestions": "235"}, {"ruleId": "231", "severity": 1, "message": "236", "line": 75, "column": 6, "nodeType": "233", "endLine": 75, "endColumn": 14, "suggestions": "237"}, {"ruleId": "238", "severity": 2, "message": "239", "line": 229, "column": 58, "nodeType": "240", "messageId": "241", "suggestions": "242"}, {"ruleId": "221", "severity": 2, "message": "243", "line": 7, "column": 10, "nodeType": null, "messageId": "223", "endLine": 7, "endColumn": 25}, {"ruleId": "221", "severity": 2, "message": "244", "line": 11, "column": 9, "nodeType": null, "messageId": "223", "endLine": 11, "endColumn": 15}, {"ruleId": "224", "severity": 2, "message": "225", "line": 32, "column": 19, "nodeType": "226", "messageId": "227", "endLine": 32, "endColumn": 22, "suggestions": "245"}, {"ruleId": "224", "severity": 2, "message": "225", "line": 31, "column": 21, "nodeType": "226", "messageId": "227", "endLine": 31, "endColumn": 24, "suggestions": "246"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 205, "column": 27, "nodeType": "220", "endLine": 209, "endColumn": 29}, {"ruleId": "218", "severity": 1, "message": "219", "line": 86, "column": 17, "nodeType": "220", "endLine": 90, "endColumn": 19}, {"ruleId": "221", "severity": 2, "message": "247", "line": 6, "column": 8, "nodeType": null, "messageId": "223", "endLine": 6, "endColumn": 26}, {"ruleId": "224", "severity": 2, "message": "225", "line": 10, "column": 36, "nodeType": "226", "messageId": "227", "endLine": 10, "endColumn": 39, "suggestions": "248"}, {"ruleId": "231", "severity": 1, "message": "236", "line": 53, "column": 6, "nodeType": "233", "endLine": 53, "endColumn": 14, "suggestions": "249"}, {"ruleId": "224", "severity": 2, "message": "225", "line": 44, "column": 19, "nodeType": "226", "messageId": "227", "endLine": 44, "endColumn": 22, "suggestions": "250"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 17, "column": 11, "nodeType": "220", "endLine": 21, "endColumn": 13}, {"ruleId": "218", "severity": 1, "message": "219", "line": 20, "column": 11, "nodeType": "220", "endLine": 24, "endColumn": 13}, {"ruleId": "221", "severity": 2, "message": "251", "line": 33, "column": 14, "nodeType": null, "messageId": "223", "endLine": 33, "endColumn": 17}, {"ruleId": "224", "severity": 2, "message": "225", "line": 23, "column": 19, "nodeType": "226", "messageId": "227", "endLine": 23, "endColumn": 22, "suggestions": "252"}, {"ruleId": "224", "severity": 2, "message": "225", "line": 38, "column": 19, "nodeType": "226", "messageId": "227", "endLine": 38, "endColumn": 22, "suggestions": "253"}, {"ruleId": "224", "severity": 2, "message": "225", "line": 33, "column": 10, "nodeType": "226", "messageId": "227", "endLine": 33, "endColumn": 13, "suggestions": "254"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'paymentMethodId' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["255", "256"], ["257", "258"], ["259", "260"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'refreshUserData'. Either include it or remove the dependency array.", "ArrayExpression", ["261"], ["262", "263"], "React Hook useEffect has missing dependencies: 'handleRefreshUserData' and 'loadUserData'. Either include them or remove the dependency array.", ["264"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["265", "266", "267", "268"], "'refreshUserData' is defined but never used.", "'router' is assigned a value but never used.", ["269", "270"], ["271", "272"], "'VerificationNotice' is defined but never used.", ["273", "274"], ["275"], ["276", "277"], "'err' is defined but never used.", ["278", "279"], ["280", "281"], ["282", "283"], {"messageId": "284", "fix": "285", "desc": "286"}, {"messageId": "287", "fix": "288", "desc": "289"}, {"messageId": "284", "fix": "290", "desc": "286"}, {"messageId": "287", "fix": "291", "desc": "289"}, {"messageId": "284", "fix": "292", "desc": "286"}, {"messageId": "287", "fix": "293", "desc": "289"}, {"desc": "294", "fix": "295"}, {"messageId": "284", "fix": "296", "desc": "286"}, {"messageId": "287", "fix": "297", "desc": "289"}, {"desc": "298", "fix": "299"}, {"messageId": "300", "data": "301", "fix": "302", "desc": "303"}, {"messageId": "300", "data": "304", "fix": "305", "desc": "306"}, {"messageId": "300", "data": "307", "fix": "308", "desc": "309"}, {"messageId": "300", "data": "310", "fix": "311", "desc": "312"}, {"messageId": "284", "fix": "313", "desc": "286"}, {"messageId": "287", "fix": "314", "desc": "289"}, {"messageId": "284", "fix": "315", "desc": "286"}, {"messageId": "287", "fix": "316", "desc": "289"}, {"messageId": "284", "fix": "317", "desc": "286"}, {"messageId": "287", "fix": "318", "desc": "289"}, {"desc": "298", "fix": "319"}, {"messageId": "284", "fix": "320", "desc": "286"}, {"messageId": "287", "fix": "321", "desc": "289"}, {"messageId": "284", "fix": "322", "desc": "286"}, {"messageId": "287", "fix": "323", "desc": "289"}, {"messageId": "284", "fix": "324", "desc": "286"}, {"messageId": "287", "fix": "325", "desc": "289"}, {"messageId": "284", "fix": "326", "desc": "286"}, {"messageId": "287", "fix": "327", "desc": "289"}, "suggestUnknown", {"range": "328", "text": "329"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "330", "text": "331"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "332", "text": "329"}, {"range": "333", "text": "331"}, {"range": "334", "text": "329"}, {"range": "335", "text": "331"}, "Update the dependencies array to be: [refreshUserData]", {"range": "336", "text": "337"}, {"range": "338", "text": "329"}, {"range": "339", "text": "331"}, "Update the dependencies array to be: [handleRefreshUserData, loadUserData, router]", {"range": "340", "text": "341"}, "replaceWithAlt", {"alt": "342"}, {"range": "343", "text": "344"}, "Replace with `&apos;`.", {"alt": "345"}, {"range": "346", "text": "347"}, "Replace with `&lsquo;`.", {"alt": "348"}, {"range": "349", "text": "350"}, "Replace with `&#39;`.", {"alt": "351"}, {"range": "352", "text": "353"}, "Replace with `&rsquo;`.", {"range": "354", "text": "329"}, {"range": "355", "text": "331"}, {"range": "356", "text": "329"}, {"range": "357", "text": "331"}, {"range": "358", "text": "329"}, {"range": "359", "text": "331"}, {"range": "360", "text": "341"}, {"range": "361", "text": "329"}, {"range": "362", "text": "331"}, {"range": "363", "text": "329"}, {"range": "364", "text": "331"}, {"range": "365", "text": "329"}, {"range": "366", "text": "331"}, {"range": "367", "text": "329"}, {"range": "368", "text": "331"}, [3689, 3692], "unknown", [3689, 3692], "never", [5145, 5148], [5145, 5148], [902, 905], [902, 905], [2913, 2915], "[refreshUserData]", [439, 442], [439, 442], [2125, 2133], "[handleRefreshUserData, loadUserData, router]", "&apos;", [9959, 9993], "You haven&apos;t placed any orders yet.", "&lsquo;", [9959, 9993], "You haven&lsquo;t placed any orders yet.", "&#39;", [9959, 9993], "You haven&#39;t placed any orders yet.", "&rsquo;", [9959, 9993], "You haven&rsquo;t placed any orders yet.", [1119, 1122], [1119, 1122], [990, 993], [990, 993], [386, 389], [386, 389], [1507, 1515], [1378, 1381], [1378, 1381], [661, 664], [661, 664], [1079, 1082], [1079, 1082], [653, 656], [653, 656]]