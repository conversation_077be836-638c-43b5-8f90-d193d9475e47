(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{722:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(3464).A.create({baseURL:"",withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json",Accept:"application/json"}});r.interceptors.request.use(e=>{var t;console.log("Making request to: ".concat(null==(t=e.method)?void 0:t.toUpperCase()," ").concat(e.url));let a=localStorage.getItem("token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&419===e.response.status&&console.error("CSRF token mismatch. Refreshing token..."),Promise.reject(e)));let s=r},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},6091:(e,t,a)=>{Promise.resolve().then(a.bind(a,9690))},8258:(e,t,a)=>{"use strict";a.d(t,{uR:()=>o,mx:()=>u,M$:()=>i,QE:()=>c,bk:()=>n,d$:()=>m});var r=a(722);let s={login:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/login",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Login error details:",e),e}},register:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/register",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Registration error details:",e),e}},logout:async()=>{try{await r.A.get("/sanctum/csrf-cookie"),await r.A.post("/api/logout"),localStorage.removeItem("token"),localStorage.removeItem("user")}catch(e){console.error("Logout error:",e),localStorage.removeItem("token"),localStorage.removeItem("user")}},getCurrentUser:()=>{let e=localStorage.getItem("user");return e?JSON.parse(e):null},isAuthenticated:()=>!!localStorage.getItem("token"),isEmailVerified:()=>{let e=localStorage.getItem("user");return!!e&&!!JSON.parse(e).email_verified_at},resendVerificationEmail:async()=>{try{if(!s.isAuthenticated())throw Error("You must be logged in to resend verification email");console.log("Attempting to resend verification email...");let e=await r.A.get("/sanctum/csrf-cookie");console.log("CSRF cookie response:",e.status),console.log("Sending verification email request...");let t=await r.A.post("/api/email/verification-notification");return console.log("Verification email response:",t.status,t.data),t.data}catch(e){throw console.error("Resend verification email error:",e),e}},refreshUserData:async()=>{try{if(!s.isAuthenticated())return null;let e=await r.A.get("/api/user");if(console.log("Refresh user data response:",e.data),e.data&&e.data.success&&e.data.data&&e.data.data.user){let t=e.data.data.user;return localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),t}return null}catch(e){return console.error("Refresh user data error:",e),null}}},o=s,n={fetchProducts:async()=>{try{return(await r.A.get("/products")).data}catch(e){throw e}},getFeaturedProducts:async()=>{try{return(await r.A.get("/products?featured=true")).data}catch(e){throw e}},getProductBySlug:async e=>{try{let t=(await r.A.get("/products")).data.data.find(t=>t.slug===e);if(!t)throw Error("Product not found");return t}catch(e){throw e}},getAllProducts:async()=>{try{return(await r.A.get("/products")).data.data}catch(e){throw e}}},i={getAllCategories:async()=>{try{return(await r.A.get("/categories")).data.data}catch(e){throw e}},getCategoryBySlug:async e=>{try{let t=(await r.A.get("/categories")).data.data.find(t=>t.slug===e);if(!t)throw Error("Category not found");return t}catch(e){throw e}}},c={getAllOrders:async()=>{try{return(await r.A.get("/orders")).data.data}catch(e){throw e}},getOrderById:async e=>{try{return(await r.A.get("/orders/".concat(e))).data.data}catch(e){throw e}},createOrder:async e=>{try{return(await r.A.post("/orders",e)).data.data}catch(e){throw e}},updateOrder:async(e,t)=>{try{return(await r.A.put("/orders/".concat(e),{notes:t})).data.data}catch(e){throw e}},cancelOrder:async e=>{try{return(await r.A.post("/orders/".concat(e,"/cancel"))).data.data}catch(e){throw e}}},d="ecommerce_cart",l={getCart:()=>{let e=localStorage.getItem(d);return e?JSON.parse(e):{items:[],total:0}},saveCart:e=>{localStorage.setItem(d,JSON.stringify(e))},addToCart:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=l.getCart(),r=a.items.findIndex(t=>t.product_id===e.id);return -1!==r?a.items[r].quantity+=t:a.items.push({product_id:e.id,quantity:t,product:e}),a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),l.saveCart(a),a},updateQuantity:(e,t)=>{let a=l.getCart(),r=a.items.findIndex(t=>t.product_id===e);return -1!==r&&(t<=0?a.items.splice(r,1):a.items[r].quantity=t,a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),l.saveCart(a)),a},removeFromCart:e=>{let t=l.getCart(),a=t.items.findIndex(t=>t.product_id===e);return -1!==a&&(t.items.splice(a,1),t.total=t.items.reduce((e,t)=>e+t.product.price*t.quantity,0),l.saveCart(t)),t},clearCart:()=>{let e={items:[],total:0};return l.saveCart(e),e},getItemCount:()=>l.getCart().items.reduce((e,t)=>e+t.quantity,0)},u=l,m={createPaymentIntent:async e=>{try{return(await r.A.post("/payment/create-intent",e)).data}catch(e){throw e}},processPayment:async(e,t)=>{try{return(await r.A.post("/payment/process",{payment_method_id:e,items:t})).data}catch(e){throw e}}}},9690:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(5155),s=a(2115),o=a(6874),n=a.n(o),i=a(5695),c=a(8258);let d=()=>{let[e,t]=(0,s.useState)(""),[a,o]=(0,s.useState)(""),[d,l]=(0,s.useState)(null),[u,m]=(0,s.useState)(!1),g=(0,i.useRouter)(),h=async t=>{t.preventDefault();try{m(!0),l(null),await c.uR.login({email:e,password:a}),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),g.push("/")}catch(e){var r,s,o,n;console.error("Login error:",e),(null==(s=e.response)||null==(r=s.data)?void 0:r.email_verified)===!1?l("Email not verified. Please check your email for verification link or go to dashboard to resend it."):l((null==(n=e.response)||null==(o=n.data)?void 0:o.message)||"Invalid email or password")}finally{m(!1)}};return(0,r.jsxs)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,r.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,r.jsx)(n(),{href:"/register",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"create a new account"})]})]}),(0,r.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[d&&(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:d})})]})}),(0,r.jsxs)("form",{className:"space-y-6",onSubmit:h,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:a,onChange:e=>o(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:u,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ".concat(u?"opacity-70 cursor-not-allowed":""),children:u?"Signing in...":"Sign in"})})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,441,684,358],()=>t(6091)),_N_E=e.O()}]);