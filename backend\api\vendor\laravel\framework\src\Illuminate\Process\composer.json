{"name": "illuminate/process", "description": "The Illuminate Process package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/collections": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "illuminate/support": "^12.0", "symfony/process": "^7.2.0"}, "autoload": {"psr-4": {"Illuminate\\Process\\": ""}}, "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}