meilisearch:
    image: 'getmeili/meilisearch:latest'
    ports:
        - '${FORWARD_MEILISEARCH_PORT:-7700}:7700'
    environment:
        MEILI_NO_ANALYTICS: '${MEILISEARCH_NO_ANALYTICS:-false}'
    volumes:
        - 'sail-meilisearch:/meili_data'
    networks:
        - sail
    healthcheck:
        test: ["CMD", "wget", "--no-verbose", "--spider",  "http://127.0.0.1:7700/health"]
        retries: 3
        timeout: 5s
