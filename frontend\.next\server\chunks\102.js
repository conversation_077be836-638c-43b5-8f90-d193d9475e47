"use strict";exports.id=102,exports.ids=[102],exports.modules={53199:(e,s,a)=>{a.d(s,{rm:()=>d,AA:()=>n});var t=a(60687);a(43210);var r=a(35421),l=a(85814),i=a.n(l);let n=({product:e})=>(0,t.jsxs)("div",{className:"group relative bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden",children:[(0,t.jsx)("div",{className:"aspect-w-3 aspect-h-4 bg-gray-200 group-hover:opacity-75 h-48",children:e.image?(0,t.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-center object-cover"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gray-100",children:(0,t.jsx)("span",{className:"text-gray-400",children:"No image"})})}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2 p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:(0,t.jsxs)(i(),{href:`/products/${e.slug}`,className:"block",children:[(0,t.jsx)("span",{"aria-hidden":"true",className:"absolute inset-0"}),e.name]})}),(0,t.jsx)("p",{className:"text-sm text-gray-500 line-clamp-2",children:e.description}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("p",{className:"text-lg font-medium text-gray-900",children:["$",Number(e.price).toFixed(2)]}),e.quantity>0?(0,t.jsx)("span",{className:"text-sm text-green-600",children:"In Stock"}):(0,t.jsx)("span",{className:"text-sm text-red-600",children:"Out of Stock"})]}),(0,t.jsx)("button",{onClick:()=>{r.mx.addToCart(e,1),window.dispatchEvent(new Event("storage"))},disabled:e.quantity<=0,className:`mt-2 w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${e.quantity>0?"bg-indigo-600 hover:bg-indigo-700":"bg-gray-400 cursor-not-allowed"}`,children:"Add to Cart"})]})]}),d=({category:e})=>(0,t.jsxs)(i(),{href:`/categories/${e.slug}`,className:"group block bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow",children:[(0,t.jsx)("div",{className:"aspect-w-3 aspect-h-2 bg-gray-200 group-hover:opacity-75 h-40",children:e.image?(0,t.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-center object-cover"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gray-100",children:(0,t.jsx)("span",{className:"text-gray-400",children:"No image"})})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),e.description&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 line-clamp-2",children:e.description})]})]});a(14329),a(68399),a(57071)},57071:(e,s,a)=>{a.d(s,{A:()=>n});var t=a(60687),r=a(43210),l=a(46299);let i={style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"}},invalid:{color:"#9e2146"}}},n=({onSuccess:e,onError:s,loading:a,setLoading:n})=>{let d=(0,l.t2)(),c=(0,l.HH)(),[o,m]=(0,r.useState)(null),x=async a=>{if(a.preventDefault(),!d||!c)return;let t=c.getElement(l.hA);if(t){n(!0),m(null);try{let{error:a,paymentMethod:r}=await d.createPaymentMethod({type:"card",card:t});a?(m(a.message||"An error occurred during payment"),s(a.message||"An error occurred during payment")):r&&e(r.id)}catch{m("Payment failed. Please try again."),s("Payment failed. Please try again.")}finally{n(!1)}}};return(0,t.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Card Information"}),(0,t.jsx)("div",{className:"border border-gray-300 rounded-md p-3 bg-white",children:(0,t.jsx)(l.hA,{options:i,onChange:e=>{e.error?m(e.error.message):m(null)}})}),o&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o})]}),(0,t.jsx)("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"This is a test environment. Use test card number 4242 4242 4242 4242 with any future expiry date and any 3-digit CVC."})})]})}),(0,t.jsx)("button",{type:"submit",disabled:!d||a,className:`w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white ${!d||a?"bg-gray-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700 cursor-pointer"}`,children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"}),"Processing Payment..."]}):"Pay Now"})]})}},70440:(e,s,a)=>{a.r(s),a.d(s,{default:()=>r});var t=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};