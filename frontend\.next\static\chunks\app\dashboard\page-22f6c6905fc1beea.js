(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{722:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(3464).A.create({baseURL:"",withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json",Accept:"application/json"}});r.interceptors.request.use(e=>{var t;console.log("Making request to: ".concat(null==(t=e.method)?void 0:t.toUpperCase()," ").concat(e.url));let a=localStorage.getItem("token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&419===e.response.status&&console.error("CSRF token mismatch. Refreshing token..."),Promise.reject(e)));let s=r},1541:(e,t,a)=>{"use strict";a.d(t,{eq:()=>i,nO:()=>s});var r=a(8258);let s=async()=>{try{console.log("Refreshing user data from backend...");let e=await r.uR.refreshUserData();return console.log("User data refreshed successfully:",e),e}catch(e){return console.error("Failed to refresh user data:",e),r.uR.getCurrentUser()}},i=e=>(window.addEventListener("storage",e),window.addEventListener("storage-update",e),()=>{window.removeEventListener("storage",e),window.removeEventListener("storage-update",e)})},3265:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(5155),s=a(2115),i=a(6874),n=a.n(i),o=a(8258);let l=e=>{let{email:t,showDismiss:a=!1,onDismiss:i}=e,[l,c]=(0,s.useState)(!1),[d,u]=(0,s.useState)(!1),[m,x]=(0,s.useState)(null),[g,h]=(0,s.useState)(!1);if(g)return null;let p=async()=>{try{c(!0),x(null),await o.uR.resendVerificationEmail(),u(!0)}catch(e){x("Failed to resend verification email. Please try again.")}finally{c(!1)}};return(0,r.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3 flex-grow",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("p",{className:"text-sm text-yellow-700",children:["Your email address (",t,") has not been verified. Please check your email for a verification link."]}),a&&(0,r.jsx)("button",{onClick:()=>{h(!0),i&&i()},className:"ml-3 flex-shrink-0 text-yellow-500 hover:text-yellow-700",children:(0,r.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),m&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600",children:m}),d?(0,r.jsx)("p",{className:"mt-2 text-sm text-green-600",children:"A new verification link has been sent to your email address."}):(0,r.jsxs)("div",{className:"mt-2 flex flex-wrap gap-2",children:[(0,r.jsx)("button",{onClick:p,disabled:l,className:"inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 cursor-pointer",children:l?"Sending...":"Resend Verification Email"}),(0,r.jsx)(n(),{href:"/profile",className:"inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Go to My Account"})]})]})]})})}},4879:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(5155),s=a(2115),i=a(5695),n=a(6874),o=a.n(n),l=a(8258),c=a(3265),d=a(1541);let u=()=>{let[e,t]=(0,s.useState)(null),[a,n]=(0,s.useState)([]),[u,m]=(0,s.useState)(!0),[x,g]=(0,s.useState)(!0),h=(0,i.useRouter)(),p=()=>{if(!l.uR.isAuthenticated())return void h.push("/login");t(l.uR.getCurrentUser()),m(!1)},f=async()=>{try{m(!0),await (0,d.nO)(),p()}catch(e){console.error("Failed to refresh user data:",e),p()}},y=async()=>{try{if(g(!0),l.uR.isAuthenticated()){let e=await l.QE.getAllOrders();n(e)}}catch(e){console.error("Failed to fetch orders:",e)}finally{g(!1)}};return((0,s.useEffect)(()=>(f(),y(),(0,d.eq)(()=>{p(),y()})),[h]),u)?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,r.jsx)("button",{onClick:f,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",disabled:u,children:u?"Refreshing...":"Refresh Data"})]}),e&&!l.uR.isEmailVerified()&&(0,r.jsx)(c.A,{email:e.email,showDismiss:!0}),(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-8",children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"User Information"}),(0,r.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Personal details and account information."})]}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:(0,r.jsxs)("dl",{children:[(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Full name"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:null==e?void 0:e.name})]}),(0,r.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Email address"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:null==e?void 0:e.email})]}),(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Email verification"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:l.uR.isEmailVerified()?(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Verified"}):(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"Not verified"})})]}),(0,r.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Account created"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:new Date(null==e?void 0:e.created_at).toLocaleDateString()})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Your Orders"}),(0,r.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"View and manage your orders."})]}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:x?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"})}):a.length>0?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order ID"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",e.id]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("completed"===e.status?"bg-green-100 text-green-800":"processing"===e.status?"bg-blue-100 text-blue-800":"canceled"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["$",Number(e.total_amount).toFixed(2)]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,r.jsx)(o(),{href:"/order-confirmation/".concat(e.id),className:"text-indigo-600 hover:text-indigo-900 mr-4",children:"View"}),"pending"===e.status&&(0,r.jsx)("button",{onClick:async()=>{if(window.confirm("Are you sure you want to cancel this order?"))try{await l.QE.cancelOrder(e.id),y()}catch(e){console.error("Failed to cancel order:",e),alert("Failed to cancel order. Please try again.")}},className:"text-red-600 hover:text-red-900",children:"Cancel"})]})]},e.id))})]})}):(0,r.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"You haven't placed any orders yet."}),(0,r.jsx)(o(),{href:"/products",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Start Shopping"})]})})]})]})}},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},8258:(e,t,a)=>{"use strict";a.d(t,{uR:()=>i,mx:()=>u,M$:()=>o,QE:()=>l,bk:()=>n,d$:()=>m});var r=a(722);let s={login:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/login",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Login error details:",e),e}},register:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/register",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Registration error details:",e),e}},logout:async()=>{try{await r.A.get("/sanctum/csrf-cookie"),await r.A.post("/api/logout"),localStorage.removeItem("token"),localStorage.removeItem("user")}catch(e){console.error("Logout error:",e),localStorage.removeItem("token"),localStorage.removeItem("user")}},getCurrentUser:()=>{let e=localStorage.getItem("user");return e?JSON.parse(e):null},isAuthenticated:()=>!!localStorage.getItem("token"),isEmailVerified:()=>{let e=localStorage.getItem("user");return!!e&&!!JSON.parse(e).email_verified_at},resendVerificationEmail:async()=>{try{if(!s.isAuthenticated())throw Error("You must be logged in to resend verification email");console.log("Attempting to resend verification email...");let e=await r.A.get("/sanctum/csrf-cookie");console.log("CSRF cookie response:",e.status),console.log("Sending verification email request...");let t=await r.A.post("/api/email/verification-notification");return console.log("Verification email response:",t.status,t.data),t.data}catch(e){throw console.error("Resend verification email error:",e),e}},refreshUserData:async()=>{try{if(!s.isAuthenticated())return null;let e=await r.A.get("/api/user");if(console.log("Refresh user data response:",e.data),e.data&&e.data.success&&e.data.data&&e.data.data.user){let t=e.data.data.user;return localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),t}return null}catch(e){return console.error("Refresh user data error:",e),null}}},i=s,n={fetchProducts:async()=>{try{return(await r.A.get("/products")).data}catch(e){throw e}},getFeaturedProducts:async()=>{try{return(await r.A.get("/products?featured=true")).data}catch(e){throw e}},getProductBySlug:async e=>{try{let t=(await r.A.get("/products")).data.data.find(t=>t.slug===e);if(!t)throw Error("Product not found");return t}catch(e){throw e}},getAllProducts:async()=>{try{return(await r.A.get("/products")).data.data}catch(e){throw e}}},o={getAllCategories:async()=>{try{return(await r.A.get("/categories")).data.data}catch(e){throw e}},getCategoryBySlug:async e=>{try{let t=(await r.A.get("/categories")).data.data.find(t=>t.slug===e);if(!t)throw Error("Category not found");return t}catch(e){throw e}}},l={getAllOrders:async()=>{try{return(await r.A.get("/orders")).data.data}catch(e){throw e}},getOrderById:async e=>{try{return(await r.A.get("/orders/".concat(e))).data.data}catch(e){throw e}},createOrder:async e=>{try{return(await r.A.post("/orders",e)).data.data}catch(e){throw e}},updateOrder:async(e,t)=>{try{return(await r.A.put("/orders/".concat(e),{notes:t})).data.data}catch(e){throw e}},cancelOrder:async e=>{try{return(await r.A.post("/orders/".concat(e,"/cancel"))).data.data}catch(e){throw e}}},c="ecommerce_cart",d={getCart:()=>{let e=localStorage.getItem(c);return e?JSON.parse(e):{items:[],total:0}},saveCart:e=>{localStorage.setItem(c,JSON.stringify(e))},addToCart:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=d.getCart(),r=a.items.findIndex(t=>t.product_id===e.id);return -1!==r?a.items[r].quantity+=t:a.items.push({product_id:e.id,quantity:t,product:e}),a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(a),a},updateQuantity:(e,t)=>{let a=d.getCart(),r=a.items.findIndex(t=>t.product_id===e);return -1!==r&&(t<=0?a.items.splice(r,1):a.items[r].quantity=t,a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(a)),a},removeFromCart:e=>{let t=d.getCart(),a=t.items.findIndex(t=>t.product_id===e);return -1!==a&&(t.items.splice(a,1),t.total=t.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(t)),t},clearCart:()=>{let e={items:[],total:0};return d.saveCart(e),e},getItemCount:()=>d.getCart().items.reduce((e,t)=>e+t.quantity,0)},u=d,m={createPaymentIntent:async e=>{try{return(await r.A.post("/payment/create-intent",e)).data}catch(e){throw e}},processPayment:async(e,t)=>{try{return(await r.A.post("/payment/process",{payment_method_id:e,items:t})).data}catch(e){throw e}}}},9942:(e,t,a)=>{Promise.resolve().then(a.bind(a,4879))}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,441,684,358],()=>t(9942)),_N_E=e.O()}]);