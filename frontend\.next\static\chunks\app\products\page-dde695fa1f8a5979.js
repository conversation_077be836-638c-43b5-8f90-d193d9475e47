(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{1057:(e,l,s)=>{"use strict";s.r(l),s.d(l,{default:()=>c});var r=s(5155),a=s(2115),d=s(8258),t=s(9768);function c(){let[e,l]=(0,a.useState)([]),[s,c]=(0,a.useState)(!0),[i,n]=(0,a.useState)(null);return((0,a.useEffect)(()=>{(async()=>{try{c(!0);let e=await d.bk.fetchProducts();l(e.data),n(null)}catch(e){console.error("Error fetching products:",e),n("Failed to load products. Please try again later.")}finally{c(!1)}})()},[]),s)?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):i?(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 my-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:i})})]})}):(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"All Products"}),e.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4",children:e.map(e=>(0,r.jsx)(t.AA,{product:e},e.id))}):(0,r.jsx)("p",{className:"text-gray-500",children:"No products available."})]})}},6040:(e,l,s)=>{Promise.resolve().then(s.bind(s,1057))}},e=>{var l=l=>e(e.s=l);e.O(0,[464,874,855,386,768,441,684,358],()=>l(6040)),_N_E=e.O()}]);