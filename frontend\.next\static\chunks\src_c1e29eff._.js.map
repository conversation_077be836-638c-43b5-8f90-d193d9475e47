{"version": 3, "sources": [], "sections": [{"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/Layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { ShoppingCartIcon, UserIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { AuthService, CartService } from '../services';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [cartItemCount, setCartItemCount] = useState(0);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // Check authentication status\n    setIsAuthenticated(AuthService.isAuthenticated());\n    \n    // Get cart item count\n    setCartItemCount(CartService.getItemCount());\n    \n    // Add event listener for cart updates\n    const handleStorageChange = () => {\n      setCartItemCount(CartService.getItemCount());\n    };\n    \n    window.addEventListener('storage', handleStorageChange);\n    \n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, []);\n\n  const handleLogout = async () => {\n    await AuthService.logout();\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0 flex items-center\">\n                <Link to=\"/\" className=\"text-2xl font-bold text-indigo-600\">\n                  EcommerceApp\n                </Link>\n              </div>\n              <nav className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n                <Link\n                  to=\"/\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Home\n                </Link>\n                <Link\n                  to=\"/products\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Products\n                </Link>\n                <Link\n                  to=\"/categories\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Categories\n                </Link>\n              </nav>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4\">\n              <Link to=\"/cart\" className=\"relative p-1 rounded-full text-gray-400 hover:text-gray-500\">\n                <ShoppingCartIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                {cartItemCount > 0 && (\n                  <span className=\"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full\">\n                    {cartItemCount}\n                  </span>\n                )}\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"p-1 rounded-full text-gray-400 hover:text-gray-500\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </Link>\n                  <button\n                    onClick={handleLogout}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50\"\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n            <div className=\"-mr-2 flex items-center sm:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {isMenuOpen ? (\n                  <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                ) : (\n                  <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden\">\n            <div className=\"pt-2 pb-3 space-y-1\">\n              <Link\n                to=\"/\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link\n                to=\"/products\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Products\n              </Link>\n              <Link\n                to=\"/categories\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Categories\n              </Link>\n              <Link\n                to=\"/cart\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Cart ({cartItemCount})\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Profile\n                  </Link>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main content */}\n      <main className=\"flex-grow\">\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white\">\n        <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n          <p className=\"text-center text-gray-500 text-sm\">\n            &copy; {new Date().getFullYear()} EcommerceApp. All rights reserved.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;AALA;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,8BAA8B;YAC9B,mBAAmB,+KAAA,CAAA,cAAW,CAAC,eAAe;YAE9C,sBAAsB;YACtB,iBAAiB,+KAAA,CAAA,cAAW,CAAC,YAAY;YAEzC,sCAAsC;YACtC,MAAM;wDAAsB;oBAC1B,iBAAiB,+KAAA,CAAA,cAAW,CAAC,YAAY;gBAC3C;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC;oCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,+KAAA,CAAA,cAAW,CAAC,MAAM;QACxB,mBAAmB;QACnB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+KAAA,CAAA,OAAI;gDAAC,IAAG;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAKL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+KAAA,CAAA,OAAI;4CAAC,IAAG;4CAAQ,WAAU;;8DACzB,6LAAC,kOAAA,CAAA,mBAAgB;oDAAC,WAAU;oDAAU,eAAY;;;;;;gDACjD,gBAAgB,mBACf,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;wCAIN,gCACC;;8DACE,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DAEV,cAAA,6LAAC,kNAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAU,eAAY;;;;;;;;;;;8DAE5C,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;yEAKH;;8DACE,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;8CAMP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,2BACC,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;qEAEjD,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ1D,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;;wCAC9B;wCACQ;wCAAc;;;;;;;gCAEtB,gCACC;;sDACE,6LAAC,+KAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,6LAAC;4CACC,SAAS;gDACP;gDACA,cAAc;4CAChB;4CACA,WAAU;sDACX;;;;;;;iEAKH;;sDACE,6LAAC,+KAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,6LAAC,+KAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAKL,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAoC;4BACvC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;GApNM;;QAIa,+KAAA,CAAA,cAAW;;;KAJxB;uCAsNS", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Product, CartService } from '../services';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  const handleAddToCart = () => {\n    CartService.addToCart(product, 1);\n    // Trigger storage event to update cart count in Layout\n    window.dispatchEvent(new Event('storage'));\n  };\n\n  return (\n    <div className=\"group relative bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden\">\n      <div className=\"aspect-w-3 aspect-h-4 bg-gray-200 group-hover:opacity-75 h-48\">\n        {product.image ? (\n          <img\n            src={product.image}\n            alt={product.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"flex flex-col space-y-2 p-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">\n          <Link href={`/products/${product.slug}`} className=\"block\">\n            <span aria-hidden=\"true\" className=\"absolute inset-0\" />\n            {product.name}\n          </Link>\n        </h3>\n        <p className=\"text-sm text-gray-500 line-clamp-2\">{product.description}</p>\n        <div className=\"flex justify-between items-center\">\n          <p className=\"text-lg font-medium text-gray-900\">${Number(product.price).toFixed(2)}</p>\n          {product.quantity > 0 ? (\n            <span className=\"text-sm text-green-600\">In Stock</span>\n          ) : (\n            <span className=\"text-sm text-red-600\">Out of Stock</span>\n          )}\n        </div>\n        <button\n          onClick={handleAddToCart}\n          disabled={product.quantity <= 0}\n          className={`mt-2 w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${\n            product.quantity > 0\n              ? 'bg-indigo-600 hover:bg-indigo-700'\n              : 'bg-gray-400 cursor-not-allowed'\n          }`}\n        >\n          Add to Cart\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAMA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,MAAM,kBAAkB;QACtB,+KAAA,CAAA,cAAW,CAAC,SAAS,CAAC,SAAS;QAC/B,uDAAuD;QACvD,OAAO,aAAa,CAAC,IAAI,MAAM;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,KAAK,iBACZ,6LAAC;oBACC,KAAK,QAAQ,KAAK;oBAClB,KAAK,QAAQ,IAAI;oBACjB,WAAU;;;;;yCAGZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;4BAAE,WAAU;;8CACjD,6LAAC;oCAAK,eAAY;oCAAO,WAAU;;;;;;gCAClC,QAAQ,IAAI;;;;;;;;;;;;kCAGjB,6LAAC;wBAAE,WAAU;kCAAsC,QAAQ,WAAW;;;;;;kCACtE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAoC;oCAAE,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC;;;;;;;4BAChF,QAAQ,QAAQ,GAAG,kBAClB,6LAAC;gCAAK,WAAU;0CAAyB;;;;;qDAEzC,6LAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;;kCAG3C,6LAAC;wBACC,SAAS;wBACT,UAAU,QAAQ,QAAQ,IAAI;wBAC9B,WAAW,CAAC,kIAAkI,EAC5I,QAAQ,QAAQ,GAAG,IACf,sCACA,kCACJ;kCACH;;;;;;;;;;;;;;;;;;AAMT;KApDM;uCAsDS", "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/CategoryCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Category } from '../services';\n\ninterface CategoryCardProps {\n  category: Category;\n}\n\nconst CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {\n  return (\n    <Link\n      href={`/categories/${category.slug}`}\n      className=\"group block bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow\"\n    >\n      <div className=\"aspect-w-3 aspect-h-2 bg-gray-200 group-hover:opacity-75 h-40\">\n        {category.image ? (\n          <img\n            src={category.image}\n            alt={category.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"p-4\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{category.name}</h3>\n        {category.description && (\n          <p className=\"mt-1 text-sm text-gray-500 line-clamp-2\">{category.description}</p>\n        )}\n      </div>\n    </Link>\n  );\n};\n\nexport default CategoryCard;\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE;QACpC,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,iBACb,6LAAC;oBACC,KAAK,SAAS,KAAK;oBACnB,KAAK,SAAS,IAAI;oBAClB,WAAU;;;;;yCAGZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC,SAAS,IAAI;;;;;;oBAC/D,SAAS,WAAW,kBACnB,6LAAC;wBAAE,WAAU;kCAA2C,SAAS,WAAW;;;;;;;;;;;;;;;;;;AAKtF;KA3BM;uCA6BS", "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/StripePaymentForm.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useStripe, useElements, CardElement } from \"@stripe/react-stripe-js\";\nimport { StripeCardElementOptions } from \"@stripe/stripe-js\";\n\ninterface StripePaymentFormProps {\n  onSuccess: (paymentMethodId: string) => void;\n  onError: (error: string) => void;\n  loading: boolean;\n  setLoading: (loading: boolean) => void;\n}\n\nconst cardElementOptions: StripeCardElementOptions = {\n  style: {\n    base: {\n      fontSize: \"16px\",\n      color: \"#424770\",\n      \"::placeholder\": {\n        color: \"#aab7c4\",\n      },\n    },\n    invalid: {\n      color: \"#9e2146\",\n    },\n  },\n};\n\nconst StripePaymentForm: React.FC<StripePaymentFormProps> = ({\n  onSuccess,\n  onError,\n  loading,\n  setLoading,\n}) => {\n  const stripe = useStripe();\n  const elements = useElements();\n  const [cardError, setCardError] = useState<string | null>(null);\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n\n    if (!stripe || !elements) {\n      return;\n    }\n\n    const cardElement = elements.getElement(CardElement);\n\n    if (!cardElement) {\n      return;\n    }\n\n    setLoading(true);\n    setCardError(null);\n\n    try {\n      // Create payment method\n      const { error, paymentMethod } = await stripe.createPaymentMethod({\n        type: \"card\",\n        card: cardElement,\n      });\n\n      if (error) {\n        setCardError(error.message || \"An error occurred during payment\");\n        onError(error.message || \"An error occurred during payment\");\n      } else if (paymentMethod) {\n        onSuccess(paymentMethod.id);\n      }\n    } catch {\n      setCardError(\"Payment failed. Please try again.\");\n      onError(\"Payment failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCardChange = (event: { error?: { message: string } }) => {\n    if (event.error) {\n      setCardError(event.error.message);\n    } else {\n      setCardError(null);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Card Information\n        </label>\n        <div className=\"border border-gray-300 rounded-md p-3 bg-white\">\n          <CardElement\n            options={cardElementOptions}\n            onChange={handleCardChange}\n          />\n        </div>\n        {cardError && <p className=\"mt-1 text-sm text-red-600\">{cardError}</p>}\n      </div>\n\n      <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <svg\n              className=\"h-5 w-5 text-blue-400\"\n              viewBox=\"0 0 20 20\"\n              fill=\"currentColor\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm text-blue-700\">\n              This is a test environment. Use test card number 4242 4242 4242\n              4242 with any future expiry date and any 3-digit CVC.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <button\n        type=\"submit\"\n        disabled={!stripe || loading}\n        className={`w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white ${\n          !stripe || loading\n            ? \"bg-gray-400 cursor-not-allowed\"\n            : \"bg-indigo-600 hover:bg-indigo-700 cursor-pointer\"\n        }`}\n      >\n        {loading ? (\n          <>\n            <div className=\"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2\"></div>\n            Processing Payment...\n          </>\n        ) : (\n          \"Pay Now\"\n        )}\n      </button>\n    </form>\n  );\n};\n\nexport default StripePaymentForm;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAUA,MAAM,qBAA+C;IACnD,OAAO;QACL,MAAM;YACJ,UAAU;YACV,OAAO;YACP,iBAAiB;gBACf,OAAO;YACT;QACF;QACA,SAAS;YACP,OAAO;QACT;IACF;AACF;AAEA,MAAM,oBAAsD,CAAC,EAC3D,SAAS,EACT,OAAO,EACP,OAAO,EACP,UAAU,EACX;;IACC,MAAM,SAAS,CAAA,GAAA,sLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB;QACF;QAEA,MAAM,cAAc,SAAS,UAAU,CAAC,sLAAA,CAAA,cAAW;QAEnD,IAAI,CAAC,aAAa;YAChB;QACF;QAEA,WAAW;QACX,aAAa;QAEb,IAAI;YACF,wBAAwB;YACxB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,OAAO,mBAAmB,CAAC;gBAChE,MAAM;gBACN,MAAM;YACR;YAEA,IAAI,OAAO;gBACT,aAAa,MAAM,OAAO,IAAI;gBAC9B,QAAQ,MAAM,OAAO,IAAI;YAC3B,OAAO,IAAI,eAAe;gBACxB,UAAU,cAAc,EAAE;YAC5B;QACF,EAAE,OAAM;YACN,aAAa;YACb,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM,KAAK,EAAE;YACf,aAAa,MAAM,KAAK,CAAC,OAAO;QAClC,OAAO;YACL,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,sLAAA,CAAA,cAAW;4BACV,SAAS;4BACT,UAAU;;;;;;;;;;;oBAGb,2BAAa,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG1D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,SAAQ;gCACR,MAAK;0CAEL,cAAA,6LAAC;oCACC,UAAS;oCACT,GAAE;oCACF,UAAS;;;;;;;;;;;;;;;;sCAIf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU;gBACrB,WAAW,CAAC,+HAA+H,EACzI,CAAC,UAAU,UACP,mCACA,oDACJ;0BAED,wBACC;;sCACE,6LAAC;4BAAI,WAAU;;;;;;wBAAkF;;mCAInG;;;;;;;;;;;;AAKV;GAlHM;;QAMW,sLAAA,CAAA,YAAS;QACP,sLAAA,CAAA,cAAW;;;KAPxB;uCAoHS", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/index.ts"], "sourcesContent": ["export { default as Layout } from './Layout';\nexport { default as ProductCard } from './ProductCard';\nexport { default as CategoryCard } from './CategoryCard';\nexport { default as VerificationNotice } from './VerificationNotice';\nexport { default as CsrfToken } from './CsrfToken';\nexport { default as StripePaymentForm } from './StripePaymentForm';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/app/products/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { ProductService, Product } from \"../../services\";\nimport { ProductCard } from \"../../components\";\n\nexport default function ProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const data = await ProductService.fetchProducts();\n\n        // Safely access the data array\n        if (data && data.data) {\n          setProducts(Array.isArray(data.data) ? data.data : []);\n        } else {\n          setProducts([]);\n        }\n\n        setError(null);\n      } catch (err) {\n        console.error(\"Error fetching products:\", err);\n        setError(\"Failed to load products. Please try again later.\");\n        setProducts([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-red-50 border-l-4 border-red-400 p-4 my-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <svg\n              className=\"h-5 w-5 text-red-400\"\n              viewBox=\"0 0 20 20\"\n              fill=\"currentColor\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm text-red-700\">{error}</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">All Products</h1>\n\n      {products.length > 0 ? (\n        <div className=\"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4\">\n          {products.map((product) => (\n            <ProductCard key={product.id} product={product} />\n          ))}\n        </div>\n      ) : (\n        <p className=\"text-gray-500\">No products available.</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;wDAAgB;oBACpB,IAAI;wBACF,WAAW;wBACX,MAAM,OAAO,MAAM,qLAAA,CAAA,iBAAc,CAAC,aAAa;wBAE/C,+BAA+B;wBAC/B,IAAI,QAAQ,KAAK,IAAI,EAAE;4BACrB,YAAY,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE;wBACvD,OAAO;4BACL,YAAY,EAAE;wBAChB;wBAEA,SAAS;oBACX,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,SAAS;wBACT,YAAY,EAAE;oBAChB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,SAAQ;4BACR,MAAK;sCAEL,cAAA,6LAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;;;;;;kCAIf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAG,WAAU;0BAAwC;;;;;;YAErD,SAAS,MAAM,GAAG,kBACjB,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,8KAAA,CAAA,cAAW;wBAAkB,SAAS;uBAArB,QAAQ,EAAE;;;;;;;;;qCAIhC,6LAAC;gBAAE,WAAU;0BAAgB;;;;;;;;;;;;AAIrC;GA/EwB;KAAA", "debugId": null}}]}