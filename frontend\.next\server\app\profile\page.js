(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24313:(e,t,r)=>{Promise.resolve().then(r.bind(r,75758))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},53936:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75758)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\profile\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75758:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\profile\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),i=r(43210),a=r(16189),n=r(35421),o=r(31309);let d=()=>{let[e,t]=(0,i.useState)(null),[r,d]=(0,i.useState)(!0),l=(0,a.useRouter)(),c=()=>{if(!n.uR.isAuthenticated())return void l.push("/login");t(n.uR.getCurrentUser()),d(!1)},m=async()=>{try{d(!0),await (0,o.nO)(),c()}catch(e){console.error("Failed to refresh user data:",e),c()}};return((0,i.useEffect)(()=>(m(),(0,o.eq)(()=>{c()})),[l]),r)?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Account"}),(0,s.jsx)("button",{onClick:m,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",disabled:r,children:r?"Refreshing...":"Refresh Data"})]}),(0,s.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:[(0,s.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,s.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"User Information"}),(0,s.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Personal details and account information."})]}),(0,s.jsx)("div",{className:"border-t border-gray-200",children:(0,s.jsxs)("dl",{children:[(0,s.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Full name"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:e?.name})]}),(0,s.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Email address"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:e?.email})]}),(0,s.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Email verification"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:n.uR.isEmailVerified()?(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Verified"}):(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"Not verified"})})]}),(0,s.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Account created"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:new Date(e?.created_at).toLocaleDateString()})]})]})})]})]})}},94735:e=>{"use strict";e.exports=require("events")},94937:(e,t,r)=>{Promise.resolve().then(r.bind(r,87032))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,318,658,905],()=>r(53936));module.exports=s})();