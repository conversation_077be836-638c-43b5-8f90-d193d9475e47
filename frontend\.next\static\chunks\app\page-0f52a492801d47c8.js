(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{650:(e,s,a)=>{"use strict";a.d(s,{default:()=>i});var r=a(5155),l=a(2115),t=a(9120),d=a(8258);let i=()=>{let[e,s]=(0,l.useState)(""),[a,i]=(0,l.useState)(""),[n,o]=(0,l.useState)(null),[c,m]=(0,l.useState)(!1),x=(0,t.Zp)(),u=async s=>{s.preventDefault();try{m(!0),o(null),await d.uR.login({email:e,password:a}),x("/")}catch(e){var r,l;console.error("Login error:",e),o((null==(l=e.response)||null==(r=l.data)?void 0:r.message)||"Invalid email or password")}finally{m(!1)}};return(0,r.jsxs)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,r.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,r.jsx)(t.N_,{to:"/register",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"create a new account"})]})]}),(0,r.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[n&&(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:n})})]})}),(0,r.jsxs)("form",{className:"space-y-6",onSubmit:u,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:a,onChange:e=>i(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:c,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ".concat(c?"opacity-70 cursor-not-allowed":""),children:c?"Signing in...":"Sign in"})})]})]})})]})}},1049:(e,s)=>{"use strict";Object.prototype.toString},6664:(e,s,a)=>{"use strict";a.d(s,{default:()=>o});var r=a(5155),l=a(2115),t=a(6874),d=a.n(t),i=a(8258),n=a(9768);let o=()=>{let[e,s]=(0,l.useState)([]),[a,t]=(0,l.useState)([]),[o,c]=(0,l.useState)(!0),[m,x]=(0,l.useState)(null);return((0,l.useEffect)(()=>{(async()=>{try{c(!0);let[e,a]=await Promise.all([i.bk.getFeaturedProducts(),i.M$.getAllCategories()]);s(e.data),t(a.data),x(null)}catch(e){console.error("Error fetching data:",e),x("Failed to load data. Please try again later.")}finally{c(!1)}})()},[]),o)?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):m?(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 my-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:m})})]})}):(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"bg-indigo-700 rounded-lg shadow-xl overflow-hidden",children:(0,r.jsx)("div",{className:"px-4 py-12 sm:px-6 lg:px-8 lg:py-16",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-extrabold text-white sm:text-4xl",children:(0,r.jsx)("span",{className:"block",children:"Welcome to EcommerceApp"})}),(0,r.jsx)("p",{className:"mt-4 text-lg leading-6 text-indigo-100",children:"Discover amazing products at great prices. Shop now and enjoy fast delivery!"}),(0,r.jsx)("div",{className:"mt-8 flex justify-center",children:(0,r.jsx)("div",{className:"inline-flex rounded-md shadow",children:(0,r.jsx)(d(),{href:"/products",className:"inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50",children:"Browse Products"})})})]})})}),(0,r.jsxs)("div",{className:"mt-12",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Featured Products"}),(0,r.jsx)(d(),{href:"/products",className:"text-indigo-600 hover:text-indigo-500",children:"View all"})]}),e.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4",children:e.map(e=>(0,r.jsx)(n.AA,{product:e},e.id))}):(0,r.jsx)("p",{className:"text-gray-500",children:"No featured products available."})]}),(0,r.jsxs)("div",{className:"mt-12",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Shop by Category"}),(0,r.jsx)(d(),{href:"/categories",className:"text-indigo-600 hover:text-indigo-500",children:"View all"})]}),a.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3",children:a.slice(0,3).map(e=>(0,r.jsx)(n.rm,{category:e},e.id))}):(0,r.jsx)("p",{className:"text-gray-500",children:"No categories available."})]})]})}},7455:(e,s,a)=>{Promise.resolve().then(a.bind(a,6664)),Promise.resolve().then(a.bind(a,650)),Promise.resolve().then(a.bind(a,9206))},9206:(e,s,a)=>{"use strict";a.d(s,{default:()=>i});var r=a(5155),l=a(2115),t=a(9120),d=a(8258);let i=()=>{let[e,s]=(0,l.useState)(""),[a,i]=(0,l.useState)(""),[n,o]=(0,l.useState)(""),[c,m]=(0,l.useState)(""),[x,u]=(0,l.useState)(null),[h,p]=(0,l.useState)({}),[g,f]=(0,l.useState)(!1),j=(0,t.Zp)(),v=async s=>{if(s.preventDefault(),n!==c)return void u("Passwords do not match");try{f(!0),u(null),p({}),await d.uR.register({name:e,email:a,password:n,password_confirmation:c}),j("/")}catch(e){var r,l,t,i;console.error("Registration error:",e),(null==(l=e.response)||null==(r=l.data)?void 0:r.errors)?p(e.response.data.errors):u((null==(i=e.response)||null==(t=i.data)?void 0:t.message)||"Registration failed. Please try again.")}finally{f(!1)}};return(0,r.jsxs)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create a new account"}),(0,r.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,r.jsx)(t.N_,{to:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"sign in to your existing account"})]})]}),(0,r.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[x&&(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:x})})]})}),(0,r.jsxs)("form",{className:"space-y-6",onSubmit:v,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Name"}),(0,r.jsxs)("div",{className:"mt-1",children:[(0,r.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,value:e,onChange:e=>s(e.target.value),className:"appearance-none block w-full px-3 py-2 border ".concat(h.name?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),h.name&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600",children:h.name[0]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,r.jsxs)("div",{className:"mt-1",children:[(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:a,onChange:e=>i(e.target.value),className:"appearance-none block w-full px-3 py-2 border ".concat(h.email?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),h.email&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600",children:h.email[0]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsxs)("div",{className:"mt-1",children:[(0,r.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:n,onChange:e=>o(e.target.value),className:"appearance-none block w-full px-3 py-2 border ".concat(h.password?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),h.password&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600",children:h.password[0]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password_confirmation",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"password_confirmation",name:"password_confirmation",type:"password",autoComplete:"new-password",required:!0,value:c,onChange:e=>m(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:g,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ".concat(g?"opacity-70 cursor-not-allowed":""),children:g?"Creating account...":"Create account"})})]})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[639,464,874,855,386,768,441,684,358],()=>s(7455)),_N_E=e.O()}]);