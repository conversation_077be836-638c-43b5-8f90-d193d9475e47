(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[279],{722:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let a=r(3464).A.create({baseURL:"",withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json",Accept:"application/json"}});a.interceptors.request.use(e=>{var t;console.log("Making request to: ".concat(null==(t=e.method)?void 0:t.toUpperCase()," ").concat(e.url));let r=localStorage.getItem("token");return r&&(e.headers.Authorization="Bearer ".concat(r)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&419===e.response.status&&console.error("CSRF token mismatch. Refreshing token..."),Promise.reject(e)));let s=a},3789:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(5155),s=r(2115),i=r(5855);let n={style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"}},invalid:{color:"#9e2146"}}},o=e=>{let{onSuccess:t,onError:r,loading:o,setLoading:d}=e,l=(0,i.useStripe)(),c=(0,i.useElements)(),[m,u]=(0,s.useState)(null),p=async e=>{if(e.preventDefault(),!l||!c)return;let a=c.getElement(i.CardElement);if(a){d(!0),u(null);try{let{error:e,paymentMethod:s}=await l.createPaymentMethod({type:"card",card:a});e?(u(e.message||"An error occurred during payment"),r(e.message||"An error occurred during payment")):s&&t(s.id)}catch(e){u("Payment failed. Please try again."),r("Payment failed. Please try again.")}finally{d(!1)}}};return(0,a.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Card Information"}),(0,a.jsx)("div",{className:"border border-gray-300 rounded-md p-3 bg-white",children:(0,a.jsx)(i.CardElement,{options:n,onChange:e=>{e.error?u(e.error.message):u(null)}})}),m&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m})]}),(0,a.jsx)("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"This is a test environment. Use test card number 4242 4242 4242 4242 with any future expiry date and any 3-digit CVC."})})]})}),(0,a.jsx)("button",{type:"submit",disabled:!l||o,className:"w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white ".concat(!l||o?"bg-gray-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700 cursor-pointer"),children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"}),"Processing Payment..."]}):"Pay Now"})]})}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},7300:(e,t,r)=>{Promise.resolve().then(r.bind(r,7375))},7375:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var a,s=r(5155),i=r(2115),n=r(5695),o=r(6874),d=r.n(o),l=r(8258),c="https://js.stripe.com/v3",m=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,u=function(){for(var e=document.querySelectorAll('script[src^="'.concat(c,'"]')),t=0;t<e.length;t++){var r=e[t];if(m.test(r.src))return r}return null},p=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",r=document.createElement("script");r.src="".concat(c).concat(t);var a=document.head||document.body;if(!a)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return a.appendChild(r),r},h=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"2.4.0",startTime:t})},g=null,x=null,y=null,f=function(e,t,r){if(null===e)return null;var a=e.apply(void 0,t);return h(a,r),a},v=!1,b=function(){return a?a:a=(null!==g?g:(g=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document)return void e(null);if(window.Stripe,window.Stripe)return void e(window.Stripe);try{var r,a=u();a?a&&null!==y&&null!==x&&(a.removeEventListener("load",y),a.removeEventListener("error",x),null==(r=a.parentNode)||r.removeChild(a),a=p(null)):a=p(null),y=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},x=function(){t(Error("Failed to load Stripe.js"))},a.addEventListener("load",y),a.addEventListener("error",x)}catch(e){t(e);return}})).catch(function(e){return g=null,Promise.reject(e)})).catch(function(e){return a=null,Promise.reject(e)})};Promise.resolve().then(function(){return b()}).catch(function(e){v||console.warn(e)});var w=r(5855);let j=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];v=!0;var a=Date.now();return b().then(function(e){return f(e,t,a)})}("pk_test_51RPMtI2LG4ckcbPFWzTKshhLh9J7PhDyJLTd7msI8zt2DYQllIZapYimPQciaMpOpVsMH1zXoNujyP8EJlMDLFTY00T67YmkEy"),N=e=>{let{children:t,clientSecret:r}=e;return(0,s.jsx)(w.Elements,{stripe:j,options:r?{clientSecret:r,appearance:{theme:"stripe"}}:void 0,children:t})};var S=r(3789);function C(){let e=(0,n.useRouter)(),[t,r]=(0,i.useState)({items:[],total:0}),[a,o]=(0,i.useState)(!0),[c,m]=(0,i.useState)(!1),[u,p]=(0,i.useState)(null),[h,g]=(0,i.useState)(""),[x,y]=(0,i.useState)(""),[f,v]=(0,i.useState)(""),[b,w]=(0,i.useState)(""),[j,C]=(0,i.useState)(""),[P,k]=(0,i.useState)(""),[E,A]=(0,i.useState)(""),[_,I]=(0,i.useState)("credit_card"),[q,F]=(0,i.useState)(""),[O,R]=(0,i.useState)({});(0,i.useEffect)(()=>{if(!l.uR.isAuthenticated())return void e.push("/login?redirect=/checkout");let t=l.mx.getCart();if(0===t.items.length)return void e.push("/cart");r(t),o(!1)},[e]);let L=()=>{let e={};return x.trim()||(e.shippingAddress="Shipping address is required"),f.trim()||(e.shippingCity="City is required"),b.trim()||(e.shippingState="State is required"),j.trim()||(e.shippingCountry="Country is required"),P.trim()||(e.shippingZipCode="ZIP code is required"),E.trim()||(e.shippingPhone="Phone number is required"),R(e),0===Object.keys(e).length},z=e=>{I(e)},M=async e=>{if(g(e),L())try{m(!0),p(null);let r=t.items.map(e=>({product_id:e.product_id,quantity:e.quantity})),a=await l.d$.processPayment(e,r);a.success?await J(a.payment_intent):p(a.message||"Payment failed")}catch(e){var r,a;console.error("Error processing payment:",e),p(e instanceof Error&&"response"in e?null==(a=e.response)||null==(r=a.data)?void 0:r.message:"Payment failed. Please try again.")}finally{m(!1)}},J=async r=>{try{let a={shipping_address:x,shipping_city:f,shipping_state:b,shipping_country:j,shipping_zip_code:P,shipping_phone:E,payment_method:_,notes:q||void 0,items:t.items.map(e=>({product_id:e.product_id,quantity:e.quantity})),...r&&{stripe_payment_id:r}},s=await l.QE.createOrder(a);l.mx.clearCart(),window.dispatchEvent(new Event("storage")),e.push("/order-confirmation/".concat(s.id))}catch(e){var a,s;throw console.error("Error creating order:",e),p(e instanceof Error&&"response"in e?null==(s=e.response)||null==(a=s.data)?void 0:a.message:"Failed to create order. Please try again."),e}},T=async e=>{if((e.preventDefault(),L())&&"credit_card"!==_)try{m(!0),await J()}catch(e){}finally{m(!1)}};return a?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Checkout"}),u&&(0,s.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm text-red-700",children:u})})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"md:col-span-2",children:(0,s.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:(0,s.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Shipping Information"}),(0,s.jsxs)("form",{onSubmit:T,children:[(0,s.jsxs)("div",{className:"grid grid-cols-6 gap-6",children:[(0,s.jsxs)("div",{className:"col-span-6",children:[(0,s.jsx)("label",{htmlFor:"shipping-address",className:"block text-sm font-medium text-gray-700",children:"Address"}),(0,s.jsx)("input",{type:"text",id:"shipping-address",value:x,onChange:e=>y(e.target.value),className:"mt-1 block w-full border ".concat(O.shippingAddress?"border-red-300":"border-gray-300"," rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),O.shippingAddress&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:O.shippingAddress})]}),(0,s.jsxs)("div",{className:"col-span-6 sm:col-span-3",children:[(0,s.jsx)("label",{htmlFor:"shipping-city",className:"block text-sm font-medium text-gray-700",children:"City"}),(0,s.jsx)("input",{type:"text",id:"shipping-city",value:f,onChange:e=>v(e.target.value),className:"mt-1 block w-full border ".concat(O.shippingCity?"border-red-300":"border-gray-300"," rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),O.shippingCity&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:O.shippingCity})]}),(0,s.jsxs)("div",{className:"col-span-6 sm:col-span-3",children:[(0,s.jsx)("label",{htmlFor:"shipping-state",className:"block text-sm font-medium text-gray-700",children:"State / Province"}),(0,s.jsx)("input",{type:"text",id:"shipping-state",value:b,onChange:e=>w(e.target.value),className:"mt-1 block w-full border ".concat(O.shippingState?"border-red-300":"border-gray-300"," rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),O.shippingState&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:O.shippingState})]}),(0,s.jsxs)("div",{className:"col-span-6 sm:col-span-3",children:[(0,s.jsx)("label",{htmlFor:"shipping-country",className:"block text-sm font-medium text-gray-700",children:"Country"}),(0,s.jsx)("input",{type:"text",id:"shipping-country",value:j,onChange:e=>C(e.target.value),className:"mt-1 block w-full border ".concat(O.shippingCountry?"border-red-300":"border-gray-300"," rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),O.shippingCountry&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:O.shippingCountry})]}),(0,s.jsxs)("div",{className:"col-span-6 sm:col-span-3",children:[(0,s.jsx)("label",{htmlFor:"shipping-zip",className:"block text-sm font-medium text-gray-700",children:"ZIP / Postal Code"}),(0,s.jsx)("input",{type:"text",id:"shipping-zip",value:P,onChange:e=>k(e.target.value),className:"mt-1 block w-full border ".concat(O.shippingZipCode?"border-red-300":"border-gray-300"," rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),O.shippingZipCode&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:O.shippingZipCode})]}),(0,s.jsxs)("div",{className:"col-span-6",children:[(0,s.jsx)("label",{htmlFor:"shipping-phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,s.jsx)("input",{type:"text",id:"shipping-phone",value:E,onChange:e=>A(e.target.value),className:"mt-1 block w-full border ".concat(O.shippingPhone?"border-red-300":"border-gray-300"," rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),O.shippingPhone&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:O.shippingPhone})]})]}),(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Payment Method"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"payment-credit-card",name:"payment-method",type:"radio",checked:"credit_card"===_,onChange:()=>z("credit_card"),className:"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"}),(0,s.jsx)("label",{htmlFor:"payment-credit-card",className:"ml-3 block text-sm font-medium text-gray-700",children:"Credit Card"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"payment-paypal",name:"payment-method",type:"radio",checked:"paypal"===_,onChange:()=>z("paypal"),className:"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"}),(0,s.jsx)("label",{htmlFor:"payment-paypal",className:"ml-3 block text-sm font-medium text-gray-700",children:"PayPal"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"payment-cash",name:"payment-method",type:"radio",checked:"cash"===_,onChange:()=>z("cash"),className:"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"}),(0,s.jsx)("label",{htmlFor:"payment-cash",className:"ml-3 block text-sm font-medium text-gray-700",children:"Cash on Delivery"})]})]})]}),"credit_card"===_&&(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Payment Information"}),(0,s.jsx)(N,{children:(0,s.jsx)(S.A,{onSuccess:M,onError:e=>{p(e)},loading:c,setLoading:m})})]}),(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("label",{htmlFor:"notes",className:"block text-sm font-medium text-gray-700",children:"Order Notes (Optional)"}),(0,s.jsx)("textarea",{id:"notes",rows:3,value:q,onChange:e=>F(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Special instructions for delivery"})]}),"credit_card"!==_&&(0,s.jsxs)("div",{className:"mt-8 flex justify-end",children:[(0,s.jsx)(d(),{href:"/cart",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 mr-4",children:"Back to Cart"}),(0,s.jsx)("button",{type:"submit",disabled:c,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer",children:c?"Processing...":"Place Order"})]}),"credit_card"===_&&(0,s.jsx)("div",{className:"mt-8 flex justify-start",children:(0,s.jsx)(d(),{href:"/cart",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Back to Cart"})})]})]})})}),(0,s.jsx)("div",{className:"md:col-span-1",children:(0,s.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:(0,s.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Order Summary"}),(0,s.jsx)("div",{className:"mt-4 border-t border-gray-200 pt-4",children:(0,s.jsx)("div",{className:"flow-root",children:(0,s.jsx)("ul",{className:"-my-4 divide-y divide-gray-200",children:t.items.map(e=>(0,s.jsxs)("li",{className:"py-4 flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-16 h-16 border border-gray-200 rounded-md overflow-hidden",children:e.product.image?(0,s.jsx)("img",{src:e.product.image,alt:e.product.name,className:"w-full h-full object-center object-cover"}):(0,s.jsx)("div",{className:"w-full h-full bg-gray-200 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-gray-400 text-xs",children:"No image"})})}),(0,s.jsxs)("div",{className:"ml-4 flex-1 flex flex-col",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[(0,s.jsx)("h3",{children:e.product.name}),(0,s.jsxs)("p",{className:"ml-4",children:["$",(e.product.price*e.quantity).toFixed(2)]})]})}),(0,s.jsx)("div",{className:"flex-1 flex items-end justify-between text-sm",children:(0,s.jsxs)("p",{className:"text-gray-500",children:["Qty ",e.quantity]})})]})]},e.product_id))})})}),(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:[(0,s.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[(0,s.jsx)("p",{children:"Subtotal"}),(0,s.jsxs)("p",{children:["$",t.total.toFixed(2)]})]}),(0,s.jsx)("p",{className:"mt-0.5 text-sm text-gray-500",children:"Shipping and taxes calculated at checkout."})]}),(0,s.jsx)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:(0,s.jsxs)("div",{className:"flex justify-between text-lg font-bold text-gray-900",children:[(0,s.jsx)("p",{children:"Total"}),(0,s.jsxs)("p",{children:["$",t.total.toFixed(2)]})]})})]})})})]})]})}},8258:(e,t,r)=>{"use strict";r.d(t,{uR:()=>i,mx:()=>m,M$:()=>o,QE:()=>d,bk:()=>n,d$:()=>u});var a=r(722);let s={login:async e=>{try{await a.A.get("/sanctum/csrf-cookie");let t=await a.A.post("/api/login",e);if(t.data.success&&t.data.data){let{user:e,token:r}=t.data.data;return localStorage.setItem("token",r),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Login error details:",e),e}},register:async e=>{try{await a.A.get("/sanctum/csrf-cookie");let t=await a.A.post("/api/register",e);if(t.data.success&&t.data.data){let{user:e,token:r}=t.data.data;return localStorage.setItem("token",r),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Registration error details:",e),e}},logout:async()=>{try{await a.A.get("/sanctum/csrf-cookie"),await a.A.post("/api/logout"),localStorage.removeItem("token"),localStorage.removeItem("user")}catch(e){console.error("Logout error:",e),localStorage.removeItem("token"),localStorage.removeItem("user")}},getCurrentUser:()=>{let e=localStorage.getItem("user");return e?JSON.parse(e):null},isAuthenticated:()=>!!localStorage.getItem("token"),isEmailVerified:()=>{let e=localStorage.getItem("user");return!!e&&!!JSON.parse(e).email_verified_at},resendVerificationEmail:async()=>{try{if(!s.isAuthenticated())throw Error("You must be logged in to resend verification email");console.log("Attempting to resend verification email...");let e=await a.A.get("/sanctum/csrf-cookie");console.log("CSRF cookie response:",e.status),console.log("Sending verification email request...");let t=await a.A.post("/api/email/verification-notification");return console.log("Verification email response:",t.status,t.data),t.data}catch(e){throw console.error("Resend verification email error:",e),e}},refreshUserData:async()=>{try{if(!s.isAuthenticated())return null;let e=await a.A.get("/api/user");if(console.log("Refresh user data response:",e.data),e.data&&e.data.success&&e.data.data&&e.data.data.user){let t=e.data.data.user;return localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),t}return null}catch(e){return console.error("Refresh user data error:",e),null}}},i=s,n={fetchProducts:async()=>{try{return(await a.A.get("/products")).data}catch(e){throw e}},getFeaturedProducts:async()=>{try{return(await a.A.get("/products?featured=true")).data}catch(e){throw e}},getProductBySlug:async e=>{try{let t=(await a.A.get("/products")).data.data.find(t=>t.slug===e);if(!t)throw Error("Product not found");return t}catch(e){throw e}},getAllProducts:async()=>{try{return(await a.A.get("/products")).data.data}catch(e){throw e}}},o={getAllCategories:async()=>{try{return(await a.A.get("/categories")).data.data}catch(e){throw e}},getCategoryBySlug:async e=>{try{let t=(await a.A.get("/categories")).data.data.find(t=>t.slug===e);if(!t)throw Error("Category not found");return t}catch(e){throw e}}},d={getAllOrders:async()=>{try{return(await a.A.get("/orders")).data.data}catch(e){throw e}},getOrderById:async e=>{try{return(await a.A.get("/orders/".concat(e))).data.data}catch(e){throw e}},createOrder:async e=>{try{return(await a.A.post("/orders",e)).data.data}catch(e){throw e}},updateOrder:async(e,t)=>{try{return(await a.A.put("/orders/".concat(e),{notes:t})).data.data}catch(e){throw e}},cancelOrder:async e=>{try{return(await a.A.post("/orders/".concat(e,"/cancel"))).data.data}catch(e){throw e}}},l="ecommerce_cart",c={getCart:()=>{let e=localStorage.getItem(l);return e?JSON.parse(e):{items:[],total:0}},saveCart:e=>{localStorage.setItem(l,JSON.stringify(e))},addToCart:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=c.getCart(),a=r.items.findIndex(t=>t.product_id===e.id);return -1!==a?r.items[a].quantity+=t:r.items.push({product_id:e.id,quantity:t,product:e}),r.total=r.items.reduce((e,t)=>e+t.product.price*t.quantity,0),c.saveCart(r),r},updateQuantity:(e,t)=>{let r=c.getCart(),a=r.items.findIndex(t=>t.product_id===e);return -1!==a&&(t<=0?r.items.splice(a,1):r.items[a].quantity=t,r.total=r.items.reduce((e,t)=>e+t.product.price*t.quantity,0),c.saveCart(r)),r},removeFromCart:e=>{let t=c.getCart(),r=t.items.findIndex(t=>t.product_id===e);return -1!==r&&(t.items.splice(r,1),t.total=t.items.reduce((e,t)=>e+t.product.price*t.quantity,0),c.saveCart(t)),t},clearCart:()=>{let e={items:[],total:0};return c.saveCart(e),e},getItemCount:()=>c.getCart().items.reduce((e,t)=>e+t.quantity,0)},m=c,u={createPaymentIntent:async e=>{try{return(await a.A.post("/payment/create-intent",e)).data}catch(e){throw e}},processPayment:async(e,t)=>{try{return(await a.A.post("/payment/process",{payment_method_id:e,items:t})).data}catch(e){throw e}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,855,441,684,358],()=>t(7300)),_N_E=e.O()}]);