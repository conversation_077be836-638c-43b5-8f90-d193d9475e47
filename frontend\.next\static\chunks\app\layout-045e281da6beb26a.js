(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1541:(e,r,t)=>{"use strict";t.d(r,{eq:()=>n,nO:()=>s});var a=t(8258);let s=async()=>{try{console.log("Refreshing user data from backend...");let e=await a.uR.refreshUserData();return console.log("User data refreshed successfully:",e),e}catch(e){return console.error("Failed to refresh user data:",e),a.uR.getCurrentUser()}},n=e=>(window.addEventListener("storage",e),window.addEventListener("storage-update",e),()=>{window.removeEventListener("storage",e),window.removeEventListener("storage-update",e)})},2093:e=>{e.exports={style:{fontFamily:"'Geist', '<PERSON>eist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},2844:(e,r,t)=>{"use strict";t.d(r,{default:()=>u});var a=t(5155),s=t(2115),n=t(6874),o=t.n(n);let l=s.forwardRef(function(e,r){let{title:t,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},n),t?s.createElement("title",{id:a},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"}))}),i=s.forwardRef(function(e,r){let{title:t,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},n),t?s.createElement("title",{id:a},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}),d=s.forwardRef(function(e,r){let{title:t,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},n),t?s.createElement("title",{id:a},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),c=s.forwardRef(function(e,r){let{title:t,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},n),t?s.createElement("title",{id:a},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});var m=t(8258),h=t(829),x=t(3265),g=t(1541);function u(e){let{children:r}=e,[t,n]=(0,s.useState)(!1),[u,b]=(0,s.useState)(!1),[p,f]=(0,s.useState)(0),[y,v]=(0,s.useState)(!1),[w,j]=(0,s.useState)(null),N=()=>{let e=m.uR.isAuthenticated();if(b(e),e){let e=m.uR.getCurrentUser();j(e),e&&!m.uR.isEmailVerified()?v(!0):v(!1)}else j(null),v(!1);f(m.mx.getItemCount())},k=async()=>{try{m.uR.isAuthenticated()&&(console.log("Refreshing user data from backend..."),await (0,g.nO)(),N())}catch(e){console.error("Failed to refresh user data:",e),N()}};(0,s.useEffect)(()=>{k();let e=setInterval(()=>{m.uR.isAuthenticated()&&k()},6e4),r=(0,g.eq)(()=>{N()});return()=>{r(),clearInterval(e)}},[]);let E=async()=>{await m.uR.logout(),b(!1),window.location.href="/login"};return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(h.A,{}),(0,a.jsxs)("header",{className:"bg-white shadow",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)(o(),{href:"/",className:"text-2xl font-bold text-indigo-600",children:"EcommerceApp"})}),(0,a.jsxs)("nav",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[(0,a.jsx)(o(),{href:"/",className:"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Home"}),(0,a.jsx)(o(),{href:"/products",className:"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Products"}),(0,a.jsx)(o(),{href:"/categories",className:"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Categories"})]})]}),(0,a.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4",children:[(0,a.jsxs)(o(),{href:"/cart",className:"relative p-1 rounded-full text-gray-400 hover:text-gray-500",children:[(0,a.jsx)(l,{className:"h-6 w-6","aria-hidden":"true"}),p>0&&(0,a.jsx)("span",{className:"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full",children:p})]}),u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(o(),{href:"/profile",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50",children:[(0,a.jsx)(i,{className:"h-5 w-5 mr-2","aria-hidden":"true"}),"My Account"]}),(0,a.jsx)("button",{onClick:E,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 cursor-pointer",children:"Logout"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/login",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50",children:"Login"}),(0,a.jsx)(o(),{href:"/register",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Register"})]})]}),(0,a.jsx)("div",{className:"-mr-2 flex items-center sm:hidden",children:(0,a.jsxs)("button",{onClick:()=>n(!t),className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 cursor-pointer",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open main menu"}),t?(0,a.jsx)(d,{className:"block h-6 w-6","aria-hidden":"true"}):(0,a.jsx)(c,{className:"block h-6 w-6","aria-hidden":"true"})]})})]})}),t&&(0,a.jsx)("div",{className:"sm:hidden",children:(0,a.jsxs)("div",{className:"pt-2 pb-3 space-y-1",children:[(0,a.jsx)(o(),{href:"/",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>n(!1),children:"Home"}),(0,a.jsx)(o(),{href:"/products",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>n(!1),children:"Products"}),(0,a.jsx)(o(),{href:"/categories",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>n(!1),children:"Categories"}),(0,a.jsxs)(o(),{href:"/cart",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>n(!1),children:["Cart (",p,")"]}),u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/profile",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>n(!1),children:"My Account"}),(0,a.jsx)("button",{onClick:()=>{E(),n(!1)},className:"block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 cursor-pointer",children:"Logout"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/login",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>n(!1),children:"Login"}),(0,a.jsx)(o(),{href:"/register",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>n(!1),children:"Register"})]})]})})]}),(0,a.jsx)("main",{className:"flex-grow",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[y&&w&&(0,a.jsx)(x.A,{email:w.email,showDismiss:!0,onDismiss:()=>v(!1)}),r]})}),(0,a.jsx)("footer",{className:"bg-white",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("p",{className:"text-center text-gray-500 text-sm",children:["\xa9 ",new Date().getFullYear()," EcommerceApp. All rights reserved."]})})})]})}},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9968:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2093,23)),Promise.resolve().then(t.t.bind(t,7735,23)),Promise.resolve().then(t.bind(t,2844)),Promise.resolve().then(t.t.bind(t,347,23))}},e=>{var r=r=>e(e.s=r);e.O(0,[360,464,874,386,441,684,358],()=>r(9968)),_N_E=e.O()}]);