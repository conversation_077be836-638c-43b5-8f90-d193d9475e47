(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[316],{279:(e,t,a)=>{Promise.resolve().then(a.bind(a,3294))},722:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(3464).A.create({baseURL:"",withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json",Accept:"application/json"}});r.interceptors.request.use(e=>{var t;console.log("Making request to: ".concat(null==(t=e.method)?void 0:t.toUpperCase()," ").concat(e.url));let a=localStorage.getItem("token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&419===e.response.status&&console.error("CSRF token mismatch. Refreshing token..."),Promise.reject(e)));let s=r},3294:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(5155),s=a(2115),o=a(5695),i=a(6874),n=a.n(i),c=a(8258);let l=()=>{let e=(0,o.useSearchParams)();(0,o.useRouter)();let[t,a]=(0,s.useState)(5),[i,l]=(0,s.useState)(!1),d=(0,s.useRef)(!1),u="true"===e.get("success"),m="true"===e.get("already");return(0,s.useEffect)(()=>{let e=async()=>{if(u||m){l(!0),console.log("Email verification successful, refreshing user data..."),await new Promise(e=>setTimeout(e,500));try{let t=3,a=null;for(;t>0&&!a;)try{a=await e(),console.log("User data refreshed successfully:",a);break}catch(e){if(--t>0)console.log("Retrying user data refresh... (".concat(t," attempts left)")),await new Promise(e=>setTimeout(e,1e3));else throw e}}catch(e){if(console.error("Failed to refresh user data:",e),u){let e=c.uR.getCurrentUser();if(e){console.log("Using fallback method to update verification status");let t={...e,email_verified_at:new Date().toISOString()};localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update"))}}}finally{l(!1)}}};e()},[u,m]),(0,s.useEffect)(()=>{if(!u&&!m)return;let e=setInterval(()=>{a(t=>t<=1?(clearInterval(e),d.current||(d.current=!0,window.location.href="/"),0):t-1)},1e3);return()=>clearInterval(e)},[u,m]),(0,r.jsxs)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Email Verification"})}),(0,r.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[u?(0,r.jsx)("div",{className:"bg-green-50 border-l-4 border-green-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-green-700",children:"Your email has been verified successfully!"})})]})}):m?(0,r.jsx)("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-blue-700",children:"Your email has already been verified."})})]})}):(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:"Invalid verification link. Please request a new verification link."})})]})}),i?(0,r.jsxs)("div",{className:"flex justify-center items-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-500 mr-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Updating your account information..."})]}):(0,r.jsxs)("p",{className:"text-center text-sm text-gray-600",children:["You will be redirected to the home page in ",t," seconds."]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(n(),{href:"/",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Go to Home Page"})})]})})]})},d=()=>(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,r.jsxs)("div",{className:"flex justify-center items-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-500 mr-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Loading..."})]})})})}),children:(0,r.jsx)(l,{})})},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},8258:(e,t,a)=>{"use strict";a.d(t,{uR:()=>o,mx:()=>u,M$:()=>n,QE:()=>c,bk:()=>i,d$:()=>m});var r=a(722);let s={login:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/login",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Login error details:",e),e}},register:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/register",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Registration error details:",e),e}},logout:async()=>{try{await r.A.get("/sanctum/csrf-cookie"),await r.A.post("/api/logout"),localStorage.removeItem("token"),localStorage.removeItem("user")}catch(e){console.error("Logout error:",e),localStorage.removeItem("token"),localStorage.removeItem("user")}},getCurrentUser:()=>{let e=localStorage.getItem("user");return e?JSON.parse(e):null},isAuthenticated:()=>!!localStorage.getItem("token"),isEmailVerified:()=>{let e=localStorage.getItem("user");return!!e&&!!JSON.parse(e).email_verified_at},resendVerificationEmail:async()=>{try{if(!s.isAuthenticated())throw Error("You must be logged in to resend verification email");console.log("Attempting to resend verification email...");let e=await r.A.get("/sanctum/csrf-cookie");console.log("CSRF cookie response:",e.status),console.log("Sending verification email request...");let t=await r.A.post("/api/email/verification-notification");return console.log("Verification email response:",t.status,t.data),t.data}catch(e){throw console.error("Resend verification email error:",e),e}},refreshUserData:async()=>{try{if(!s.isAuthenticated())return null;let e=await r.A.get("/api/user");if(console.log("Refresh user data response:",e.data),e.data&&e.data.success&&e.data.data&&e.data.data.user){let t=e.data.data.user;return localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),t}return null}catch(e){return console.error("Refresh user data error:",e),null}}},o=s,i={fetchProducts:async()=>{try{return(await r.A.get("/products")).data}catch(e){throw e}},getFeaturedProducts:async()=>{try{return(await r.A.get("/products?featured=true")).data}catch(e){throw e}},getProductBySlug:async e=>{try{let t=(await r.A.get("/products")).data.data.find(t=>t.slug===e);if(!t)throw Error("Product not found");return t}catch(e){throw e}},getAllProducts:async()=>{try{return(await r.A.get("/products")).data.data}catch(e){throw e}}},n={getAllCategories:async()=>{try{return(await r.A.get("/categories")).data.data}catch(e){throw e}},getCategoryBySlug:async e=>{try{let t=(await r.A.get("/categories")).data.data.find(t=>t.slug===e);if(!t)throw Error("Category not found");return t}catch(e){throw e}}},c={getAllOrders:async()=>{try{return(await r.A.get("/orders")).data.data}catch(e){throw e}},getOrderById:async e=>{try{return(await r.A.get("/orders/".concat(e))).data.data}catch(e){throw e}},createOrder:async e=>{try{return(await r.A.post("/orders",e)).data.data}catch(e){throw e}},updateOrder:async(e,t)=>{try{return(await r.A.put("/orders/".concat(e),{notes:t})).data.data}catch(e){throw e}},cancelOrder:async e=>{try{return(await r.A.post("/orders/".concat(e,"/cancel"))).data.data}catch(e){throw e}}},l="ecommerce_cart",d={getCart:()=>{let e=localStorage.getItem(l);return e?JSON.parse(e):{items:[],total:0}},saveCart:e=>{localStorage.setItem(l,JSON.stringify(e))},addToCart:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=d.getCart(),r=a.items.findIndex(t=>t.product_id===e.id);return -1!==r?a.items[r].quantity+=t:a.items.push({product_id:e.id,quantity:t,product:e}),a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(a),a},updateQuantity:(e,t)=>{let a=d.getCart(),r=a.items.findIndex(t=>t.product_id===e);return -1!==r&&(t<=0?a.items.splice(r,1):a.items[r].quantity=t,a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(a)),a},removeFromCart:e=>{let t=d.getCart(),a=t.items.findIndex(t=>t.product_id===e);return -1!==a&&(t.items.splice(a,1),t.total=t.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(t)),t},clearCart:()=>{let e={items:[],total:0};return d.saveCart(e),e},getItemCount:()=>d.getCart().items.reduce((e,t)=>e+t.quantity,0)},u=d,m={createPaymentIntent:async e=>{try{return(await r.A.post("/payment/create-intent",e)).data}catch(e){throw e}},processPayment:async(e,t)=>{try{return(await r.A.post("/payment/process",{payment_method_id:e,items:t})).data}catch(e){throw e}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,441,684,358],()=>t(279)),_N_E=e.O()}]);