"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[639],{9120:(e,t,r)=>{r.d(t,{N_:()=>rs,Zp:()=>to});var n,a,o=r(2115);r(1049);var i=r(1890),l=e=>{throw TypeError(e)},s=(e,t,r)=>t.has(e)||l("Cannot "+r),u=(e,t,r)=>(s(e,t,"read from private field"),r?r.call(e):t.get(e)),d=(e,t,r)=>t.has(e)?l("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),c=(e=>(e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE",e))(c||{}),h="popstate";function f(e,t){if(!1===e||null==e)throw Error(t)}function m(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw Error(t)}catch(e){}}}function p(e,t){return{usr:e.state,key:e.key,idx:t}}function y(e,t,r=null,n){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?g(t):t,state:r,key:t&&t.key||n||Math.random().toString(36).substring(2,10)}}function v({pathname:e="/",search:t="",hash:r=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),r&&"#"!==r&&(e+="#"===r.charAt(0)?r:"#"+r),e}function g(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function w(e,t,r,n={}){let{window:a=document.defaultView,v5Compat:o=!1}=n,i=a.history,l="POP",s=null,u=d();function d(){return(i.state||{idx:null}).idx}function c(){l="POP";let e=d(),t=null==e?null:e-u;u=e,s&&s({action:l,location:g.location,delta:t})}function m(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,r="string"==typeof e?e:v(e);return r=r.replace(/ $/,"%20"),f(t,`No window.location.(origin|href) available to create URL for href: ${r}`),new URL(r,t)}null==u&&(u=0,i.replaceState({...i.state,idx:u},""));let g={get action(){return l},get location(){return e(a,i)},listen(e){if(s)throw Error("A history only accepts one active listener");return a.addEventListener(h,c),s=e,()=>{a.removeEventListener(h,c),s=null}},createHref:e=>t(a,e),createURL:m,encodeLocation(e){let t=m(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){l="PUSH";let n=y(g.location,e,t);r&&r(n,e);let c=p(n,u=d()+1),h=g.createHref(n);try{i.pushState(c,"",h)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;a.location.assign(h)}o&&s&&s({action:l,location:g.location,delta:1})},replace:function(e,t){l="REPLACE";let n=y(g.location,e,t);r&&r(n,e);let a=p(n,u=d()),c=g.createHref(n);i.replaceState(a,"",c),o&&s&&s({action:l,location:g.location,delta:0})},go:e=>i.go(e)};return g}var b=class{constructor(e){if(d(this,n,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(u(this,n).has(e))return u(this,n).get(e);if(void 0!==e.defaultValue)return e.defaultValue;throw Error("No value found for context")}set(e,t){u(this,n).set(e,t)}};n=new WeakMap;var R=new Set(["lazy","caseSensitive","path","id","index","children"]),E=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function x(e,t,r=[],n={}){return e.map((e,a)=>{let o=[...r,String(a)],i="string"==typeof e.id?e.id:o.join("-");if(f(!0!==e.index||!e.children,"Cannot specify children on an index route"),f(!n[i],`Found a route id collision on id "${i}".  Route id's must be globally unique within Data Router usages`),!0===e.index){let r={...e,...t(e),id:i};return n[i]=r,r}{let r={...e,...t(e),id:i,children:void 0};return n[i]=r,e.children&&(r.children=x(e.children,t,o,n)),r}})}function C(e,t,r="/"){return S(e,t,r,!1)}function S(e,t,r,n){let a=T(("string"==typeof t?g(t):t).pathname||"/",r);if(null==a)return null;let o=function e(t,r=[],n=[],a=""){let o=(t,o,i)=>{var l,s;let u,d,c={relativePath:void 0===i?t.path||"":i,caseSensitive:!0===t.caseSensitive,childrenIndex:o,route:t};c.relativePath.startsWith("/")&&(f(c.relativePath.startsWith(a),`Absolute route path "${c.relativePath}" nested under path "${a}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),c.relativePath=c.relativePath.slice(a.length));let h=A([a,c.relativePath]),m=n.concat(c);t.children&&t.children.length>0&&(f(!0!==t.index,`Index routes must not have child routes. Please remove all child routes from route path "${h}".`),e(t.children,r,m,h)),(null!=t.path||t.index)&&r.push({path:h,score:(l=h,s=t.index,d=(u=l.split("/")).length,u.some(L)&&(d+=-2),s&&(d+=2),u.filter(e=>!L(e)).reduce((e,t)=>e+(P.test(t)?3:""===t?1:10),d)),routesMeta:m})};return t.forEach((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let r of function e(t){let r=t.split("/");if(0===r.length)return[];let[n,...a]=r,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===a.length)return o?[i,""]:[i];let l=e(a.join("/")),s=[];return s.push(...l.map(e=>""===e?i:[i,e].join("/"))),o&&s.push(...l),s.map(e=>t.startsWith("/")&&""===e?"/":e)}(e.path))o(e,t,r);else o(e,t)}),r}(e);o.sort((e,t)=>{var r,n;return e.score!==t.score?t.score-e.score:(r=e.routesMeta.map(e=>e.childrenIndex),n=t.routesMeta.map(e=>e.childrenIndex),r.length===n.length&&r.slice(0,-1).every((e,t)=>e===n[t])?r[r.length-1]-n[n.length-1]:0)});let i=null;for(let e=0;null==i&&e<o.length;++e){let t=function(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return m(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}(a);i=function(e,t,r=!1){let{routesMeta:n}=e,a={},o="/",i=[];for(let e=0;e<n.length;++e){let l=n[e],s=e===n.length-1,u="/"===o?t:t.slice(o.length)||"/",d=_({path:l.relativePath,caseSensitive:l.caseSensitive,end:s},u),c=l.route;if(!d&&s&&r&&!n[n.length-1].route.index&&(d=_({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},u)),!d)return null;Object.assign(a,d.params),i.push({params:a,pathname:A([o,d.pathname]),pathnameBase:U(A([o,d.pathnameBase])),route:c}),"/"!==d.pathnameBase&&(o=A([o,d.pathnameBase]))}return i}(o[e],t,n)}return i}function $(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}var P=/^:[\w-]+$/,L=e=>"*"===e;function _(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=k(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:n.reduce((e,{paramName:t,isOptional:r},n)=>{if("*"===t){let e=l[n]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}let a=l[n];return r&&!a?e[t]=void 0:e[t]=(a||"").replace(/%2F/g,"/"),e},{}),pathname:o,pathnameBase:i,pattern:e}}function k(e,t=!1,r=!0){m("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,r)=>(n.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function T(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}function j(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function O(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function M(e){let t=O(e);return t.map((e,r)=>r===t.length-1?e.pathname:e.pathnameBase)}function N(e,t,r,n=!1){let a,o;"string"==typeof e?a=g(e):(f(!(a={...e}).pathname||!a.pathname.includes("?"),j("?","pathname","search",a)),f(!a.pathname||!a.pathname.includes("#"),j("#","pathname","hash",a)),f(!a.search||!a.search.includes("#"),j("#","search","hash",a)));let i=""===e||""===a.pathname,l=i?"/":a.pathname;if(null==l)o=r;else{let e=t.length-1;if(!n&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t="/"){var r;let n,{pathname:a,search:o="",hash:i=""}="string"==typeof e?g(e):e;return{pathname:a?a.startsWith("/")?a:(r=a,n=t.replace(/\/+$/,"").split("/"),r.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"):t,search:D(o),hash:z(i)}}(a,o),u=l&&"/"!==l&&l.endsWith("/"),d=(i||"."===l)&&r.endsWith("/");return!s.pathname.endsWith("/")&&(u||d)&&(s.pathname+="/"),s}var A=e=>e.join("/").replace(/\/\/+/g,"/"),U=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),D=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",z=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"",H=class{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}},F=(e,t=302)=>{let r=t;"number"==typeof r?r={status:r}:void 0===r.status&&(r.status=302);let n=new Headers(r.headers);return n.set("Location",e),new Response(null,{...r,headers:n})},I=(e,t)=>{let r=F(e,t);return r.headers.set("X-Remix-Reload-Document","true"),r},W=(e,t)=>{let r=F(e,t);return r.headers.set("X-Remix-Replace","true"),r},B=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function q(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}var J=["POST","PUT","PATCH","DELETE"],Y=new Set(J),X=new Set(["GET",...J]),V=new Set([301,302,303,307,308]),K=new Set([307,308]),G={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Z={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Q={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},ee=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,et=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),er="remix-router-transitions",en=Symbol("ResetLoaderData");function ea(e,t,r,n){let a=n||t._deepestRenderedBoundaryId||e[0].id;return{...t,statusCode:q(r)?r.status:500,errors:{[a]:r}}}function eo(e,t){if(void 0!==e.signal.reason)throw e.signal.reason;throw Error(`${t?"queryRoute":"query"}() call aborted without an \`AbortSignal.reason\`: ${e.method} ${e.url}`)}function ei(e,t,r,n,a,o){let i,l;if(a){for(let e of(i=[],t))if(i.push(e),e.route.id===a){l=e;break}}else i=t,l=t[t.length-1];let s=N(n||".",M(i),T(e.pathname,r)||e.pathname,"path"===o);if(null==n&&(s.search=e.search,s.hash=e.hash),(null==n||""===n||"."===n)&&l){let e=eK(s.search);if(l.route.index&&!e)s.search=s.search?s.search.replace(/^\?/,"?index&"):"?index";else if(!l.route.index&&e){let e=new URLSearchParams(s.search),t=e.getAll("index");e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let r=e.toString();s.search=r?`?${r}`:""}}return"/"!==r&&(s.pathname="/"===s.pathname?r:A([r,s.pathname])),v(s)}function el(e,t,r){let n,a;if(!r||!(null!=r&&("formData"in r&&null!=r.formData||"body"in r&&void 0!==r.body)))return{path:t};if(r.formMethod&&!eX(r.formMethod))return{path:t,error:eD(405,{method:r.formMethod})};let o=()=>({path:t,error:eD(400,{type:"invalid-body"})}),i=(r.formMethod||"get").toUpperCase(),l=eH(t);if(void 0!==r.body){if("text/plain"===r.formEncType){if(!eV(i))return o();let e="string"==typeof r.body?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((e,[t,r])=>`${e}${t}=${r}
`,""):String(r.body);return{path:t,submission:{formMethod:i,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:e}}}else if("application/json"===r.formEncType){if(!eV(i))return o();try{let e="string"==typeof r.body?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:i,formAction:l,formEncType:r.formEncType,formData:void 0,json:e,text:void 0}}}catch(e){return o()}}}if(f("function"==typeof FormData,"FormData is not available in this environment"),r.formData)n=ek(r.formData),a=r.formData;else if(r.body instanceof FormData)n=ek(r.body),a=r.body;else if(r.body instanceof URLSearchParams)a=eT(n=r.body);else if(null==r.body)n=new URLSearchParams,a=new FormData;else try{n=new URLSearchParams(r.body),a=eT(n)}catch(e){return o()}let s={formMethod:i,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:a,json:void 0,text:void 0};if(eV(s.formMethod))return{path:t,submission:s};let u=g(t);return e&&u.search&&eK(u.search)&&n.append("index",""),u.search=`?${n}`,{path:v(u),submission:s}}function es(e,t,r,n,a,o,i,l,s,u,d,c,h,f,m,p,y,v,g){let w,b=g?eI(g[1])?g[1].error:g[1].data:void 0,R=a.createURL(o.location),E=a.createURL(s);if(d&&o.errors){let e=Object.keys(o.errors)[0];w=i.findIndex(t=>t.route.id===e)}else if(g&&eI(g[1])){let e=g[0];w=i.findIndex(t=>t.route.id===e)-1}let x=g?g[1].statusCode:void 0,S=x&&x>=400,$={currentUrl:R,currentParams:o.matches[0]?.params||{},nextUrl:E,nextParams:i[0].params,...l,actionResult:b,actionStatus:x},P=i.map((a,i)=>{var l,s,h,f,m;let p,y,v,{route:g}=a,b=null;if(null!=w&&i>w?b=!1:g.lazy?b=!0:null==g.loader?b=!1:d?b=eu(g,o.loaderData,o.errors):(l=o.loaderData,s=o.matches[i],h=a,p=!s||h.route.id!==s.route.id,y=!l.hasOwnProperty(h.route.id),(p||y)&&(b=!0)),null!==b)return eE(r,n,e,a,u,t,b);let x=!S&&(c||R.pathname+R.search===E.pathname+E.search||R.search!==E.search||(f=o.matches[i],m=a,v=f.route.path,f.pathname!==m.pathname||null!=v&&v.endsWith("*")&&f.params["*"]!==m.params["*"])),C={...$,defaultShouldRevalidate:x},P=ed(a,C);return eE(r,n,e,a,u,t,P,C)}),L=[];return m.forEach((e,l)=>{if(d||!i.some(t=>t.route.id===e.routeId)||f.has(l))return;let s=C(y,e.path,v);if(!s)return void L.push({key:l,routeId:e.routeId,path:e.path,matches:null,match:null,request:null,controller:null});if(p.has(l))return;let m=o.fetchers.get(l),g=eG(s,e.path),w=new AbortController,b=e_(a,e.path,w.signal),R=null;if(h.has(l))h.delete(l),R=ex(r,n,b,s,g,u,t);else if(m&&"idle"!==m.state&&void 0===m.data)c&&(R=ex(r,n,b,s,g,u,t));else{let e={...$,defaultShouldRevalidate:!S&&c};ed(g,e)&&(R=ex(r,n,b,s,g,u,t,e))}R&&L.push({key:l,routeId:e.routeId,path:e.path,matches:R,match:g,request:b,controller:w})}),{dsMatches:P,revalidatingFetchers:L}}function eu(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=null!=t&&void 0!==t[e.id],a=null!=r&&void 0!==r[e.id];return(!!n||!a)&&("function"==typeof e.loader&&!0===e.loader.hydrate||!n&&!a)}function ed(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if("boolean"==typeof r)return r}return t.defaultShouldRevalidate}function ec(e,t,r,n,a){let o;if(e){let t=n[e];f(t,`No route found to patch children into: routeId = ${e}`),t.children||(t.children=[]),o=t.children}else o=r;let i=x(t.filter(e=>!o.some(t=>(function e(t,r){return"id"in t&&"id"in r&&t.id===r.id||t.index===r.index&&t.path===r.path&&t.caseSensitive===r.caseSensitive&&((!t.children||0===t.children.length)&&(!r.children||0===r.children.length)||t.children.every((t,n)=>r.children?.some(r=>e(t,r))))})(e,t))),a,[e||"_","patch",String(o?.length||"0")],n);o.push(...i)}var eh=new WeakMap,ef=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let a=r[t.id];if(f(a,"No route found in manifest"),!a.lazy||"object"!=typeof a.lazy)return;let o=a.lazy[e];if(!o)return;let i=eh.get(a);i||(i={},eh.set(a,i));let l=i[e];if(l)return l;let s=(async()=>{let t=R.has(e),r=void 0!==a[e]&&"hasErrorBoundary"!==e;if(t)m(!t,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),i[e]=Promise.resolve();else if(r)m(!1,`Route "${a.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let t=await o();null!=t&&(Object.assign(a,{[e]:t}),Object.assign(a,n(a)))}"object"==typeof a.lazy&&(a.lazy[e]=void 0,Object.values(a.lazy).every(e=>void 0===e)&&(a.lazy=void 0))})();return i[e]=s,s},em=new WeakMap;function ep(e){return void 0!==e}function ey(e,t,r){let n=e.map(({route:e})=>{if("object"==typeof e.lazy&&e.lazy.unstable_middleware)return ef({key:"unstable_middleware",route:e,manifest:t,mapRouteProperties:r})}).filter(ep);return n.length>0?Promise.all(n):void 0}async function ev(e){let t=e.matches.filter(e=>e.shouldLoad),r={};return(await Promise.all(t.map(e=>e.resolve()))).forEach((e,n)=>{r[t[n].route.id]=e}),r}async function eg(e){return e.matches.some(e=>e.route.unstable_middleware)?ew(e,!1,()=>ev(e),(e,t)=>({[t]:{type:"error",result:e}})):ev(e)}async function ew(e,t,r,n){let{matches:a,request:o,params:i,context:l}=e,s={handlerResult:void 0};try{let e=a.flatMap(e=>e.route.unstable_middleware?e.route.unstable_middleware.map(t=>[e.route.id,t]):[]),n=await eb({request:o,params:i,context:l},e,t,s,r);return t?n:s.handlerResult}catch(r){if(!s.middlewareError)throw r;let e=await n(s.middlewareError.error,s.middlewareError.routeId);if(t||!s.handlerResult)return e;return Object.assign(s.handlerResult,e)}}async function eb(e,t,r,n,a,o=0){let i,{request:l}=e;if(l.signal.aborted){if(l.signal.reason)throw l.signal.reason;throw Error(`Request aborted without an \`AbortSignal.reason\`: ${l.method} ${l.url}`)}let s=t[o];if(!s)return n.handlerResult=await a(),n.handlerResult;let[u,d]=s,c=!1,h=async()=>{if(c)throw Error("You may only call `next()` once per middleware");c=!0;let l=await eb(e,t,r,n,a,o+1);if(r)return i=l};try{let t=await d({request:e.request,params:e.params,context:e.context},h);if(!c)return h();if(void 0===t)return i;return t}catch(e){throw n.middlewareError?n.middlewareError.error!==e&&(n.middlewareError={routeId:u,error:e}):n.middlewareError={routeId:u,error:e},e}}function eR(e,t,r,n,a){let o=ef({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),i=function(e,t,r,n,a){let o,i=r[e.id];if(f(i,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if("function"==typeof e.lazy){let t=em.get(i);if(t)return{lazyRoutePromise:t,lazyHandlerPromise:t};let r=(async()=>{f("function"==typeof e.lazy,"No lazy route function found");let t=await e.lazy(),r={};for(let e in t){let n=t[e];if(void 0===n)continue;let a=E.has(e),o=void 0!==i[e]&&"hasErrorBoundary"!==e;a?m(!a,"Route property "+e+" is not a supported property to be returned from a lazy route function. This property will be ignored."):o?m(!o,`Route "${i.id}" has a static property "${e}" defined but its lazy function is also returning a value for this property. The lazy route property "${e}" will be ignored.`):r[e]=n}Object.assign(i,r),Object.assign(i,{...n(i),lazy:void 0})})();return em.set(i,r),r.catch(()=>{}),{lazyRoutePromise:r,lazyHandlerPromise:r}}let l=Object.keys(e.lazy),s=[];for(let i of l){if(a&&a.includes(i))continue;let l=ef({key:i,route:e,manifest:r,mapRouteProperties:n});l&&(s.push(l),i===t&&(o=l))}let u=s.length>0?Promise.all(s).then(()=>{}):void 0;return u?.catch(()=>{}),o?.catch(()=>{}),{lazyRoutePromise:u,lazyHandlerPromise:o}}(n.route,eV(r.method)?"action":"loader",t,e,a);return{middleware:o,route:i.lazyRoutePromise,handler:i.lazyHandlerPromise}}function eE(e,t,r,n,a,o,i,l=null){let s=!1,u=eR(e,t,r,n,a);return{...n,_lazyPromises:u,shouldLoad:i,unstable_shouldRevalidateArgs:l,unstable_shouldCallHandler:e=>(s=!0,l)?"boolean"==typeof e?ed(n,{...l,defaultShouldRevalidate:e}):ed(n,l):i,resolve:e=>s||i||e&&"GET"===r.method&&(n.route.lazy||n.route.loader)?eS({request:r,match:n,lazyHandlerPromise:u?.handler,lazyRoutePromise:u?.route,handlerOverride:e,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}function ex(e,t,r,n,a,o,i,l=null){return n.map(n=>n.route.id!==a.route.id?{...n,shouldLoad:!1,unstable_shouldRevalidateArgs:l,unstable_shouldCallHandler:()=>!1,_lazyPromises:eR(e,t,r,n,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:eE(e,t,r,n,o,i,!0,l))}async function eC(e,t,r,n,a,o){r.some(e=>e._lazyPromises?.middleware)&&await Promise.all(r.map(e=>e._lazyPromises?.middleware));let i={request:t,params:r[0].params,context:a,matches:r},l=o?()=>{throw Error("You cannot call `unstable_runClientMiddleware()` from a static handler `dataStrategy`. Middleware is run outside of `dataStrategy` during SSR in order to bubble up the Response.  You can enable middleware via the `respond` API in `query`/`queryRoute`")}:e=>ew(i,!1,()=>e({...i,fetcherKey:n,unstable_runClientMiddleware:()=>{throw Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(e,t)=>({[t]:{type:"error",result:e}})),s=await e({...i,fetcherKey:n,unstable_runClientMiddleware:l});try{await Promise.all(r.flatMap(e=>[e._lazyPromises?.handler,e._lazyPromises?.route]))}catch(e){}return s}async function eS({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:a,scopedContext:o}){let i,l,s=eV(e.method),u=s?"action":"loader",d=r=>{let n,i=new Promise((e,t)=>n=t);l=()=>n(),e.signal.addEventListener("abort",l);let s=n=>"function"!=typeof r?Promise.reject(Error(`You cannot call the handler for a route which defines a boolean "${u}" [routeId: ${t.route.id}]`)):r({request:e,params:t.params,context:o},...void 0!==n?[n]:[]);return Promise.race([(async()=>{try{let e=await (a?a(e=>s(e)):s());return{type:"data",result:e}}catch(e){return{type:"error",result:e}}})(),i])};try{let a=s?t.route.action:t.route.loader;if(r||n)if(a){let e,[t]=await Promise.all([d(a).catch(t=>{e=t}),r,n]);if(void 0!==e)throw e;i=t}else{await r;let a=s?t.route.action:t.route.loader;if(a)[i]=await Promise.all([d(a),n]);else{if("action"!==u)return{type:"data",result:void 0};let r=new URL(e.url),n=r.pathname+r.search;throw eD(405,{method:e.method,pathname:n,routeId:t.route.id})}}else if(a)i=await d(a);else{let t=new URL(e.url),r=t.pathname+t.search;throw eD(404,{pathname:r})}}catch(e){return{type:"error",result:e}}finally{l&&e.signal.removeEventListener("abort",l)}return i}async function e$(e){let{result:t,type:r}=e;if(eq(t)){let e;try{let r=t.headers.get("Content-Type");e=r&&/\bapplication\/json\b/.test(r)?null==t.body?null:await t.json():await t.text()}catch(e){return{type:"error",error:e}}return"error"===r?{type:"error",error:new B(t.status,t.statusText,e),statusCode:t.status,headers:t.headers}:{type:"data",data:e,statusCode:t.status,headers:t.headers}}if("error"===r)return eB(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new B(t.init?.status||500,void 0,t.data),statusCode:q(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:q(t)?t.status:void 0};return eB(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function eP(e,t,r,n,a){let o=e.headers.get("Location");if(f(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!ee.test(o)){let i=n.slice(0,n.findIndex(e=>e.route.id===r)+1);o=ei(new URL(t.url),i,a,o),e.headers.set("Location",o)}return e}function eL(e,t,r){if(ee.test(e)){let n=new URL(e.startsWith("//")?t.protocol+e:e),a=null!=T(n.pathname,r);if(n.origin===t.origin&&a)return n.pathname+n.search+n.hash}return e}function e_(e,t,r,n){let a=e.createURL(eH(t)).toString(),o={signal:r};if(n&&eV(n.formMethod)){let{formMethod:e,formEncType:t}=n;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(n.json)):"text/plain"===t?o.body=n.text:"application/x-www-form-urlencoded"===t&&n.formData?o.body=ek(n.formData):o.body=n.formData}return new Request(a,o)}function ek(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,"string"==typeof n?n:n.name);return t}function eT(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function ej(e,t,r,n=!1,a=!1){let o,i={},l=null,s=!1,u={},d=r&&eI(r[1])?r[1].error:void 0;return e.forEach(r=>{if(!(r.route.id in t))return;let c=r.route.id,h=t[c];if(f(!eW(h),"Cannot handle redirect results in processLoaderData"),eI(h)){let t=h.error;if(void 0!==d&&(t=d,d=void 0),l=l||{},a)l[c]=t;else{let r=eA(e,c);null==l[r.route.id]&&(l[r.route.id]=t)}n||(i[c]=en),s||(s=!0,o=q(h.error)?h.error.status:500),h.headers&&(u[c]=h.headers)}else i[c]=h.data,h.statusCode&&200!==h.statusCode&&!s&&(o=h.statusCode),h.headers&&(u[c]=h.headers)}),void 0!==d&&r&&(l={[r[0]]:d},i[r[0]]=void 0),{loaderData:i,errors:l,statusCode:o||200,loaderHeaders:u}}function eO(e,t,r,n,a,o){let{loaderData:i,errors:l}=ej(t,r,n);return a.filter(e=>!e.matches||e.matches.some(e=>e.shouldLoad)).forEach(t=>{let{key:r,match:n,controller:a}=t,i=o[r];if(f(i,"Did not find corresponding fetcher result"),!a||!a.signal.aborted)if(eI(i)){let t=eA(e.matches,n?.route.id);l&&l[t.route.id]||(l={...l,[t.route.id]:i.error}),e.fetchers.delete(r)}else if(eW(i))f(!1,"Unhandled fetcher revalidation redirect");else{let t=e1(i.data);e.fetchers.set(r,t)}}),{loaderData:i,errors:l}}function eM(e,t,r,n){let a=Object.entries(t).filter(([,e])=>e!==en).reduce((e,[t,r])=>(e[t]=r,e),{});for(let o of r){let r=o.route.id;if(!t.hasOwnProperty(r)&&e.hasOwnProperty(r)&&o.route.loader&&(a[r]=e[r]),n&&n.hasOwnProperty(r))break}return a}function eN(e){return e?eI(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function eA(e,t){return(t?e.slice(0,e.findIndex(e=>e.route.id===t)+1):[...e]).reverse().find(e=>!0===e.route.hasErrorBoundary)||e[0]}function eU(e){let t=1===e.length?e[0]:e.find(e=>e.index||!e.path||"/"===e.path)||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function eD(e,{pathname:t,routeId:r,method:n,type:a,message:o}={}){let i="Unknown Server Error",l="Unknown @remix-run/router error";return 400===e?(i="Bad Request",n&&t&&r?l=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:"invalid-body"===a&&(l="Unable to encode submission body")):403===e?(i="Forbidden",l=`Route "${r}" does not match URL "${t}"`):404===e?(i="Not Found",l=`No route matches URL "${t}"`):405===e&&(i="Method Not Allowed",n&&t&&r?l=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(l=`Invalid request method "${n.toUpperCase()}"`)),new B(e||500,i,Error(l),!0)}function ez(e){let t=Object.entries(e);for(let e=t.length-1;e>=0;e--){let[r,n]=t[e];if(eW(n))return{key:r,result:n}}}function eH(e){return v({..."string"==typeof e?g(e):e,hash:""})}function eF(e){return eq(e.result)&&V.has(e.result.status)}function eI(e){return"error"===e.type}function eW(e){return"redirect"===(e&&e.type)}function eB(e){return"object"==typeof e&&null!=e&&"type"in e&&"data"in e&&"init"in e&&"DataWithResponseInit"===e.type}function eq(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"object"==typeof e.headers&&void 0!==e.body}function eJ(e){return V.has(e)}function eY(e){return eq(e)&&eJ(e.status)&&e.headers.has("Location")}function eX(e){return X.has(e.toUpperCase())}function eV(e){return Y.has(e.toUpperCase())}function eK(e){return new URLSearchParams(e).getAll("index").some(e=>""===e)}function eG(e,t){let r="string"==typeof t?g(t).search:t.search;if(e[e.length-1].route.index&&eK(r||""))return e[e.length-1];let n=O(e);return n[n.length-1]}function eZ(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:o,json:i}=e;if(t&&r&&n){if(null!=a)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};else if(null!=o)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};else if(void 0!==i)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:i,text:void 0}}}function eQ(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function e0(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function e1(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}var e2=o.createContext(null);e2.displayName="DataRouter";var e4=o.createContext(null);e4.displayName="DataRouterState";var e5=o.createContext({isTransitioning:!1});e5.displayName="ViewTransition";var e3=o.createContext(new Map);e3.displayName="Fetchers";var e9=o.createContext(null);e9.displayName="Await";var e8=o.createContext(null);e8.displayName="Navigation";var e6=o.createContext(null);e6.displayName="Location";var e7=o.createContext({outlet:null,matches:[],isDataRoute:!1});e7.displayName="Route";var te=o.createContext(null);function tt(){return null!=o.useContext(e6)}function tr(){return f(tt(),"useLocation() may be used only in the context of a <Router> component."),o.useContext(e6).location}te.displayName="RouteError";var tn="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ta(e){o.useContext(e8).static||o.useLayoutEffect(e)}function to(){let{isDataRoute:e}=o.useContext(e7);return e?function(){let{router:e}=function(e){let t=o.useContext(e2);return f(t,th(e)),t}("useNavigate"),t=tm("useNavigate"),r=o.useRef(!1);return ta(()=>{r.current=!0}),o.useCallback(async(n,a={})=>{m(r.current,tn),r.current&&("number"==typeof n?e.navigate(n):await e.navigate(n,{fromRouteId:t,...a}))},[e,t])}():function(){f(tt(),"useNavigate() may be used only in the context of a <Router> component.");let e=o.useContext(e2),{basename:t,navigator:r}=o.useContext(e8),{matches:n}=o.useContext(e7),{pathname:a}=tr(),i=JSON.stringify(M(n)),l=o.useRef(!1);return ta(()=>{l.current=!0}),o.useCallback((n,o={})=>{if(m(l.current,tn),!l.current)return;if("number"==typeof n)return void r.go(n);let s=N(n,JSON.parse(i),a,"path"===o.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:A([t,s.pathname])),(o.replace?r.replace:r.push)(s,o.state,o)},[t,r,i,a,e])}()}var ti=o.createContext(null);function tl(e,{relative:t}={}){let{matches:r}=o.useContext(e7),{pathname:n}=tr(),a=JSON.stringify(M(r));return o.useMemo(()=>N(e,JSON.parse(a),n,"path"===t),[e,a,n,t])}function ts(e,t,r,n){let a;f(tt(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:i,static:l}=o.useContext(e8),{matches:s}=o.useContext(e7),u=s[s.length-1],d=u?u.params:{},c=u?u.pathname:"/",h=u?u.pathnameBase:"/",p=u&&u.route;{let e=p&&p.path||"";tv(c,!p||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${c}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let y=tr();if(t){let e="string"==typeof t?g(t):t;f("/"===h||e.pathname?.startsWith(h),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${h}" but pathname "${e.pathname}" was given in the \`location\` prop.`),a=e}else a=y;let v=a.pathname||"/",w=v;if("/"!==h){let e=h.replace(/^\//,"").split("/");w="/"+v.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=!l&&r&&r.matches&&r.matches.length>0?r.matches:C(e,{pathname:w});m(p||null!=b,`No routes matched location "${a.pathname}${a.search}${a.hash}" `),m(null==b||void 0!==b[b.length-1].route.element||void 0!==b[b.length-1].route.Component||void 0!==b[b.length-1].route.lazy,`Matched leaf route at location "${a.pathname}${a.search}${a.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let R=function(e,t=[],r=null,n=null){if(null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(0!==t.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let a=e,i=r?.errors;if(null!=i){let e=a.findIndex(e=>e.route.id&&i?.[e.route.id]!==void 0);f(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(i).join(",")}`),a=a.slice(0,Math.min(a.length,e+1))}let l=!1,s=-1;if(r)for(let e=0;e<a.length;e++){let t=a[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(s=e),t.route.id){let{loaderData:e,errors:n}=r,o=t.route.loader&&!e.hasOwnProperty(t.route.id)&&(!n||void 0===n[t.route.id]);if(t.route.lazy||o){l=!0,a=s>=0?a.slice(0,s+1):[a[0]];break}}}return a.reduceRight((e,n,u)=>{let d,c=!1,h=null,f=null;r&&(d=i&&n.route.id?i[n.route.id]:void 0,h=n.route.errorElement||tu,l&&(s<0&&0===u?(tv("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),c=!0,f=null):s===u&&(c=!0,f=n.route.hydrateFallbackElement||null)));let m=t.concat(a.slice(0,u+1)),p=()=>{let t;return t=d?h:c?f:n.route.Component?o.createElement(n.route.Component,null):n.route.element?n.route.element:e,o.createElement(tc,{match:n,routeContext:{outlet:e,matches:m,isDataRoute:null!=r},children:t})};return r&&(n.route.ErrorBoundary||n.route.errorElement||0===u)?o.createElement(td,{location:r.location,revalidation:r.revalidation,component:h,error:d,children:p(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):p()},null)}(b&&b.map(e=>Object.assign({},e,{params:Object.assign({},d,e.params),pathname:A([h,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?h:A([h,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),s,r,n);return t&&R?o.createElement(e6.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...a},navigationType:"POP"}},R):R}var tu=o.createElement(function(){let e=tp(),t=q(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",a={padding:"2px 4px",backgroundColor:n},i=null;return console.error("Error handled by React Router default ErrorBoundary:",e),i=o.createElement(o.Fragment,null,o.createElement("p",null,"\uD83D\uDCBF Hey developer \uD83D\uDC4B"),o.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",o.createElement("code",{style:a},"ErrorBoundary")," or"," ",o.createElement("code",{style:a},"errorElement")," prop on your route.")),o.createElement(o.Fragment,null,o.createElement("h2",null,"Unexpected Application Error!"),o.createElement("h3",{style:{fontStyle:"italic"}},t),r?o.createElement("pre",{style:{padding:"0.5rem",backgroundColor:n}},r):null,i)},null),td=class extends o.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?o.createElement(e7.Provider,{value:this.props.routeContext},o.createElement(te.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function tc({routeContext:e,match:t,children:r}){let n=o.useContext(e2);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),o.createElement(e7.Provider,{value:e},r)}function th(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function tf(e){let t=o.useContext(e4);return f(t,th(e)),t}function tm(e){let t,r=(f(t=o.useContext(e7),th(e)),t),n=r.matches[r.matches.length-1];return f(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function tp(){let e=o.useContext(te),t=tf("useRouteError"),r=tm("useRouteError");return void 0!==e?e:t.errors?.[r]}var ty={};function tv(e,t,r){t||ty[e]||(ty[e]=!0,m(!1,r))}var tg={};function tw(e,t){e||tg[t]||(tg[t]=!0,console.warn(t))}var tb=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}})}},tR=o.memo(function({routes:e,future:t,state:r}){return ts(e,void 0,r,t)});function tE({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:a,static:i=!1}){f(!tt(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),s=o.useMemo(()=>({basename:l,navigator:a,static:i,future:{}}),[l,a,i]);"string"==typeof r&&(r=g(r));let{pathname:u="/",search:d="",hash:c="",state:h=null,key:p="default"}=r,y=o.useMemo(()=>{let e=T(u,l);return null==e?null:{location:{pathname:e,search:d,hash:c,state:h,key:p},navigationType:n}},[l,u,d,c,h,p,n]);return(m(null!=y,`<Router basename="${l}"> is not able to match the URL "${u}${d}${c}" because it does not start with the basename, so the <Router> won't render anything.`),null==y)?null:o.createElement(e8.Provider,{value:s},o.createElement(e6.Provider,{children:t,value:y}))}o.Component;var tx="get",tC="application/x-www-form-urlencoded";function tS(e){return null!=e&&"string"==typeof e.tagName}var t$=null,tP=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function tL(e){return null==e||tP.has(e)?e:(m(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${tC}"`),null)}function t_(e,t){if(!1===e||null==e)throw Error(t)}async function tk(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(t){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(t),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function tT(e){return e.css?e.css.map(e=>({rel:"stylesheet",href:e})):[]}async function tj(e){if(!e.css)return;let t=tT(e);await Promise.all(t.map(tM))}async function tO(e,t){if(!e.css&&!t.links||!function(){if(void 0!==a)return a;let e=document.createElement("link");return a=e.relList.supports("preload"),e=null,a}())return;let r=[];if(e.css&&r.push(...tT(e)),t.links&&r.push(...t.links()),0===r.length)return;let n=[];for(let e of r)tN(e)||"stylesheet"!==e.rel||n.push({...e,rel:"preload",as:"style"});await Promise.all(n.map(tM))}async function tM(e){return new Promise(t=>{if(e.media&&!window.matchMedia(e.media).matches||document.querySelector(`link[rel="stylesheet"][href="${e.href}"]`))return t();let r=document.createElement("link");function n(){document.head.contains(r)&&document.head.removeChild(r)}Object.assign(r,e),r.onload=()=>{n(),t()},r.onerror=()=>{n(),t()},document.head.appendChild(r)})}function tN(e){return null!=e&&"string"==typeof e.page}function tA(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}async function tU(e,t,r){return function(e,t){let r=new Set,n=new Set(t);return e.reduce((e,a)=>{if(t&&!tN(a)&&"script"===a.as&&a.href&&n.has(a.href))return e;let o=JSON.stringify(function(e){let t={};for(let r of Object.keys(e).sort())t[r]=e[r];return t}(a));return r.has(o)||(r.add(o),e.push({key:o,link:a})),e},[])}((await Promise.all(e.map(async e=>{let n=t.routes[e.route.id];if(n){let e=await tk(n,r);return e.links?e.links():[]}return[]}))).flat(1).filter(tA).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"}))}function tD(e,t,r,n,a,o){let i=(e,t)=>!r[t]||e.route.id!==r[t].route.id,l=(e,t)=>r[t].pathname!==e.pathname||r[t].route.path?.endsWith("*")&&r[t].params["*"]!==e.params["*"];return"assets"===o?t.filter((e,t)=>i(e,t)||l(e,t)):"data"===o?t.filter((t,o)=>{let s=n.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,o)||l(t,o))return!0;if(t.route.shouldRevalidate){let n=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof n)return n}return!0}):[]}function tz(e,t,{includeHydrateFallback:r}={}){return[...new Set(e.map(e=>{let n=t.routes[e.route.id];if(!n)return[];let a=[n.module];return n.clientActionModule&&(a=a.concat(n.clientActionModule)),n.clientLoaderModule&&(a=a.concat(n.clientLoaderModule)),r&&n.hydrateFallbackModule&&(a=a.concat(n.hydrateFallbackModule)),n.imports&&(a=a.concat(n.imports)),a}).flat(1))]}var tH={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},tF=/[&><\u2028\u2029]/g;async function tI(e){let t={signal:e.signal};if("GET"!==e.method){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}var tW=Symbol("SingleFetchRedirect"),tB=new Set([100,101,204,205]);function tq({context:e,identifier:t,reader:r,textDecoder:n,nonce:a}){if(!e.renderMeta||!e.renderMeta.didRenderScripts)return null;e.renderMeta.streamCache||(e.renderMeta.streamCache={});let{streamCache:o}=e.renderMeta,i=o[t];if(i||(i=o[t]=r.read().then(e=>{o[t].result={done:e.done,value:n.decode(e.value,{stream:!0})}}).catch(e=>{o[t].error=e})),i.error)throw i.error;if(void 0===i.result)throw i;let{done:l,value:s}=i.result,u=s?React4.createElement("script",{nonce:a,dangerouslySetInnerHTML:{__html:`window.__reactRouterContext.streamController.enqueue(${JSON.stringify(s).replace(tF,e=>tH[e])});`}}):null;return l?React4.createElement(React4.Fragment,null,u,React4.createElement("script",{nonce:a,dangerouslySetInnerHTML:{__html:"window.__reactRouterContext.streamController.close();"}})):React4.createElement(React4.Fragment,null,u,React4.createElement(React4.Suspense,null,React4.createElement(tq,{context:e,identifier:t+1,reader:r,textDecoder:n,nonce:a})))}function tJ(e,t){let r="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===r.pathname?r.pathname="_root.data":t&&"/"===T(r.pathname,t)?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function tY(e,t){return decode(e,{plugins:[(e,...r)=>{if("SanitizedError"===e){let[e,n,a]=r,o=Error;e&&e in t&&"function"==typeof t[e]&&(o=t[e]);let i=new o(n);return i.stack=a,{value:i}}if("ErrorResponse"===e){let[e,t,n]=r;return{value:new B(t,n,e)}}return"SingleFetchRedirect"===e?{value:{[tW]:r[0]}}:"SingleFetchClassInstance"===e?{value:r[0]}:"SingleFetchFallback"===e?{value:void 0}:void 0}]})}function tX(e,t){if("redirect"in e){let{redirect:t,revalidate:r,reload:n,replace:a,status:o}=e.redirect;throw F(t,{status:o,headers:{...r?{"X-Remix-Revalidate":"yes"}:null,...n?{"X-Remix-Reload-Document":"yes"}:null,...a?{"X-Remix-Replace":"yes"}:null}})}let r=e.routes[t];if("error"in r)throw r.error;if("data"in r)return r.data;throw Error(`No response found for routeId "${t}"`)}function tV(){let e,t,r=new Promise((n,a)=>{e=async e=>{n(e);try{await r}catch(e){}},t=async e=>{a(e);try{await r}catch(e){}}});return{promise:r,resolve:e,reject:t}}function tK({error:e,isOutsideRemixApp:t}){let r;console.error(e);let n=o.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "\u{1F4BF} Hey developer \u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information."
        );
      `}});return q(e)?o.createElement(tG,{title:"Unhandled Thrown Response!"},o.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),n):(r=e instanceof Error?e:Error(null==e?"Unknown Error":"object"==typeof e&&"toString"in e?e.toString():JSON.stringify(e)),o.createElement(tG,{title:"Application Error!",isOutsideRemixApp:t},o.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),o.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},r.stack),n))}function tG({title:e,renderScripts:t,isOutsideRemixApp:r,children:n}){let{routeModules:a}=re();return a.root?.Layout&&!r?n:o.createElement("html",{lang:"en"},o.createElement("head",null,o.createElement("meta",{charSet:"utf-8"}),o.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),o.createElement("title",null,e)),o.createElement("body",null,o.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},n,t?o.createElement(ro,null):null)))}function tZ(){return React6.createElement(tG,{title:"Loading...",renderScripts:!0},React6.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "\u{1F4BF} Hey developer \u{1F44B}. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://remix.run/route/hydrate-fallback " +
                "for more information."
              );
            `}}))}o.Component;function tQ(e,t){if("loader"===e&&!t.hasLoader||"action"===e&&!t.hasAction){let r=`You are trying to call ${"action"===e?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;throw console.error(r),new B(400,"Bad Request",Error(r),!0)}}function t0(e,t){let r="clientAction"===e?"a":"an",n=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(n),new B(405,"Method Not Allowed",Error(n),!0)}function t1(e,t,r,n,a){if(a){var o,i,l;let e;return o=r.id,i=t.shouldRevalidate,l=a,e=!1,t=>e?i?i(t):t.defaultShouldRevalidate:(e=!0,l.has(o))}if(!n&&r.hasLoader&&!r.hasClientLoader){let r=e?k(e)[1].map(e=>e.paramName):[],n=e=>r.some(t=>e.currentParams[t]!==e.nextParams[t]);if(!t.shouldRevalidate)return e=>n(e);{let e=t.shouldRevalidate;return t=>e({...t,defaultShouldRevalidate:n(t)})}}if(n&&t.shouldRevalidate){let e=t.shouldRevalidate;return t=>e({...t,defaultShouldRevalidate:!0})}return t.shouldRevalidate}async function t2(e,t){let r=tk(e,t),n=tj(e),a=await r;return await Promise.all([n,tO(e,a)]),{Component:t4(a),ErrorBoundary:a.ErrorBoundary,unstable_clientMiddleware:a.unstable_clientMiddleware,clientAction:a.clientAction,clientLoader:a.clientLoader,handle:a.handle,links:a.links,meta:a.meta,shouldRevalidate:a.shouldRevalidate}}function t4(e){if(null!=e.default&&("object"!=typeof e.default||0!==Object.keys(e.default).length))return e.default}var t5=new Set,t3=new Set,t9="react-router-manifest-version";function t8(){let e=o.useContext(e2);return t_(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function t6(){let e=o.useContext(e4);return t_(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var t7=o.createContext(void 0);function re(){let e=o.useContext(t7);return t_(e,"You must render this element inside a <HydratedRouter> element"),e}function rt(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}t7.displayName="FrameworkContext";function rr({page:e,...t}){let{router:r}=t8(),n=o.useMemo(()=>C(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?o.createElement(rn,{page:e,matches:n,...t}):null}function rn({page:e,matches:t,...r}){let n=tr(),{manifest:a,routeModules:i}=re(),{basename:l}=t8(),{loaderData:s,matches:u}=t6(),d=o.useMemo(()=>tD(e,t,u,a,n,"data"),[e,t,u,a,n]),c=o.useMemo(()=>tD(e,t,u,a,n,"assets"),[e,t,u,a,n]),h=o.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let r=new Set,o=!1;if(t.forEach(e=>{let t=a.routes[e.route.id];t&&t.hasLoader&&(!d.some(t=>t.route.id===e.route.id)&&e.route.id in s&&i[e.route.id]?.shouldRevalidate||t.hasClientLoader?o=!0:r.add(e.route.id))}),0===r.size)return[];let u=tJ(e,l);return o&&r.size>0&&u.searchParams.set("_routes",t.filter(e=>r.has(e.route.id)).map(e=>e.route.id).join(",")),[u.pathname+u.search]},[l,s,n,a,d,t,e,i]),f=o.useMemo(()=>tz(c,a),[c,a]),m=function(e){let{manifest:t,routeModules:r}=re(),[n,a]=o.useState([]);return o.useEffect(()=>{let n=!1;return tU(e,t,r).then(e=>{n||a(e)}),()=>{n=!0}},[e,t,r]),n}(c);return o.createElement(o.Fragment,null,h.map(e=>o.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...r})),f.map(e=>o.createElement("link",{key:e,rel:"modulepreload",href:e,...r})),m.map(({key:e,link:t})=>o.createElement("link",{key:e,...t})))}var ra=!1;function ro(e){let{manifest:t,serverHandoffString:r,isSpaMode:n,ssr:a,renderMeta:i}=re(),{router:l,static:s,staticContext:u}=t8(),{matches:d}=t6(),c=!0===a;i&&(i.didRenderScripts=!0);let h=function(e,t,r){if(r&&!ra)return[e[0]];!1;return e}(d,null,n);o.useEffect(()=>{ra=!0},[]);let f=o.useMemo(()=>{let n=u?`window.__reactRouterContext = ${r};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",a=s?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${!c?`import ${JSON.stringify(t.url)}`:""};
${h.map((e,r)=>{let n=`route${r}`,a=t.routes[e.route.id];t_(a,`Route ${e.route.id} not found in manifest`);let{clientActionModule:o,clientLoaderModule:i,clientMiddlewareModule:l,hydrateFallbackModule:s,module:u}=a,d=[...o?[{module:o,varName:`${n}_clientAction`}]:[],...i?[{module:i,varName:`${n}_clientLoader`}]:[],...l?[{module:l,varName:`${n}_clientMiddleware`}]:[],...s?[{module:s,varName:`${n}_HydrateFallback`}]:[],{module:u,varName:`${n}_main`}];return 1===d.length?`import * as ${n} from ${JSON.stringify(u)};`:[d.map(e=>`import * as ${e.varName} from "${e.module}";`).join("\n"),`const ${n} = {${d.map(e=>`...${e.varName}`).join(",")}};`].join("\n")}).join("\n")}
  ${c?`window.__reactRouterManifest = ${JSON.stringify(function({sri:e,...t},r){let n=new Set(r.state.matches.map(e=>e.route.id)),a=r.state.location.pathname.split("/").filter(Boolean),o=["/"];for(a.pop();a.length>0;)o.push(`/${a.join("/")}`),a.pop();o.forEach(e=>{let t=C(r.routes,e,r.basename);t&&t.forEach(e=>n.add(e.route.id))});let i=[...n].reduce((e,r)=>Object.assign(e,{[r]:t.routes[r]}),{});return{...t,routes:i,sri:!!e||void 0}}(t,l),null,2)};`:""}
  window.__reactRouterRouteModules = {${h.map((e,t)=>`${JSON.stringify(e.route.id)}:route${t}`).join(",")}};

import(${JSON.stringify(t.entry.module)});`:" ";return o.createElement(o.Fragment,null,o.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:n},type:void 0}),o.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:a},type:"module",async:!0}))},[]),m=ra?[]:[...new Set(t.entry.imports.concat(tz(h,t,{includeHydrateFallback:!0})))],p="object"==typeof t.sri?t.sri:{};return ra?null:o.createElement(o.Fragment,null,"object"==typeof t.sri?o.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:p})}}):null,c?null:o.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:p[t.url],suppressHydrationWarning:!0}),o.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:p[t.entry.module],suppressHydrationWarning:!0}),m.map(t=>o.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:p[t],suppressHydrationWarning:!0})),f)}var ri="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{ri&&(window.__reactRouterVersion="7.5.2")}catch(e){}var rl=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,rs=o.forwardRef(function({onClick:e,discover:t="render",prefetch:r="none",relative:n,reloadDocument:a,replace:i,state:l,target:s,to:u,preventScrollReset:d,viewTransition:c,...h},p){let y,{basename:g}=o.useContext(e8),w="string"==typeof u&&rl.test(u),b=!1;if("string"==typeof u&&w&&(y=u,ri))try{let e=new URL(window.location.href),t=new URL(u.startsWith("//")?e.protocol+u:u),r=T(t.pathname,g);t.origin===e.origin&&null!=r?u=r+t.search+t.hash:b=!0}catch(e){m(!1,`<Link to="${u}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let R=function(e,{relative:t}={}){f(tt(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=o.useContext(e8),{hash:a,pathname:i,search:l}=tl(e,{relative:t}),s=i;return"/"!==r&&(s="/"===i?r:A([r,i])),n.createHref({pathname:s,search:l,hash:a})}(u,{relative:n}),[E,x,C]=function(e,t){let r=o.useContext(t7),[n,a]=o.useState(!1),[i,l]=o.useState(!1),{onFocus:s,onBlur:u,onMouseEnter:d,onMouseLeave:c,onTouchStart:h}=t,f=o.useRef(null);o.useEffect(()=>{if("render"===e&&l(!0),"viewport"===e){let e=new IntersectionObserver(e=>{e.forEach(e=>{l(e.isIntersecting)})},{threshold:.5});return f.current&&e.observe(f.current),()=>{e.disconnect()}}},[e]),o.useEffect(()=>{if(n){let e=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(e)}}},[n]);let m=()=>{a(!0)},p=()=>{a(!1),l(!1)};return r?"intent"!==e?[i,f,{}]:[i,f,{onFocus:rt(s,m),onBlur:rt(u,p),onMouseEnter:rt(d,m),onMouseLeave:rt(c,p),onTouchStart:rt(h,m)}]:[!1,f,{}]}(r,h),S=function(e,{target:t,replace:r,state:n,preventScrollReset:a,relative:i,viewTransition:l}={}){let s=to(),u=tr(),d=tl(e,{relative:i});return o.useCallback(o=>{0!==o.button||t&&"_self"!==t||o.metaKey||o.altKey||o.ctrlKey||o.shiftKey||(o.preventDefault(),s(e,{replace:void 0!==r?r:v(u)===v(d),state:n,preventScrollReset:a,relative:i,viewTransition:l}))},[u,s,d,r,n,t,e,a,i,l])}(u,{replace:i,state:l,target:s,preventScrollReset:d,relative:n,viewTransition:c}),$=o.createElement("a",{...h,...C,href:y||R,onClick:b||a?e:function(t){e&&e(t),t.defaultPrevented||S(t)},ref:function(...e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}(p,x),target:s,"data-discover":w||"render"!==t?void 0:"true"});return E&&!w?o.createElement(o.Fragment,null,$,o.createElement(rr,{page:R})):$});function ru(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function rd(e){let t=o.useContext(e2);return f(t,ru(e)),t}rs.displayName="Link",o.forwardRef(function({"aria-current":e="page",caseSensitive:t=!1,className:r="",end:n=!1,style:a,to:i,viewTransition:l,children:s,...u},d){let c,h=tl(i,{relative:u.relative}),m=tr(),p=o.useContext(e4),{navigator:y,basename:v}=o.useContext(e8),g=null!=p&&function(e,t={}){let r=o.useContext(e5);f(null!=r,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=rd("useViewTransitionState"),a=tl(e,{relative:t.relative});if(!r.isTransitioning)return!1;let i=T(r.currentLocation.pathname,n)||r.currentLocation.pathname,l=T(r.nextLocation.pathname,n)||r.nextLocation.pathname;return null!=_(a.pathname,l)||null!=_(a.pathname,i)}(h)&&!0===l,w=y.encodeLocation?y.encodeLocation(h).pathname:h.pathname,b=m.pathname,R=p&&p.navigation&&p.navigation.location?p.navigation.location.pathname:null;t||(b=b.toLowerCase(),R=R?R.toLowerCase():null,w=w.toLowerCase()),R&&v&&(R=T(R,v)||R);let E="/"!==w&&w.endsWith("/")?w.length-1:w.length,x=b===w||!n&&b.startsWith(w)&&"/"===b.charAt(E),C=null!=R&&(R===w||!n&&R.startsWith(w)&&"/"===R.charAt(w.length)),S={isActive:x,isPending:C,isTransitioning:g},$=x?e:void 0;c="function"==typeof r?r(S):[r,x?"active":null,C?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let P="function"==typeof a?a(S):a;return o.createElement(rs,{...u,"aria-current":$,className:c,ref:d,style:P,to:i,viewTransition:l},"function"==typeof s?s(S):s)}).displayName="NavLink",o.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:a,state:i,method:l=tx,action:s,onSubmit:u,relative:d,preventScrollReset:c,viewTransition:h,...m},p)=>{let y=function(){let{router:e}=rd("useSubmit"),{basename:t}=o.useContext(e8),r=tm("useRouteId");return o.useCallback(async(n,a={})=>{let{action:o,method:i,encType:l,formData:s,body:u}=function(e,t){let r,n,a,o,i;if(tS(e)&&"form"===e.tagName.toLowerCase()){let i=e.getAttribute("action");n=i?T(i,t):null,r=e.getAttribute("method")||tx,a=tL(e.getAttribute("enctype"))||tC,o=new FormData(e)}else if(tS(e)&&"button"===e.tagName.toLowerCase()||tS(e)&&"input"===e.tagName.toLowerCase()&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(n=l?T(l,t):null,r=e.getAttribute("formmethod")||i.getAttribute("method")||tx,a=tL(e.getAttribute("formenctype"))||tL(i.getAttribute("enctype"))||tC,o=new FormData(i,e),!function(){if(null===t$)try{new FormData(document.createElement("form"),0),t$=!1}catch(e){t$=!0}return t$}()){let{name:t,type:r,value:n}=e;if("image"===r){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,n)}}else if(tS(e))throw Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');else r=tx,n=null,a=tC,i=e;return o&&"text/plain"===a&&(i=o,o=void 0),{action:n,method:r.toLowerCase(),encType:a,formData:o,body:i}}(n,t);if(!1===a.navigate){let t=a.fetcherKey||rh();await e.fetch(t,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||i,formEncType:a.encType||l,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||i,formEncType:a.encType||l,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}(),g=function(e,{relative:t}={}){let{basename:r}=o.useContext(e8),n=o.useContext(e7);f(n,"useFormAction must be used inside a RouteContext");let[a]=n.matches.slice(-1),i={...tl(e||".",{relative:t})},l=tr();if(null==e){i.search=l.search;let e=new URLSearchParams(i.search),t=e.getAll("index");if(t.some(e=>""===e)){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let r=e.toString();i.search=r?`?${r}`:""}}return(!e||"."===e)&&a.route.index&&(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),"/"!==r&&(i.pathname="/"===i.pathname?r:A([r,i.pathname])),v(i)}(s,{relative:d}),w="get"===l.toLowerCase()?"get":"post",b="string"==typeof s&&rl.test(s);return o.createElement("form",{ref:p,method:w,action:g,onSubmit:n?u:e=>{if(u&&u(e),e.defaultPrevented)return;e.preventDefault();let n=e.nativeEvent.submitter,o=n?.getAttribute("formmethod")||l;y(n||e.currentTarget,{fetcherKey:t,method:o,navigate:r,replace:a,state:i,relative:d,preventScrollReset:c,viewTransition:h})},...m,"data-discover":b||"render"!==e?void 0:"true"})}).displayName="Form";var rc=0,rh=()=>`__${String(++rc)}__`;function rf({routes:e,future:t,state:r}){return ts(e,void 0,r,t)}function rm(e){return"string"==typeof e?e:v(e)}function rp(e){let t="string"==typeof e?e:v(e),r=ry.test(t=t.replace(/ $/,"%20"))?new URL(t):new URL(t,"http://localhost");return{pathname:r.pathname,search:r.search,hash:r.hash}}var ry=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,rv={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},rg=/[&><\u2028\u2029]/g,rw=new TextEncoder,rb=async(e,t)=>{let r=rw.encode(e),n=await rE(t,["sign"]);return e+"."+btoa(String.fromCharCode(...new Uint8Array(await crypto.subtle.sign("HMAC",n,r)))).replace(/=+$/,"")},rR=async(e,t)=>{let r=e.lastIndexOf("."),n=e.slice(0,r),a=e.slice(r+1),o=rw.encode(n),i=await rE(t,["verify"]),l=function(e){let t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}(atob(a));return!!await crypto.subtle.verify("HMAC",i,l,o)&&n},rE=async(e,t)=>crypto.subtle.importKey("raw",rw.encode(e),{name:"HMAC",hash:"SHA-256"},!1,t);async function rx(e,t){let r=btoa(function(e){let t,r,n=e.toString(),a="",o=0;for(;o<n.length;){if("%"===(t=n.charAt(o++))){if("u"===n.charAt(o)){if(r=n.slice(o+1,o+5),/^[\da-f]{4}$/i.exec(r)){a+=String.fromCharCode(parseInt(r,16)),o+=5;continue}}else if(r=n.slice(o,o+2),/^[\da-f]{2}$/i.exec(r)){a+=String.fromCharCode(parseInt(r,16)),o+=2;continue}}a+=t}return a}(encodeURIComponent(JSON.stringify(e))));return t.length>0&&(r=await rb(r,t[0])),r}async function rC(e,t){if(t.length>0){for(let r of t){let t=await rR(e,r);if(!1!==t)return rS(t)}return null}return rS(e)}function rS(e){try{return JSON.parse(decodeURIComponent(function(e){let t,r,n=e.toString(),a="",o=0;for(;o<n.length;)t=n.charAt(o++),/[\w*+\-./@]/.exec(t)?a+=t:(r=t.charCodeAt(0))<256?a+="%"+r$(r,2):a+="%u"+r$(r,4).toUpperCase();return a}(atob(e))))}catch(e){return{}}}function r$(e,t){let r=e.toString(16);for(;r.length<t;)r="0"+r;return r}var rP=(e=>(e.Development="development",e.Production="production",e.Test="test",e))(rP||{});function rL(e,t){if(e instanceof Error&&"development"!==t){let e=Error("Unexpected Server Error");return e.stack=void 0,e}return e}function r_(e,t){return Object.entries(e).reduce((e,[r,n])=>Object.assign(e,{[r]:rL(n,t)}),{})}function rk(e,t){let r=rL(e,t);return{message:r.message,stack:r.stack}}function rT(e,t){if(!e)return null;let r=Object.entries(e),n={};for(let[e,a]of r)if(q(a))n[e]={...a,__type:"RouteErrorResponse"};else if(a instanceof Error){let r=rL(a,t);n[e]={message:r.message,stack:r.stack,__type:"Error",..."Error"!==r.name?{__subType:r.name}:{}}}else n[e]=a;return n}async function rj(e,t){var r;let n,a,o=await e({request:(r=function(e){let t=new URL(e.url),r=t.searchParams.getAll("index");t.searchParams.delete("index");let n=[];for(let e of r)e&&n.push(e);for(let e of n)t.searchParams.append("index",e);let a={method:e.method,body:e.body,headers:e.headers,signal:e.signal};return a.body&&(a.duplex="half"),new Request(t.href,a)}(t.request),(n=new URL(r.url)).searchParams.delete("_routes"),(a={method:r.method,body:r.body,headers:r.headers,signal:r.signal}).body&&(a.duplex="half"),new Request(n.href,a)),params:t.params,context:t.context});if(eB(o)&&o.init&&o.init.status&&eJ(o.init.status))throw new Response(null,o.init);return o}function rO(e,t){if(!1===e||null==e)throw console.error("The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose"),Error(t)}function rM(e){let t={};return Object.values(e).forEach(e=>{if(e){let r=e.parentId||"";t[r]||(t[r]=[]),t[r].push(e)}}),t}var rN={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},rA=/[&><\u2028\u2029]/g;function rU(e){return JSON.stringify(e).replace(rA,e=>rN[e])}function rD(e,t){let r,n=t.errors?t.matches.findIndex(e=>t.errors[e.route.id]):-1,a=n>=0?t.matches.slice(0,n+1):t.matches;if(n>=0){let{actionHeaders:e,actionData:a,loaderHeaders:o,loaderData:i}=t;t.matches.slice(n).some(t=>{let n=t.route.id;return!e[n]||a&&a.hasOwnProperty(n)?o[n]&&!i.hasOwnProperty(n)&&(r=o[n]):r=e[n],null!=r})}return a.reduce((n,o,i)=>{let{id:l}=o.route,s=e.routes[l];rO(s,`Route with id "${l}" not found in build`);let u=s.module,d=t.loaderHeaders[l]||new Headers,c=t.actionHeaders[l]||new Headers,h=null!=r&&i===a.length-1,f=h&&r!==d&&r!==c;if(null==u.headers){let e=new Headers(n);return f&&rz(r,e),rz(c,e),rz(d,e),e}let m=new Headers(u.headers?"function"==typeof u.headers?u.headers({loaderHeaders:d,parentHeaders:n,actionHeaders:c,errorHeaders:h?r:void 0}):u.headers:void 0);return f&&rz(r,m),rz(c,m),rz(d,m),rz(n,m),m},new Headers)}function rz(e,t){let r=e.get("Set-Cookie");if(r){let e=splitCookiesString(r),n=new Set(t.getSetCookie());e.forEach(e=>{n.has(e)||t.append("Set-Cookie",e)})}}var rH=new Set([...tB,304]);async function rF(e,t,r,n,a,o,i){try{let l=function(r){let a,o=rD(e,r);return eJ(r.statusCode)&&o.has("Location")?rW(n,e,t,{result:rB(r.statusCode,o,e.basename),headers:o,status:202}):(r.errors&&(Object.values(r.errors).forEach(e=>{(!q(e)||e.error)&&i(e)}),r.errors=r_(r.errors,t)),a=r.errors?{error:Object.values(r.errors)[0]}:{data:Object.values(r.actionData||{})[0]},rW(n,e,t,{result:a,headers:o,status:r.statusCode}))},s=new Request(a,{method:n.method,body:n.body,headers:n.headers,signal:n.signal,...n.body?{duplex:"half"}:void 0}),u=await r.query(s,{requestContext:o,skipLoaderErrorBubbling:!0,skipRevalidation:!0,unstable_respond:l});if(eq(u)||(u=l(u)),eY(u))return rW(n,e,t,{result:rB(u.status,u.headers,e.basename),headers:u.headers,status:202});return u}catch(r){return i(r),rW(n,e,t,{result:{error:r},headers:new Headers,status:500})}}async function rI(e,t,r,n,a,o,i){try{let l=function(r){let a=rD(e,r);if(eJ(r.statusCode)&&a.has("Location"))return rW(n,e,t,{result:{[tW]:rB(r.statusCode,a,e.basename)},headers:a,status:202});r.errors&&(Object.values(r.errors).forEach(e=>{(!q(e)||e.error)&&i(e)}),r.errors=r_(r.errors,t));let o={},l=new Set(r.matches.filter(e=>d?d.has(e.route.id):null!=e.route.loader).map(e=>e.route.id));if(r.errors)for(let[e,t]of Object.entries(r.errors))o[e]={error:t};for(let[e,t]of Object.entries(r.loaderData))!(e in o)&&l.has(e)&&(o[e]={data:t});return rW(n,e,t,{result:o,headers:a,status:r.statusCode})},s=new Request(a,{headers:n.headers,signal:n.signal}),u=new URL(n.url).searchParams.get("_routes"),d=u?new Set(u.split(",")):null,c=await r.query(s,{requestContext:o,filterMatchesToLoad:e=>!d||d.has(e.route.id),skipLoaderErrorBubbling:!0,unstable_respond:l});if(eq(c)||(c=l(c)),eY(c))return rW(n,e,t,{result:{[tW]:rB(c.status,c.headers,e.basename)},headers:c.headers,status:202});return c}catch(r){return i(r),rW(n,e,t,{result:{root:{error:r}},headers:new Headers,status:500})}}function rW(e,t,r,{result:n,headers:a,status:o}){let i=new Headers(a);return(i.set("X-Remix-Response","yes"),rH.has(o))?new Response(null,{status:o,headers:i}):(i.set("Content-Type","text/x-script"),new Response(rq(n,e.signal,t.entry.module.streamTimeout,r),{status:o||200,headers:i}))}function rB(e,t,r){let n=t.get("Location");return r&&(n=T(n,r)||n),{redirect:n,status:e,revalidate:t.has("X-Remix-Revalidate")||t.has("Set-Cookie"),reload:t.has("X-Remix-Reload-Document"),replace:t.has("X-Remix-Replace")}}function rq(e,t,r,n){let a=new AbortController,o=setTimeout(()=>a.abort(Error("Server Timeout")),"number"==typeof r?r:4950);return t.addEventListener("abort",()=>clearTimeout(o)),encode(e,{signal:a.signal,plugins:[e=>{if(e instanceof Error){let{name:t,message:r,stack:a}="production"===n?rL(e,n):e;return["SanitizedError",t,r,a]}if(e instanceof B){let{data:t,status:r,statusText:n}=e;return["ErrorResponse",t,r,n]}if(e&&"object"==typeof e&&tW in e)return["SingleFetchRedirect",e[tW]]}],postPlugins:[e=>{if(e&&"object"==typeof e)return["SingleFetchClassInstance",Object.fromEntries(Object.entries(e))]},()=>["SingleFetchFallback"]]})}function rJ(e,t){let r="Unexpected Server Error";return"production"!==t&&(r+=`

${String(e)}`),new Response(r,{status:500,headers:{"Content-Type":"text/plain"}})}function rY(e){return`__flash_${e}__`}}}]);