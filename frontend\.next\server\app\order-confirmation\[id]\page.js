(()=>{var e={};e.id=841,e.ids=[841],e.modules={3151:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\app\\\\order-confirmation\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\order-confirmation\\[id]\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6035:(e,t,s)=>{Promise.resolve().then(s.bind(s,3151))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13170:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(60687),a=s(43210),i=s(16189),n=s(85814),d=s.n(n);s(35421);let l=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});function o(){let e=(0,i.useParams)();(0,i.useRouter)(),parseInt(e.id);let[t,s]=(0,a.useState)(null),[n,o]=(0,a.useState)(!0),[c,x]=(0,a.useState)(null);if(n)return(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})});if(c)return(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:c})})]})}),(0,r.jsx)(d(),{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Go to Dashboard"})]});if(!t)return(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-yellow-700",children:"Order not found."})})]})}),(0,r.jsx)(d(),{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Go to Dashboard"})]});let m=new Date(t.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)(l,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Order Confirmed!"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"Thank you for your purchase. Your order has been received and is being processed."})]}),(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-8",children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Order Details"}),(0,r.jsxs)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:["Order #",t.id]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 px-4 py-5 sm:px-6",children:(0,r.jsxs)("dl",{className:"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[(0,r.jsxs)("div",{className:"sm:col-span-1",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Order Date"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:m})]}),(0,r.jsxs)("div",{className:"sm:col-span-1",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Order Status"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:t.status.charAt(0).toUpperCase()+t.status.slice(1)})})]}),(0,r.jsxs)("div",{className:"sm:col-span-1",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Payment Method"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:"credit_card"===t.payment_method?"Credit Card":"paypal"===t.payment_method?"PayPal":"Cash on Delivery"})]}),(0,r.jsxs)("div",{className:"sm:col-span-1",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Payment Status"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:(0,r.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"paid"===t.payment_status?"bg-green-100 text-green-800":"pending"===t.payment_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:t.payment_status.charAt(0).toUpperCase()+t.payment_status.slice(1)})})]}),(0,r.jsxs)("div",{className:"sm:col-span-2",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Shipping Address"}),(0,r.jsxs)("dd",{className:"mt-1 text-sm text-gray-900",children:[t.shipping_address,(0,r.jsx)("br",{}),t.shipping_city,", ",t.shipping_state," ",t.shipping_zip_code,(0,r.jsx)("br",{}),t.shipping_country,(0,r.jsx)("br",{}),"Phone: ",t.shipping_phone]})]}),t.notes&&(0,r.jsxs)("div",{className:"sm:col-span-2",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Notes"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:t.notes})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-8",children:[(0,r.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Order Items"})}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quantity"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subtotal"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.order_items?.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:e.product?.image?(0,r.jsx)("img",{className:"h-10 w-10 rounded-full object-cover",src:e.product.image,alt:e.product?.name}):(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-500 text-xs",children:"No img"})})}),(0,r.jsx)("div",{className:"ml-4",children:(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.product?.name})})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["$",Number(e.price).toFixed(2)]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.quantity})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["$",Number(e.subtotal).toFixed(2)]})})]},e.id))}),(0,r.jsx)("tfoot",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{colSpan:3,className:"px-6 py-4 text-right text-sm font-medium text-gray-900",children:"Total:"}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["$",Number(t.total_amount).toFixed(2)]})]})})]})})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(d(),{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Go to Dashboard"}),(0,r.jsx)(d(),{href:"/products",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Continue Shopping"})]})]})}},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42987:(e,t,s)=>{Promise.resolve().then(s.bind(s,13170))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},99396:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),d=s(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);s.d(t,l);let o={children:["",{children:["order-confirmation",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3151)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\order-confirmation\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\order-confirmation\\[id]\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/order-confirmation/[id]/page",pathname:"/order-confirmation/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,318,658,905],()=>s(99396));module.exports=r})();