"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[386],{722:(e,t,a)=>{a.d(t,{A:()=>o});let r=a(3464).A.create({baseURL:"",withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json",Accept:"application/json"}});r.interceptors.request.use(e=>{var t;console.log("Making request to: ".concat(null==(t=e.method)?void 0:t.toUpperCase()," ").concat(e.url));let a=localStorage.getItem("token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&419===e.response.status&&console.error("CSRF token mismatch. Refreshing token..."),Promise.reject(e)));let o=r},829:(e,t,a)=>{a.d(t,{A:()=>s});var r=a(2115),o=a(722);function s(){return(0,r.useEffect)(()=>{(async()=>{try{await o.A.get("/sanctum/csrf-cookie"),console.log("CSRF token fetched successfully")}catch(e){console.error("Failed to fetch CSRF token:",e)}})()},[]),null}},3265:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(5155),o=a(2115),s=a(6874),n=a.n(s),i=a(8258);let c=e=>{let{email:t,showDismiss:a=!1,onDismiss:s}=e,[c,l]=(0,o.useState)(!1),[d,u]=(0,o.useState)(!1),[g,m]=(0,o.useState)(null),[h,p]=(0,o.useState)(!1);if(h)return null;let f=async()=>{try{l(!0),m(null),await i.uR.resendVerificationEmail(),u(!0)}catch(e){m("Failed to resend verification email. Please try again.")}finally{l(!1)}};return(0,r.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3 flex-grow",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("p",{className:"text-sm text-yellow-700",children:["Your email address (",t,") has not been verified. Please check your email for a verification link."]}),a&&(0,r.jsx)("button",{onClick:()=>{p(!0),s&&s()},className:"ml-3 flex-shrink-0 text-yellow-500 hover:text-yellow-700",children:(0,r.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),g&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600",children:g}),d?(0,r.jsx)("p",{className:"mt-2 text-sm text-green-600",children:"A new verification link has been sent to your email address."}):(0,r.jsxs)("div",{className:"mt-2 flex flex-wrap gap-2",children:[(0,r.jsx)("button",{onClick:f,disabled:c,className:"inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 cursor-pointer",children:c?"Sending...":"Resend Verification Email"}),(0,r.jsx)(n(),{href:"/profile",className:"inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Go to My Account"})]})]})]})})}},8258:(e,t,a)=>{a.d(t,{uR:()=>s,mx:()=>u,M$:()=>i,QE:()=>c,bk:()=>n,d$:()=>g});var r=a(722);let o={login:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/login",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Login error details:",e),e}},register:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/register",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Registration error details:",e),e}},logout:async()=>{try{await r.A.get("/sanctum/csrf-cookie"),await r.A.post("/api/logout"),localStorage.removeItem("token"),localStorage.removeItem("user")}catch(e){console.error("Logout error:",e),localStorage.removeItem("token"),localStorage.removeItem("user")}},getCurrentUser:()=>{let e=localStorage.getItem("user");return e?JSON.parse(e):null},isAuthenticated:()=>!!localStorage.getItem("token"),isEmailVerified:()=>{let e=localStorage.getItem("user");return!!e&&!!JSON.parse(e).email_verified_at},resendVerificationEmail:async()=>{try{if(!o.isAuthenticated())throw Error("You must be logged in to resend verification email");console.log("Attempting to resend verification email...");let e=await r.A.get("/sanctum/csrf-cookie");console.log("CSRF cookie response:",e.status),console.log("Sending verification email request...");let t=await r.A.post("/api/email/verification-notification");return console.log("Verification email response:",t.status,t.data),t.data}catch(e){throw console.error("Resend verification email error:",e),e}},refreshUserData:async()=>{try{if(!o.isAuthenticated())return null;let e=await r.A.get("/api/user");if(console.log("Refresh user data response:",e.data),e.data&&e.data.success&&e.data.data&&e.data.data.user){let t=e.data.data.user;return localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),t}return null}catch(e){return console.error("Refresh user data error:",e),null}}},s=o,n={fetchProducts:async()=>{try{return(await r.A.get("/products")).data}catch(e){throw e}},getFeaturedProducts:async()=>{try{return(await r.A.get("/products?featured=true")).data}catch(e){throw e}},getProductBySlug:async e=>{try{let t=(await r.A.get("/products")).data.data.find(t=>t.slug===e);if(!t)throw Error("Product not found");return t}catch(e){throw e}},getAllProducts:async()=>{try{return(await r.A.get("/products")).data.data}catch(e){throw e}}},i={getAllCategories:async()=>{try{return(await r.A.get("/categories")).data.data}catch(e){throw e}},getCategoryBySlug:async e=>{try{let t=(await r.A.get("/categories")).data.data.find(t=>t.slug===e);if(!t)throw Error("Category not found");return t}catch(e){throw e}}},c={getAllOrders:async()=>{try{return(await r.A.get("/orders")).data.data}catch(e){throw e}},getOrderById:async e=>{try{return(await r.A.get("/orders/".concat(e))).data.data}catch(e){throw e}},createOrder:async e=>{try{return(await r.A.post("/orders",e)).data.data}catch(e){throw e}},updateOrder:async(e,t)=>{try{return(await r.A.put("/orders/".concat(e),{notes:t})).data.data}catch(e){throw e}},cancelOrder:async e=>{try{return(await r.A.post("/orders/".concat(e,"/cancel"))).data.data}catch(e){throw e}}},l="ecommerce_cart",d={getCart:()=>{let e=localStorage.getItem(l);return e?JSON.parse(e):{items:[],total:0}},saveCart:e=>{localStorage.setItem(l,JSON.stringify(e))},addToCart:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=d.getCart(),r=a.items.findIndex(t=>t.product_id===e.id);return -1!==r?a.items[r].quantity+=t:a.items.push({product_id:e.id,quantity:t,product:e}),a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(a),a},updateQuantity:(e,t)=>{let a=d.getCart(),r=a.items.findIndex(t=>t.product_id===e);return -1!==r&&(t<=0?a.items.splice(r,1):a.items[r].quantity=t,a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(a)),a},removeFromCart:e=>{let t=d.getCart(),a=t.items.findIndex(t=>t.product_id===e);return -1!==a&&(t.items.splice(a,1),t.total=t.items.reduce((e,t)=>e+t.product.price*t.quantity,0),d.saveCart(t)),t},clearCart:()=>{let e={items:[],total:0};return d.saveCart(e),e},getItemCount:()=>d.getCart().items.reduce((e,t)=>e+t.quantity,0)},u=d,g={createPaymentIntent:async e=>{try{return(await r.A.post("/payment/create-intent",e)).data}catch(e){throw e}},processPayment:async(e,t)=>{try{return(await r.A.post("/payment/process",{payment_method_id:e,items:t})).data}catch(e){throw e}}}}}]);