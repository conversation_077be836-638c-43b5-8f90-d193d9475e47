(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[841],{299:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(5155),s=a(2115),i=a(5695),n=a(6874),d=a.n(n),o=a(8258);let l=s.forwardRef(function(e,t){let{title:a,titleId:r,...i}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});function c(){var e;let t=(0,i.useParams)(),a=(0,i.useRouter)(),n=parseInt(t.id),[c,m]=(0,s.useState)(null),[u,x]=(0,s.useState)(!0),[h,p]=(0,s.useState)(null);if((0,s.useEffect)(()=>{if(!o.uR.isAuthenticated())return void a.push("/login");let e=async()=>{try{x(!0);let e=await o.QE.getOrderById(n);m(e),p(null)}catch(a){var e,t;console.error("Error fetching order:",a),p((null==(t=a.response)||null==(e=t.data)?void 0:e.message)||"Failed to load order. Please try again later.")}finally{x(!1)}};n&&e()},[n,a]),u)return(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})});if(h)return(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:h})})]})}),(0,r.jsx)(d(),{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Go to Dashboard"})]});if(!c)return(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-yellow-700",children:"Order not found."})})]})}),(0,r.jsx)(d(),{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Go to Dashboard"})]});let g=new Date(c.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)(l,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Order Confirmed!"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"Thank you for your purchase. Your order has been received and is being processed."})]}),(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-8",children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Order Details"}),(0,r.jsxs)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:["Order #",c.id]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 px-4 py-5 sm:px-6",children:(0,r.jsxs)("dl",{className:"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[(0,r.jsxs)("div",{className:"sm:col-span-1",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Order Date"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:g})]}),(0,r.jsxs)("div",{className:"sm:col-span-1",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Order Status"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:c.status.charAt(0).toUpperCase()+c.status.slice(1)})})]}),(0,r.jsxs)("div",{className:"sm:col-span-1",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Payment Method"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:"credit_card"===c.payment_method?"Credit Card":"paypal"===c.payment_method?"PayPal":"Cash on Delivery"})]}),(0,r.jsxs)("div",{className:"sm:col-span-1",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Payment Status"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("paid"===c.payment_status?"bg-green-100 text-green-800":"pending"===c.payment_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:c.payment_status.charAt(0).toUpperCase()+c.payment_status.slice(1)})})]}),(0,r.jsxs)("div",{className:"sm:col-span-2",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Shipping Address"}),(0,r.jsxs)("dd",{className:"mt-1 text-sm text-gray-900",children:[c.shipping_address,(0,r.jsx)("br",{}),c.shipping_city,", ",c.shipping_state," ",c.shipping_zip_code,(0,r.jsx)("br",{}),c.shipping_country,(0,r.jsx)("br",{}),"Phone: ",c.shipping_phone]})]}),c.notes&&(0,r.jsxs)("div",{className:"sm:col-span-2",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Notes"}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:c.notes})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-8",children:[(0,r.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Order Items"})}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quantity"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subtotal"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==(e=c.order_items)?void 0:e.map(e=>{var t,a,s;return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(null==(t=e.product)?void 0:t.image)?(0,r.jsx)("img",{className:"h-10 w-10 rounded-full object-cover",src:e.product.image,alt:null==(a=e.product)?void 0:a.name}):(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-500 text-xs",children:"No img"})})}),(0,r.jsx)("div",{className:"ml-4",children:(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:null==(s=e.product)?void 0:s.name})})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["$",Number(e.price).toFixed(2)]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.quantity})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["$",Number(e.subtotal).toFixed(2)]})})]},e.id)})}),(0,r.jsx)("tfoot",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{colSpan:3,className:"px-6 py-4 text-right text-sm font-medium text-gray-900",children:"Total:"}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["$",Number(c.total_amount).toFixed(2)]})]})})]})})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(d(),{href:"/dashboard",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Go to Dashboard"}),(0,r.jsx)(d(),{href:"/products",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Continue Shopping"})]})]})}},722:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(3464).A.create({baseURL:"",withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json",Accept:"application/json"}});r.interceptors.request.use(e=>{var t;console.log("Making request to: ".concat(null==(t=e.method)?void 0:t.toUpperCase()," ").concat(e.url));let a=localStorage.getItem("token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&419===e.response.status&&console.error("CSRF token mismatch. Refreshing token..."),Promise.reject(e)));let s=r},4229:(e,t,a)=>{Promise.resolve().then(a.bind(a,299))},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},8258:(e,t,a)=>{"use strict";a.d(t,{uR:()=>i,mx:()=>m,M$:()=>d,QE:()=>o,bk:()=>n,d$:()=>u});var r=a(722);let s={login:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/login",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Login error details:",e),e}},register:async e=>{try{await r.A.get("/sanctum/csrf-cookie");let t=await r.A.post("/api/register",e);if(t.data.success&&t.data.data){let{user:e,token:a}=t.data.data;return localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Registration error details:",e),e}},logout:async()=>{try{await r.A.get("/sanctum/csrf-cookie"),await r.A.post("/api/logout"),localStorage.removeItem("token"),localStorage.removeItem("user")}catch(e){console.error("Logout error:",e),localStorage.removeItem("token"),localStorage.removeItem("user")}},getCurrentUser:()=>{let e=localStorage.getItem("user");return e?JSON.parse(e):null},isAuthenticated:()=>!!localStorage.getItem("token"),isEmailVerified:()=>{let e=localStorage.getItem("user");return!!e&&!!JSON.parse(e).email_verified_at},resendVerificationEmail:async()=>{try{if(!s.isAuthenticated())throw Error("You must be logged in to resend verification email");console.log("Attempting to resend verification email...");let e=await r.A.get("/sanctum/csrf-cookie");console.log("CSRF cookie response:",e.status),console.log("Sending verification email request...");let t=await r.A.post("/api/email/verification-notification");return console.log("Verification email response:",t.status,t.data),t.data}catch(e){throw console.error("Resend verification email error:",e),e}},refreshUserData:async()=>{try{if(!s.isAuthenticated())return null;let e=await r.A.get("/api/user");if(console.log("Refresh user data response:",e.data),e.data&&e.data.success&&e.data.data&&e.data.data.user){let t=e.data.data.user;return localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),t}return null}catch(e){return console.error("Refresh user data error:",e),null}}},i=s,n={fetchProducts:async()=>{try{return(await r.A.get("/products")).data}catch(e){throw e}},getFeaturedProducts:async()=>{try{return(await r.A.get("/products?featured=true")).data}catch(e){throw e}},getProductBySlug:async e=>{try{let t=(await r.A.get("/products")).data.data.find(t=>t.slug===e);if(!t)throw Error("Product not found");return t}catch(e){throw e}},getAllProducts:async()=>{try{return(await r.A.get("/products")).data.data}catch(e){throw e}}},d={getAllCategories:async()=>{try{return(await r.A.get("/categories")).data.data}catch(e){throw e}},getCategoryBySlug:async e=>{try{let t=(await r.A.get("/categories")).data.data.find(t=>t.slug===e);if(!t)throw Error("Category not found");return t}catch(e){throw e}}},o={getAllOrders:async()=>{try{return(await r.A.get("/orders")).data.data}catch(e){throw e}},getOrderById:async e=>{try{return(await r.A.get("/orders/".concat(e))).data.data}catch(e){throw e}},createOrder:async e=>{try{return(await r.A.post("/orders",e)).data.data}catch(e){throw e}},updateOrder:async(e,t)=>{try{return(await r.A.put("/orders/".concat(e),{notes:t})).data.data}catch(e){throw e}},cancelOrder:async e=>{try{return(await r.A.post("/orders/".concat(e,"/cancel"))).data.data}catch(e){throw e}}},l="ecommerce_cart",c={getCart:()=>{let e=localStorage.getItem(l);return e?JSON.parse(e):{items:[],total:0}},saveCart:e=>{localStorage.setItem(l,JSON.stringify(e))},addToCart:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=c.getCart(),r=a.items.findIndex(t=>t.product_id===e.id);return -1!==r?a.items[r].quantity+=t:a.items.push({product_id:e.id,quantity:t,product:e}),a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),c.saveCart(a),a},updateQuantity:(e,t)=>{let a=c.getCart(),r=a.items.findIndex(t=>t.product_id===e);return -1!==r&&(t<=0?a.items.splice(r,1):a.items[r].quantity=t,a.total=a.items.reduce((e,t)=>e+t.product.price*t.quantity,0),c.saveCart(a)),a},removeFromCart:e=>{let t=c.getCart(),a=t.items.findIndex(t=>t.product_id===e);return -1!==a&&(t.items.splice(a,1),t.total=t.items.reduce((e,t)=>e+t.product.price*t.quantity,0),c.saveCart(t)),t},clearCart:()=>{let e={items:[],total:0};return c.saveCart(e),e},getItemCount:()=>c.getCart().items.reduce((e,t)=>e+t.quantity,0)},m=c,u={createPaymentIntent:async e=>{try{return(await r.A.post("/payment/create-intent",e)).data}catch(e){throw e}},processPayment:async(e,t)=>{try{return(await r.A.post("/payment/process",{payment_method_id:e,items:t})).data}catch(e){throw e}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,441,684,358],()=>t(4229)),_N_E=e.O()}]);