(()=>{var e={};e.id=316,e.ids=[316],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6294:(e,r,s)=>{Promise.resolve().then(s.bind(s,55402))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,r,s)=>{"use strict";var t=s(65773);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34772:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\app\\\\email-verified\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\email-verified\\page.tsx","default")},49410:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(r,o);let d={children:["",{children:["email-verified",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,34772)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\email-verified\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\email-verified\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/email-verified/page",pathname:"/email-verified",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55402:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>c});var t=s(60687),a=s(43210),i=s(16189),l=s(85814),n=s.n(l),o=s(35421);let d=()=>{let e=(0,i.useSearchParams)();(0,i.useRouter)();let[r,s]=(0,a.useState)(5),[l,d]=(0,a.useState)(!1),c=(0,a.useRef)(!1),u="true"===e.get("success"),m="true"===e.get("already");return(0,a.useEffect)(()=>{let e=async()=>{if(u||m){d(!0),console.log("Email verification successful, refreshing user data..."),await new Promise(e=>setTimeout(e,500));try{let r=3,s=null;for(;r>0&&!s;)try{s=await e(),console.log("User data refreshed successfully:",s);break}catch(e){if(--r>0)console.log(`Retrying user data refresh... (${r} attempts left)`),await new Promise(e=>setTimeout(e,1e3));else throw e}}catch(e){if(console.error("Failed to refresh user data:",e),u){let e=o.uR.getCurrentUser();if(e){console.log("Using fallback method to update verification status");let r={...e,email_verified_at:new Date().toISOString()};localStorage.setItem("user",JSON.stringify(r)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update"))}}}finally{d(!1)}}};e()},[u,m]),(0,a.useEffect)(()=>{if(!u&&!m)return;let e=setInterval(()=>{s(r=>r<=1?(clearInterval(e),c.current||(c.current=!0,window.location.href="/"),0):r-1)},1e3);return()=>clearInterval(e)},[u,m]),(0,t.jsxs)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,t.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Email Verification"})}),(0,t.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,t.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[u?(0,t.jsx)("div",{className:"bg-green-50 border-l-4 border-green-400 p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm text-green-700",children:"Your email has been verified successfully!"})})]})}):m?(0,t.jsx)("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Your email has already been verified."})})]})}):(0,t.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm text-red-700",children:"Invalid verification link. Please request a new verification link."})})]})}),l?(0,t.jsxs)("div",{className:"flex justify-center items-center py-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-500 mr-2"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Updating your account information..."})]}):(0,t.jsxs)("p",{className:"text-center text-sm text-gray-600",children:["You will be redirected to the home page in ",r," seconds."]}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(n(),{href:"/",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Go to Home Page"})})]})})]})},c=()=>(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,t.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,t.jsxs)("div",{className:"flex justify-center items-center py-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-500 mr-2"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Loading..."})]})})})}),children:(0,t.jsx)(d,{})})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93641:(e,r,s)=>{Promise.resolve().then(s.bind(s,34772))},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,318,658,905],()=>s(49410));module.exports=t})();