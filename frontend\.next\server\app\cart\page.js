(()=>{var e={};e.id=5,e.ids=[5],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49038:(e,t,r)=>{Promise.resolve().then(r.bind(r,70905))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70905:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\app\\\\cart\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\cart\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84101:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i),o=r(16189),d=r(35421);let c=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))});function l(){let[e,t]=(0,a.useState)({items:[],total:0}),[r,i]=(0,a.useState)(!0),l=(0,o.useRouter)(),m=(e,r)=>{t(d.mx.updateQuantity(e,r)),window.dispatchEvent(new Event("storage"))},u=e=>{t(d.mx.removeFromCart(e)),window.dispatchEvent(new Event("storage"))};return r?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):0===e.items.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Your Cart"}),(0,s.jsx)("p",{className:"text-gray-500 mb-8",children:"Your cart is empty."}),(0,s.jsx)(n(),{href:"/products",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Continue Shopping"})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Your Cart"}),(0,s.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-6",children:(0,s.jsx)("div",{className:"border-t border-gray-200",children:(0,s.jsx)("dl",{children:e.items.map(e=>(0,s.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-6 sm:gap-4 sm:px-6 border-b border-gray-200",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500 sm:col-span-2",children:(0,s.jsxs)("div",{className:"flex items-center",children:[e.product.image?(0,s.jsx)("img",{src:e.product.image,alt:e.product.name,className:"h-16 w-16 object-cover rounded mr-4"}):(0,s.jsx)("div",{className:"h-16 w-16 bg-gray-100 rounded mr-4 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-gray-400",children:"No image"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.product.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["$",e.product.price.toFixed(2)," each"]})]})]})}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("button",{onClick:()=>m(e.product_id,e.quantity-1),className:"p-1 rounded-md border border-gray-300 text-gray-700",disabled:e.quantity<=1,children:"-"}),(0,s.jsx)("span",{className:"mx-2",children:e.quantity}),(0,s.jsx)("button",{onClick:()=>m(e.product_id,e.quantity+1),className:"p-1 rounded-md border border-gray-300 text-gray-700",disabled:e.quantity>=e.product.quantity,children:"+"})]})}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:(0,s.jsx)("div",{className:"text-right",children:(0,s.jsxs)("p",{className:"text-lg font-medium text-gray-900",children:["$",(e.product.price*e.quantity).toFixed(2)]})})}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1",children:(0,s.jsx)("div",{className:"text-right",children:(0,s.jsx)("button",{onClick:()=>u(e.product_id),className:"text-red-600 hover:text-red-900",children:(0,s.jsx)(c,{className:"h-5 w-5"})})})})]},e.product_id))})})}),(0,s.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg mb-6",children:(0,s.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Order Summary"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["$",e.total.toFixed(2)]})]})})}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(n(),{href:"/products",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Continue Shopping"}),(0,s.jsx)("button",{onClick:()=>{if(!d.uR.isAuthenticated())return void l.push("/login");l.push("/checkout")},className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Proceed to Checkout"})]})]})}},88790:(e,t,r)=>{Promise.resolve().then(r.bind(r,84101))},94735:e=>{"use strict";e.exports=require("events")},99082:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>l,routeModule:()=>u,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let c={children:["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,70905)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\cart\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\cart\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,318,658,905],()=>r(99082));module.exports=s})();