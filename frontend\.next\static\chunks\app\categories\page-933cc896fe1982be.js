(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[379],{3408:(e,l,s)=>{Promise.resolve().then(s.bind(s,9137))},9137:(e,l,s)=>{"use strict";s.r(l),s.d(l,{default:()=>d});var a=s(5155),r=s(2115),t=s(8258),i=s(9768);function d(){let[e,l]=(0,r.useState)([]),[s,d]=(0,r.useState)(!0),[c,n]=(0,r.useState)(null);return((0,r.useEffect)(()=>{(async()=>{try{d(!0);let e=await t.M$.getAllCategories();l(e.data),n(null)}catch(e){console.error("Error fetching categories:",e),n("Failed to load categories. Please try again later.")}finally{d(!1)}})()},[]),s)?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):c?(0,a.jsx)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 my-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:c})})]})}):(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"All Categories"}),e.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3",children:e.map(e=>(0,a.jsx)(i.rm,{category:e},e.id))}):(0,a.jsx)("p",{className:"text-gray-500",children:"No categories available."})]})}}},e=>{var l=l=>e(e.s=l);e.O(0,[464,874,855,386,768,441,684,358],()=>l(3408)),_N_E=e.O()}]);