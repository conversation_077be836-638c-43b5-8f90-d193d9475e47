{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/categories/[slug]", "regex": "^/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/categories/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/order-confirmation/[id]", "regex": "^/order\\-confirmation/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/order\\-confirmation/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/products/[slug]", "regex": "^/products/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/products/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/cart", "regex": "^/cart(?:/)?$", "routeKeys": {}, "namedRegex": "^/cart(?:/)?$"}, {"page": "/categories", "regex": "^/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/categories(?:/)?$"}, {"page": "/checkout", "regex": "^/checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/checkout(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/email-verified", "regex": "^/email\\-verified(?:/)?$", "routeKeys": {}, "namedRegex": "^/email\\-verified(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/products", "regex": "^/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/products(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/api/:path*", "destination": "http://127.0.0.1:8000/api/:path*", "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/sanctum/:path*", "destination": "http://127.0.0.1:8000/sanctum/:path*", "regex": "^/sanctum(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}