exports.id=905,exports.ids=[905],exports.modules={14329:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(60687),s=r(43210),o=r(85814),n=r.n(o),i=r(35421);let l=({email:e,showDismiss:t=!1,onDismiss:r})=>{let[o,l]=(0,s.useState)(!1),[d,c]=(0,s.useState)(!1),[m,u]=(0,s.useState)(null),[h,g]=(0,s.useState)(!1);if(h)return null;let x=async()=>{try{l(!0),u(null),await i.uR.resendVerificationEmail(),c(!0)}catch(e){u("Failed to resend verification email. Please try again.")}finally{l(!1)}};return(0,a.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-grow",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-yellow-700",children:["Your email address (",e,") has not been verified. Please check your email for a verification link."]}),t&&(0,a.jsx)("button",{onClick:()=>{g(!0),r&&r()},className:"ml-3 flex-shrink-0 text-yellow-500 hover:text-yellow-700",children:(0,a.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),m&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600",children:m}),d?(0,a.jsx)("p",{className:"mt-2 text-sm text-green-600",children:"A new verification link has been sent to your email address."}):(0,a.jsxs)("div",{className:"mt-2 flex flex-wrap gap-2",children:[(0,a.jsx)("button",{onClick:x,disabled:o,className:"inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 cursor-pointer",children:o?"Sending...":"Resend Verification Email"}),(0,a.jsx)(n(),{href:"/profile",className:"inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Go to My Account"})]})]})]})})}},25807:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},28184:(e,t,r)=>{Promise.resolve().then(r.bind(r,90197))},31309:(e,t,r)=>{"use strict";r.d(t,{eq:()=>o,nO:()=>s});var a=r(35421);let s=async()=>{try{console.log("Refreshing user data from backend...");let e=await a.uR.refreshUserData();return console.log("User data refreshed successfully:",e),e}catch(e){return console.error("Failed to refresh user data:",e),a.uR.getCurrentUser()}},o=e=>(window.addEventListener("storage",e),window.addEventListener("storage-update",e),()=>{window.removeEventListener("storage",e),window.removeEventListener("storage-update",e)})},34055:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},35421:(e,t,r)=>{"use strict";r.d(t,{uR:()=>o,mx:()=>m,M$:()=>i,QE:()=>l,bk:()=>n,d$:()=>u});var a=r(64298);let s={login:async e=>{try{await a.A.get("/sanctum/csrf-cookie");let t=await a.A.post("/api/login",e);if(t.data.success&&t.data.data){let{user:e,token:r}=t.data.data;return localStorage.setItem("token",r),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Login error details:",e),e}},register:async e=>{try{await a.A.get("/sanctum/csrf-cookie");let t=await a.A.post("/api/register",e);if(t.data.success&&t.data.data){let{user:e,token:r}=t.data.data;return localStorage.setItem("token",r),localStorage.setItem("user",JSON.stringify(e)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),e}throw Error(t.data.message)}catch(e){throw console.error("Registration error details:",e),e}},logout:async()=>{try{await a.A.get("/sanctum/csrf-cookie"),await a.A.post("/api/logout"),localStorage.removeItem("token"),localStorage.removeItem("user")}catch(e){console.error("Logout error:",e),localStorage.removeItem("token"),localStorage.removeItem("user")}},getCurrentUser:()=>{let e=localStorage.getItem("user");return e?JSON.parse(e):null},isAuthenticated:()=>!!localStorage.getItem("token"),isEmailVerified:()=>{let e=localStorage.getItem("user");return!!e&&!!JSON.parse(e).email_verified_at},resendVerificationEmail:async()=>{try{if(!s.isAuthenticated())throw Error("You must be logged in to resend verification email");console.log("Attempting to resend verification email...");let e=await a.A.get("/sanctum/csrf-cookie");console.log("CSRF cookie response:",e.status),console.log("Sending verification email request...");let t=await a.A.post("/api/email/verification-notification");return console.log("Verification email response:",t.status,t.data),t.data}catch(e){throw console.error("Resend verification email error:",e),e}},refreshUserData:async()=>{try{if(!s.isAuthenticated())return null;let e=await a.A.get("/api/user");if(console.log("Refresh user data response:",e.data),e.data&&e.data.success&&e.data.data&&e.data.data.user){let t=e.data.data.user;return localStorage.setItem("user",JSON.stringify(t)),window.dispatchEvent(new Event("storage")),window.dispatchEvent(new Event("storage-update")),t}return null}catch(e){return console.error("Refresh user data error:",e),null}}},o=s,n={fetchProducts:async()=>{try{return(await a.A.get("/products")).data}catch(e){throw e}},getFeaturedProducts:async()=>{try{return(await a.A.get("/products?featured=true")).data}catch(e){throw e}},getProductBySlug:async e=>{try{let t=(await a.A.get("/products")).data.data.find(t=>t.slug===e);if(!t)throw Error("Product not found");return t}catch(e){throw e}},getAllProducts:async()=>{try{return(await a.A.get("/products")).data.data}catch(e){throw e}}},i={getAllCategories:async()=>{try{return(await a.A.get("/categories")).data.data}catch(e){throw e}},getCategoryBySlug:async e=>{try{let t=(await a.A.get("/categories")).data.data.find(t=>t.slug===e);if(!t)throw Error("Category not found");return t}catch(e){throw e}}},l={getAllOrders:async()=>{try{return(await a.A.get("/orders")).data.data}catch(e){throw e}},getOrderById:async e=>{try{return(await a.A.get(`/orders/${e}`)).data.data}catch(e){throw e}},createOrder:async e=>{try{return(await a.A.post("/orders",e)).data.data}catch(e){throw e}},updateOrder:async(e,t)=>{try{return(await a.A.put(`/orders/${e}`,{notes:t})).data.data}catch(e){throw e}},cancelOrder:async e=>{try{return(await a.A.post(`/orders/${e}/cancel`)).data.data}catch(e){throw e}}},d="ecommerce_cart",c={getCart:()=>{let e=localStorage.getItem(d);return e?JSON.parse(e):{items:[],total:0}},saveCart:e=>{localStorage.setItem(d,JSON.stringify(e))},addToCart:(e,t=1)=>{let r=c.getCart(),a=r.items.findIndex(t=>t.product_id===e.id);return -1!==a?r.items[a].quantity+=t:r.items.push({product_id:e.id,quantity:t,product:e}),r.total=r.items.reduce((e,t)=>e+t.product.price*t.quantity,0),c.saveCart(r),r},updateQuantity:(e,t)=>{let r=c.getCart(),a=r.items.findIndex(t=>t.product_id===e);return -1!==a&&(t<=0?r.items.splice(a,1):r.items[a].quantity=t,r.total=r.items.reduce((e,t)=>e+t.product.price*t.quantity,0),c.saveCart(r)),r},removeFromCart:e=>{let t=c.getCart(),r=t.items.findIndex(t=>t.product_id===e);return -1!==r&&(t.items.splice(r,1),t.total=t.items.reduce((e,t)=>e+t.product.price*t.quantity,0),c.saveCart(t)),t},clearCart:()=>{let e={items:[],total:0};return c.saveCart(e),e},getItemCount:()=>c.getCart().items.reduce((e,t)=>e+t.quantity,0)},m=c,u={createPaymentIntent:async e=>{try{return(await a.A.post("/payment/create-intent",e)).data}catch(e){throw e}},processPayment:async(e,t)=>{try{return(await a.A.post("/payment/process",{payment_method_id:e,items:t})).data}catch(e){throw e}}}},37912:(e,t,r)=>{Promise.resolve().then(r.bind(r,85355))},61135:()=>{},64298:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let a=r(51060).A.create({baseURL:"",withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json",Accept:"application/json"}});a.interceptors.request.use(e=>{console.log(`Making request to: ${e.method?.toUpperCase()} ${e.url}`);let t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&419===e.response.status&&console.error("CSRF token mismatch. Refreshing token..."),Promise.reject(e)));let s=a},68399:(e,t,r)=>{"use strict";function a(){return null}r.d(t,{A:()=>a}),r(43210),r(64298)},85355:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var a=r(60687),s=r(43210),o=r(85814),n=r.n(o),i=r(79857),l=r(64908),d=r(81836),c=r(6510),m=r(35421),u=r(68399),h=r(14329);function g({children:e}){let[t,r]=(0,s.useState)(!1),[o,g]=(0,s.useState)(!1),[x,p]=(0,s.useState)(0),[f,y]=(0,s.useState)(!1),[b,v]=(0,s.useState)(null),w=async()=>{await m.uR.logout(),g(!1),window.location.href="/login"};return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(u.A,{}),(0,a.jsxs)("header",{className:"bg-white shadow",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)(n(),{href:"/",className:"text-2xl font-bold text-indigo-600",children:"EcommerceApp"})}),(0,a.jsxs)("nav",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[(0,a.jsx)(n(),{href:"/",className:"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Home"}),(0,a.jsx)(n(),{href:"/products",className:"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Products"}),(0,a.jsx)(n(),{href:"/categories",className:"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Categories"})]})]}),(0,a.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4",children:[(0,a.jsxs)(n(),{href:"/cart",className:"relative p-1 rounded-full text-gray-400 hover:text-gray-500",children:[(0,a.jsx)(i.A,{className:"h-6 w-6","aria-hidden":"true"}),x>0&&(0,a.jsx)("span",{className:"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full",children:x})]}),o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(n(),{href:"/profile",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50",children:[(0,a.jsx)(l.A,{className:"h-5 w-5 mr-2","aria-hidden":"true"}),"My Account"]}),(0,a.jsx)("button",{onClick:w,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 cursor-pointer",children:"Logout"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n(),{href:"/login",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50",children:"Login"}),(0,a.jsx)(n(),{href:"/register",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",children:"Register"})]})]}),(0,a.jsx)("div",{className:"-mr-2 flex items-center sm:hidden",children:(0,a.jsxs)("button",{onClick:()=>r(!t),className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 cursor-pointer",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open main menu"}),t?(0,a.jsx)(d.A,{className:"block h-6 w-6","aria-hidden":"true"}):(0,a.jsx)(c.A,{className:"block h-6 w-6","aria-hidden":"true"})]})})]})}),t&&(0,a.jsx)("div",{className:"sm:hidden",children:(0,a.jsxs)("div",{className:"pt-2 pb-3 space-y-1",children:[(0,a.jsx)(n(),{href:"/",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>r(!1),children:"Home"}),(0,a.jsx)(n(),{href:"/products",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>r(!1),children:"Products"}),(0,a.jsx)(n(),{href:"/categories",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>r(!1),children:"Categories"}),(0,a.jsxs)(n(),{href:"/cart",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>r(!1),children:["Cart (",x,")"]}),o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n(),{href:"/profile",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>r(!1),children:"My Account"}),(0,a.jsx)("button",{onClick:()=>{w(),r(!1)},className:"block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 cursor-pointer",children:"Logout"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n(),{href:"/login",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>r(!1),children:"Login"}),(0,a.jsx)(n(),{href:"/register",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",onClick:()=>r(!1),children:"Register"})]})]})})]}),(0,a.jsx)("main",{className:"flex-grow",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[f&&b&&(0,a.jsx)(h.A,{email:b.email,showDismiss:!0,onDismiss:()=>y(!1)}),e]})}),(0,a.jsx)("footer",{className:"bg-white",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("p",{className:"text-center text-gray-500 text-sm",children:["\xa9 ",new Date().getFullYear()," EcommerceApp. All rights reserved."]})})})]})}r(31309)},90197:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\ecommerce\\\\frontend\\\\src\\\\app\\\\client-layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\ecommerce\\frontend\\src\\app\\client-layout.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var a=r(37413),s=r(22376),o=r.n(s),n=r(68726),i=r.n(n);r(61135);var l=r(90197);let d={title:"E-commerce App",description:"E-commerce application with Laravel and Next.js"};function c({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:`${o().variable} ${i().variable} antialiased`,children:(0,a.jsx)(l.default,{children:e})})})}}};